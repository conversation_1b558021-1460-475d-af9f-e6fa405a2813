<template>
  <div class="card-video-monitor-modal">
    <!-- 工具栏 -->
    <div class="monitor-toolbar mb-4">
      <div class="flex justify-between items-center">
        <div class="toolbar-left">
          <h4 class="text-lg font-semibold text-gray-800">巡更点视频监控 ({{ videoList.length }})</h4>
          <a-tag color="blue">{{ cardInfo?.name || '巡更点' }}</a-tag>
        </div>

      </div>
    </div>

    <!-- 视频网格 -->
    <div class="video-grid" v-if="videoList.length > 0">
      <div
          v-for="(video, index) in videoList"
          :key="video.id || index"
          class="video-grid-item"
      >
        <VideoMonitorPlayerModal
            :ref="el => setVideoPlayerRef(el, index)"
            :plan-id="cardInfo?.id || ''"
            :video-info="video"
            :auto-start="false"
            :show-controls="true"
            @error="onVideoError(index, $event)"
        />
      </div>
    </div>

    <!-- 无视频时的空状态 -->
    <div v-else class="empty-state">
      <div class="empty-content">
        <Icon icon="mdi:video-off" :size="80" class="text-gray-400 mb-4"></Icon>
        <p class="text-gray-500 text-lg">该巡更点暂无视频监控</p>
      </div>
    </div>

  </div>
</template>

<script lang="ts" setup>
import { ref, onUnmounted, defineExpose } from 'vue';
import { Icon } from '/@/components/Icon';
import VideoMonitorPlayerModal from './VideoMonitorPlayerModal.vue';

interface VideoInfo {
  id: string;
  name: string;
  videoUrl: string;
  streamId: string;
  websocketUrl: string;
  cameraIndexCode?: string;
  streamType?: string;
}

interface CardInfo {
  id: string;
  name: string;
}

interface Props {
  cardInfo: CardInfo;
  videoList: VideoInfo[];
}

const props = defineProps<Props>();

// 视频播放器引用
const videoPlayerRefs = ref<any[]>([]);

const setVideoPlayerRef = (el: any, index: number) => {
  if (el) {
    videoPlayerRefs.value[index] = el;
  }
};

const onVideoError = (index: number, error: any) => {
  console.error(`视频 ${index} 播放错误:`, error);
  // 可以在这里添加错误处理逻辑
};

// 清理所有视频
const cleanupAllVideos = () => {
  videoPlayerRefs.value.forEach((player, index) => {
    if (player && typeof player.cleanup === 'function') {
      try {
        player.cleanup();
      } catch (error) {
        console.error(`清理视频播放器 ${index} 时出错:`, error);
      }
    }
  });
  videoPlayerRefs.value = [];
};

// 停止所有视频
const stopAllVideos = () => {
  videoPlayerRefs.value.forEach((player, index) => {
    if (player && typeof player.stop === 'function') {
      try {
        player.stop();
      } catch (error) {
        console.error(`停止视频播放器 ${index} 时出错:`, error);
      }
    }
  });
};

// 组件卸载时清理
onUnmounted(() => {
  cleanupAllVideos();
});

// 暴露方法
defineExpose({
  cleanupAllVideos,
  stopAllVideos
});
</script>

<style scoped lang="less">
.card-video-monitor-modal {
  width: 100%;
  height: auto;
  min-height: 600px;
  display: flex;
  flex-direction: column;

  .monitor-toolbar {
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 8px;
    border: 1px solid rgba(59, 130, 246, 0.1);
    flex-shrink: 0;
  }

  .video-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
    min-height: 500px;
    height: auto;
    flex: 1;
  }

  .video-grid-item {
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid rgba(59, 130, 246, 0.2);
    transition: all 0.3s ease;
    min-height: 400px;
    height: auto;
    display: flex;
    flex-direction: column;

    // 确保子组件填充整个容器
    :deep(.video-monitor-player-modal) {
      width: 100%;
      height: 100%;
      flex: 1;
    }

    &:hover {
      border-color: rgba(59, 130, 246, 0.4);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
    }
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border: 2px dashed #cbd5e1;
    border-radius: 8px;
    min-height: 450px;
    margin: 16px;

    .empty-content {
      text-align: center;
      padding: 40px;

      .text-lg {
        font-size: 1.125rem;
        font-weight: 500;
        margin-top: 12px;
      }
    }
  }
}
</style>
