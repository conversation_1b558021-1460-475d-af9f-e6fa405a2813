import { h } from 'vue';

/**
 * 表格文字换行渲染函数
 * @param text 要显示的文字
 * @param options 配置选项
 */
export interface TextRenderOptions {
  /** 最大行数，默认3行 */
  maxLines?: number;
  /** 最大高度，默认60px */
  maxHeight?: string;
  /** 行高，默认1.4 */
  lineHeight?: string | number;
  /** 内边距，默认'4px 0' */
  padding?: string;
  /** 文字对齐方式，默认'left' */
  textAlign?: 'left' | 'center' | 'right';
  /** 是否显示title提示，默认true */
  showTitle?: boolean;
  /** 自定义样式 */
  customStyle?: Record<string, any>;
}

/**
 * 渲染支持换行的文字
 */
export function renderWrappedText(text: string | null | undefined, options: TextRenderOptions = {}) {
  const {
    maxLines = 3,
    maxHeight = '60px',
    lineHeight = 1.4,
    padding = '4px 0',
    textAlign = 'left',
    showTitle = true,
    customStyle = {}
  } = options;

  const displayText = text || '';

  const defaultStyle = {
    wordBreak: 'break-word',
    whiteSpace: 'normal',
    lineHeight: lineHeight,
    padding: padding,
    maxHeight: maxHeight,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: '-webkit-box',
    WebkitLineClamp: maxLines,
    WebkitBoxOrient: 'vertical',
    textAlign: textAlign,
    ...customStyle
  };

  return h('div', {
    style: defaultStyle,
    title: showTitle ? displayText : undefined
  }, displayText);
}

/**
 * 渲染单行省略文字
 */
export function renderEllipsisText(text: string | null | undefined, options: Omit<TextRenderOptions, 'maxLines'> = {}) {
  const {
    maxHeight = '32px',
    lineHeight = 1.4,
    padding = '4px 0',
    textAlign = 'left',
    showTitle = true,
    customStyle = {}
  } = options;

  const displayText = text || '';

  const defaultStyle = {
    wordBreak: 'break-word',
    whiteSpace: 'nowrap',
    lineHeight: lineHeight,
    padding: padding,
    maxHeight: maxHeight,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    textAlign: textAlign,
    ...customStyle
  };

  return h('div', {
    style: defaultStyle,
    title: showTitle ? displayText : undefined
  }, displayText);
}

/**
 * 表格列配置的快捷方法
 */
export function createWrappedColumn(title: string, dataIndex: string, width: number, options: TextRenderOptions = {}) {
  return {
    title,
    align: "center" as const,
    dataIndex,
    width,
    ellipsis: true,
    customRender: ({ text }: { text: string }) => renderWrappedText(text, options)
  };
}

/**
 * 表格列配置的快捷方法（单行省略）
 */
export function createEllipsisColumn(title: string, dataIndex: string, width: number, options: Omit<TextRenderOptions, 'maxLines'> = {}) {
  return {
    title,
    align: "center" as const,
    dataIndex,
    width,
    ellipsis: true,
    customRender: ({ text }: { text: string }) => renderEllipsisText(text, options)
  };
}
