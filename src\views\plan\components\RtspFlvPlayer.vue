<template>
  <div class="rtsp-flv-player-container" :style="containerStyle">
    <div class="video-wrapper">
      <video
          ref="videoElement"
          class="video-player"
          :controls="showControls"
          :muted="muted"
          :autoplay="false"
          :poster="poster"
          @loadstart="onLoadStart"
          @loadedmetadata="onLoadedMetadata"
          @canplay="onCanPlay"
          @play="onPlay"
          @pause="onPause"
          @ended="onEnded"
          @error="onError"
          @waiting="onWaiting"
          @playing="onPlaying"
      >
        您的浏览器不支持视频播放
      </video>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-overlay">
        <a-spin size="large" />
        <div class="loading-text">{{ loadingText }}</div>
        <div v-if="isRetrying" class="retry-info">
          重试次数: {{ retryCount }}/{{ maxRetryCount }}
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-if="error && !loading" class="error-overlay">
        <div class="error-content">
          <ExclamationCircleOutlined class="error-icon" />
          <div class="error-text">{{ errorMessage }}</div>
          <div class="error-actions">
            <a-button type="primary" @click="retry" :loading="loading">
              <ReloadOutlined /> 重试
            </a-button>
            <a-button v-if="retryCount > 0" @click="resetRetry">
              重置
            </a-button>
          </div>
          <div v-if="retryCount > 0" class="retry-count">
            已重试 {{ retryCount }} 次
          </div>
        </div>
      </div>

      <!-- 播放按钮覆盖层 -->
      <div v-if="!isPlaying && !loading && !error" class="play-overlay" @click="startPlay">
        <div class="play-button">
          <PlayCircleOutlined />
          <span class="play-text">点击播放</span>
        </div>
      </div>

      <!-- 连接状态指示器 -->
      <div v-if="showConnectionStatus" class="connection-status" :class="connectionStatusClass">
        <div class="status-dot"></div>
        <span class="status-text">{{ connectionStatusText }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import {
  PlayCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue';

import FlvJs from 'flv.js';

// 直接导入已安装的flv.js
const flvjs = FlvJs

interface Props {
  rtspUrl: string; // RTSP视频源地址
  streamId?: string; // 流ID
  autoplay?: boolean; // 自动播放
  muted?: boolean; // 静音
  showControls?: boolean; // 显示原生控制栏
  poster?: string; // 封面图
  width?: string | number; // 宽度
  height?: string | number; // 高度
  enableRetry?: boolean; // 是否启用自动重试
  maxRetryCount?: number; // 最大重试次数
  showConnectionStatus?: boolean; // 显示连接状态
}

const props = withDefaults(defineProps<Props>(), {
  streamId: '',
  autoplay: false,
  muted: false,
  showControls: true,
  width: '100%',
  height: 'auto',
  enableRetry: true,
  maxRetryCount: 3,
  showConnectionStatus: true
});

const emit = defineEmits<{
  play: [];
  pause: [];
  ended: [];
  error: [error: any];
  loadstart: [];
  loadedmetadata: [];
  canplay: [];
  retry: [count: number];
  connected: [];
  disconnected: [];
}>();

// 响应式数据
const videoElement = ref<HTMLVideoElement>();
const loading = ref(false);
const loadingText = ref('正在初始化...');
const error = ref(false);
const errorMessage = ref('');
const isPlaying = ref(false);
const retryCount = ref(0);
const isRetrying = ref(false);
const flvSupported = ref(false);
const connectionStatus = ref<'disconnected' | 'connecting' | 'connected'>('disconnected');

// FLV播放器实例
let flvPlayer: any = null;
let retryTimer: any = null;

// 计算属性
const containerStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height
}));

const connectionStatusClass = computed(() => ({
  'status-connected': connectionStatus.value === 'connected',
  'status-connecting': connectionStatus.value === 'connecting',
  'status-disconnected': connectionStatus.value === 'disconnected'
}));

const connectionStatusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected': return '已连接';
    case 'connecting': return '连接中';
    case 'disconnected': return '未连接';
    default: return '未知';
  }
});

// 计算FLV URL - 优化版本，支持流参数和认证
const flvUrl = computed(() => {
  if (!props.streamId) return '';

  // 支持多种URL格式
  const baseUrl = window.location.origin;
  const contextPath = window.location.pathname.split('/')[1] || 'jeecgboot';

  // 构建基础URL
  let url = `${baseUrl}/${contextPath}/video/flv/${props.streamId}`;

  // 添加必要的查询参数来确保获取真正的流
  const params = new URLSearchParams();

  // 添加时间戳防止缓存
  params.append('t', Date.now().toString());

  // 添加流类型参数
  params.append('type', 'live');

  // 如果有RTSP URL，添加为参数
  if (props.rtspUrl) {
    params.append('rtsp', encodeURIComponent(props.rtspUrl));
  }

  // 添加格式参数
  params.append('format', 'flv');

  // 添加实时流标识
  params.append('live', '1');

  // 添加认证token（如果存在）
  const token = localStorage.getItem('token') || sessionStorage.getItem('token');
  if (token) {
    params.append('token', token);
  }

  // 组合最终URL
  const finalUrl = `${url}?${params.toString()}`;
  console.log('构建的FLV流URL:', finalUrl);

  return finalUrl;
});

// 初始化flv支持状态
flvSupported.value = flvjs && flvjs.isSupported();

/**
 * 验证流是否可用 - 增强版本
 */
const validateStream = async (url: string): Promise<boolean> => {
  try {
    console.log('验证流可用性:', url);

    // 首先尝试HEAD请求
    let response = await fetch(url, {
      method: 'HEAD',
      headers: {
        'Accept': 'video/x-flv, application/octet-stream, */*',
        'Cache-Control': 'no-cache',
        'Range': 'bytes=0-1023' // 请求前1KB数据
      }
    });

    console.log('HEAD请求响应:', {
      status: response.status,
      statusText: response.statusText,
      contentType: response.headers.get('content-type'),
      contentLength: response.headers.get('content-length'),
      acceptRanges: response.headers.get('accept-ranges')
    });

    // 如果HEAD请求失败，尝试GET请求获取少量数据
    if (!response.ok) {
      console.log('HEAD请求失败，尝试GET请求验证');

      response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'video/x-flv, application/octet-stream, */*',
          'Cache-Control': 'no-cache',
          'Range': 'bytes=0-1023'
        }
      });

      console.log('GET请求响应:', {
        status: response.status,
        statusText: response.statusText,
        contentType: response.headers.get('content-type')
      });

      // 如果是206 Partial Content或200 OK，说明流可用
      if (response.status === 206 || response.status === 200) {
        // 尝试读取一小部分数据验证格式
        try {
          const buffer = await response.arrayBuffer();
          console.log('获取到数据，大小:', buffer.byteLength);

          // 检查是否是FLV格式（FLV文件头是'FLV'）
          const view = new Uint8Array(buffer);
          if (view.length >= 3) {
            const header = String.fromCharCode(view[0], view[1], view[2]);
            console.log('数据头部:', header);
            return header === 'FLV' || buffer.byteLength > 0;
          }

          return buffer.byteLength > 0;
        } catch (dataError) {
          console.error('读取流数据失败:', dataError);
          return response.ok;
        }
      }
    }

    return response.ok;
  } catch (error) {
    console.error('流验证失败:', error);
    return false;
  }
};

/**
 * 启动流转换服务
 */
const startStreamConversion = async (): Promise<boolean> => {
  try {
    const baseUrl = window.location.origin;
    const contextPath = window.location.pathname.split('/')[1] || 'jeecgboot';

    // 构建启动流转换的API URL
    const startUrl = `${baseUrl}/${contextPath}/video/start/${props.streamId}`;

    console.log('启动流转换服务:', startUrl);

    const response = await fetch(startUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        streamId: props.streamId,
        rtspUrl: props.rtspUrl,
        format: 'flv',
        live: true
      })
    });

    if (!response.ok) {
      throw new Error(`启动流转换失败: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('流转换启动结果:', result);

    return result.success !== false;
  } catch (error) {
    console.error('启动流转换服务失败:', error);
    return false;
  }
};

/**
 * 等待流准备就绪
 */
const waitForStreamReady = async (maxWaitTime = 10000): Promise<boolean> => {
  const startTime = Date.now();
  const checkInterval = 1000; // 每秒检查一次

  while (Date.now() - startTime < maxWaitTime) {
    const isReady = await validateStream(flvUrl.value);
    if (isReady) {
      console.log('流已准备就绪');
      return true;
    }

    console.log('流尚未准备就绪，等待中...');
    await new Promise(resolve => setTimeout(resolve, checkInterval));
  }

  console.error('等待流准备超时');
  return false;
};

/**
 * 重试连接
 */
const retryConnection = async () => {
  if (!props.enableRetry || retryCount.value >= props.maxRetryCount) {
    return;
  }

  retryCount.value++;
  isRetrying.value = true;
  loadingText.value = `正在重试连接 (${retryCount.value}/${props.maxRetryCount})...`;

  emit('retry', retryCount.value);

  // 等待一段时间后重试
  await new Promise(resolve => setTimeout(resolve, 2000 * retryCount.value));

  try {
    await initPlayer();
    retryCount.value = 0; // 重置重试计数
  } catch (error) {
    console.error(`重试 ${retryCount.value} 失败:`, error);
    if (retryCount.value < props.maxRetryCount) {
      retryConnection();
    } else {
      isRetrying.value = false;
      error.value = true;
      errorMessage.value = '连接失败，已达到最大重试次数';
      connectionStatus.value = 'disconnected';
    }
  }
};

/**
 * 验证后端流是否可用
 */
const validateBackendStream = async () => {
  try {
    console.log('验证后端流可用性:', flvUrl.value);

    // 发送HEAD请求检查流是否可用
    const response = await fetch(flvUrl.value, {
      method: 'HEAD',
      headers: {
        'Accept': 'video/x-flv, application/octet-stream, */*'
      }
    });

    if (!response.ok) {
      throw new Error(`后端流不可用: HTTP ${response.status}`);
    }

    const contentType = response.headers.get('content-type');
    console.log('后端流验证成功:', {
      status: response.status,
      contentType: contentType,
      contentLength: response.headers.get('content-length')
    });

    return true;
  } catch (error) {
    console.error('后端流验证失败:', error);
    return false;
  }
};

/**
 * 初始化播放器 - 优化版本
 */
const initPlayer = async () => {
  if (!videoElement.value) {
    console.error('视频元素未找到');
    return;
  }

  if (!props.streamId) {
    throw new Error('Stream ID未设置');
  }

  try {
    loading.value = true;
    loadingText.value = '正在初始化播放器...';
    error.value = false;
    isRetrying.value = false;
    connectionStatus.value = 'connecting';

    // 清理之前的播放器
    destroyPlayer();

    // 1. 首先启动流转换服务
    loadingText.value = '正在启动视频流服务...';
    console.log('步骤1: 启动流转换服务');
    const streamStarted = await startStreamConversion();
    if (!streamStarted) {
      console.warn('流转换服务启动失败，尝试直接连接');
    }

    // 2. 等待流准备就绪
    loadingText.value = '正在等待视频流准备...';
    console.log('步骤2: 等待流准备就绪');
    const streamReady = await waitForStreamReady(15000); // 等待15秒
    if (!streamReady) {
      console.warn('流未准备就绪，尝试直接连接');
    }

    // 3. 检查flv.js是否可用
    loadingText.value = '正在初始化播放器...';
    if (!flvjs || !flvjs.isSupported()) {
      throw new Error('flv.js不可用或当前浏览器不支持FLV播放');
    }

    // 检查浏览器对MSE的支持
    if (!window.MediaSource) {
      throw new Error('当前浏览器不支持Media Source Extensions (MSE)');
    }

    console.log('浏览器支持检查:', {
      flvjsSupported: flvjs.isSupported(),
      mseSupported: !!window.MediaSource,
      videoElement: !!videoElement.value,
      flvUrl: flvUrl.value
    });

    // 验证后端流是否可用
    loadingText.value = '正在验证视频流...';
    const streamValid = await validateBackendStream();
    if (!streamValid) {
      throw new Error('后端视频流不可用，请检查RTSP源或重试');
    }

    loadingText.value = '正在连接视频流...';

    // 创建优化的FLV配置
    const flvConfig = {
      type: 'flv',
      url: flvUrl.value,
      isLive: true,
      hasAudio: true,
      hasVideo: true,
      cors: true,
      withCredentials: false,
      // 优化的请求头
      headers: {
        'Accept': 'video/x-flv, application/octet-stream, */*',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    };

    // 优化的FLV播放器选项
    const flvOptions = {
      enableWorker: false, // 禁用Worker以提高兼容性
      enableStashBuffer: true, // 启用缓存缓冲区
      stashInitialSize: 256, // 增加初始缓存大小

      // 自动清理配置 - 优化内存使用
      autoCleanupSourceBuffer: true,
      autoCleanupMaxBackwardDuration: 20, // 减少历史数据保留时间
      autoCleanupMinBackwardDuration: 5, // 减少最少保留时间

      // 音视频同步
      fixAudioTimestampGap: true,

      // 禁用精确定位以提高性能
      accurateSeek: false,

      // 实时流优化
      liveBufferLatencyChasing: true,
      liveBufferLatencyMaxLatency: 2, // 减少最大延迟
      liveBufferLatencyMinRemain: 0.3, // 减少最小保留

      // 其他配置
      lazyLoad: false, // 禁用懒加载以提高实时性
      reuseRedirectedURL: false,
      referrerPolicy: 'no-referrer-when-downgrade' as ReferrerPolicy,

      // 添加错误恢复配置
      enableEarlyEofRecover: true, // 启用早期EOF恢复
      enableSeekFix: true, // 启用定位修复

      // MSE配置优化
      deferLoadAfterSourceOpen: false, // 不延迟加载

      // 新增配置优化
      enableStatisticsInfo: true, // 启用统计信息
      statisticsInfoReportInterval: 1000 // 统计信息报告间隔
    };

    console.log('创建FLV播放器:', { config: flvConfig, options: flvOptions });

    flvPlayer = flvjs.createPlayer(flvConfig, flvOptions);
    flvPlayer.attachMediaElement(videoElement.value);

    // 设置事件监听
    setupFlvEvents();

    // 标准方式加载播放器 - 添加安全检查
    console.log('开始加载FLV播放器...');

    if (!isFlvPlayerValid()) {
      throw new Error('FLV播放器在加载前已被销毁');
    }

    try {
      await flvPlayer.load();
    } catch (loadError) {
      console.error('FLV播放器加载失败:', loadError);

      // 检查是否是currentURL相关错误
      if (loadError.message && loadError.message.includes('currentURL')) {
        console.error('检测到currentURL错误，可能是播放器状态异常');
        throw new Error('播放器状态异常，请重试');
      }

      throw loadError;
    }

    console.log('FLV播放器加载完成，等待数据...');

    // 设置多个检查点来确保状态正确更新
    // 1秒后检查
    setTimeout(() => {
      if (loading.value) {
        console.log('1秒后检查：仍在加载，强制更新状态');
        forceUpdateStatus('1秒检查');
        emit('connected');
      }
    }, 1000);

    // 3秒后再次检查
    setTimeout(() => {
      if (loading.value) {
        console.log('3秒后检查：仍在加载，强制更新状态');
        forceUpdateStatus('3秒检查');
        emit('connected');

        // 尝试播放
        if (props.autoplay) {
          tryAutoPlay('3秒检查强制播放');
        }
      }
    }, 3000);

    // 5秒后最终检查
    const loadingTimeout = setTimeout(() => {
      if (loading.value) {
        console.log('5秒后最终检查：强制更新状态');
        forceUpdateStatus('5秒最终检查');
        emit('connected');

        // 尝试播放
        if (props.autoplay) {
          tryAutoPlay('5秒最终检查强制播放');
        }
      }
    }, 5000);

    // 启动定时检查机制，确保视频能够播放
    startPlaybackMonitor();

    // 保存超时定时器，以便在接收到数据时清除
    (flvPlayer as any)._loadingTimeout = loadingTimeout;

  } catch (err: any) {
    console.error('初始化播放器失败:', err);
    connectionStatus.value = 'disconnected';

    // 如果启用重试且未达到最大重试次数，则尝试重试
    if (props.enableRetry && retryCount.value < props.maxRetryCount && !isRetrying.value) {
      console.log('播放器初始化失败，准备重试...');
      retryConnection();
    } else {
      showError('播放器初始化失败: ' + (err.message || err));
    }
  }
};

/**
 * 强制更新连接状态 - 解决一直加载的问题
 */
const forceUpdateStatus = (trigger: string) => {
  console.log(`${trigger}强制更新状态`);

  // 强制更新加载状态
  loading.value = false;
  connectionStatus.value = 'connected';

  console.log('状态已强制更新: loading=false, connectionStatus=connected');
};

/**
 * 尝试自动播放
 */
const tryAutoPlay = (trigger: string) => {
  if (!videoElement.value || isPlaying.value) return;

  console.log(`${trigger}触发自动播放尝试`);

  // 先更新连接状态
  forceUpdateStatus(trigger);

  // 检查视频是否有足够的数据可以播放
  if (videoElement.value.readyState >= 2) { // HAVE_CURRENT_DATA
    console.log('视频数据就绪，开始播放');
    videoElement.value.play().then(() => {
      console.log(`${trigger}触发的播放成功`);
      isPlaying.value = true;
    }).catch(error => {
      console.error(`${trigger}触发的播放失败:`, error);
      // 如果自动播放失败，可能是浏览器策略限制
      if (error.name === 'NotAllowedError') {
        console.log('自动播放被浏览器阻止，需要用户交互');
        showError('自动播放被阻止，请手动点击播放');
      }
    });
  } else {
    console.log(`${trigger}触发但视频数据未就绪，readyState:`, videoElement.value.readyState);
    // 即使数据未就绪，也要更新状态，让用户知道连接已建立
    console.log('数据未就绪但连接已建立，等待用户手动播放');
  }
};

/**
 * 启动播放监控
 */
let playbackMonitorTimer: NodeJS.Timeout | null = null;
const startPlaybackMonitor = () => {
  // 清除之前的定时器
  if (playbackMonitorTimer) {
    clearTimeout(playbackMonitorTimer);
  }

  let attempts = 0;
  const maxAttempts = 10; // 最多尝试10次

  const checkAndPlay = () => {
    if (isPlaying.value || attempts >= maxAttempts) {
      return; // 已经在播放或达到最大尝试次数
    }

    attempts++;
    console.log(`播放监控检查 ${attempts}/${maxAttempts}`);

    if (videoElement.value) {
      const readyState = videoElement.value.readyState;
      const buffered = videoElement.value.buffered;

      console.log('视频状态检查:', {
        readyState,
        bufferedLength: buffered.length,
        bufferedEnd: buffered.length > 0 ? buffered.end(0) : 0,
        paused: videoElement.value.paused,
        currentTime: videoElement.value.currentTime
      });

      // 如果有缓冲数据且视频暂停，尝试播放
      if (readyState >= 2 && buffered.length > 0 && videoElement.value.paused) {
        console.log('检测到视频数据可用，尝试播放');
        videoElement.value.play().then(() => {
          console.log('监控触发的播放成功');
          isPlaying.value = true;
        }).catch(error => {
          console.error('监控触发的播放失败:', error);
          // 继续尝试
          playbackMonitorTimer = setTimeout(checkAndPlay, 1000);
        });
      } else {
        // 继续监控
        playbackMonitorTimer = setTimeout(checkAndPlay, 1000);
      }
    }
  };

  // 延迟1秒开始第一次检查
  playbackMonitorTimer = setTimeout(checkAndPlay, 1000);
};

/**
 * 停止播放监控
 */
const stopPlaybackMonitor = () => {
  if (playbackMonitorTimer) {
    clearTimeout(playbackMonitorTimer);
    playbackMonitorTimer = null;
  }
};

/**
 * 安全检查FLV播放器是否可用
 */
const isFlvPlayerValid = () => {
  return flvPlayer && typeof flvPlayer === 'object' && !flvPlayer._destroyed;
};

/**
 * 设置FLV事件监听
 */
const setupFlvEvents = () => {
  if (!isFlvPlayerValid()) {
    console.warn('FLV播放器无效，跳过事件设置');
    return;
  }

  // 错误事件 - 添加安全检查
  flvPlayer.on(flvjs.Events.ERROR, (errorType: string, errorDetail: string, errorInfo: any) => {
    console.error('FLV播放器错误:', errorType, errorDetail, errorInfo);

    // 检查播放器是否仍然有效
    if (!isFlvPlayerValid()) {
      console.warn('播放器已销毁，忽略错误事件');
      return;
    }

    handleFlvError(errorType, errorDetail, errorInfo);
  });

  // 加载完成事件
  flvPlayer.on(flvjs.Events.LOADING_COMPLETE, () => {
    console.log('FLV加载完成事件触发');
    forceUpdateStatus('LOADING_COMPLETE');
    emit('connected');
    tryAutoPlay('LOADING_COMPLETE');
  });

  // 恢复事件
  flvPlayer.on(flvjs.Events.RECOVERED_EARLY_EOF, () => {
    console.log('FLV从早期EOF恢复');
  });

  // 媒体信息事件
  flvPlayer.on(flvjs.Events.MEDIA_INFO, (mediaInfo: any) => {
    console.log('FLV媒体信息事件触发:', mediaInfo);

    // 清除加载超时定时器
    if ((flvPlayer as any)._loadingTimeout) {
      clearTimeout((flvPlayer as any)._loadingTimeout);
      (flvPlayer as any)._loadingTimeout = null;
    }

    forceUpdateStatus('MEDIA_INFO');
    emit('connected');
    tryAutoPlay('MEDIA_INFO');
  });

  // 统计信息事件
  flvPlayer.on(flvjs.Events.STATISTICS_INFO, (stats: any) => {
    console.debug('FLV统计信息:', stats);
  });

  // 添加视频元素事件监听来处理播放状态
  if (videoElement.value) {
    // 监听canplay事件，当视频可以播放时自动开始
    const onCanPlay = () => {
      console.log('视频可以播放，尝试自动播放');
      tryAutoPlay('canplay事件');
    };

    // 监听loadeddata事件，当视频数据加载完成时
    const onLoadedData = () => {
      console.log('视频数据加载完成，尝试自动播放');
      tryAutoPlay('loadeddata事件');
    };

    // 监听loadedmetadata事件，当视频元数据加载完成时
    const onLoadedMetadata = () => {
      console.log('视频元数据加载完成，尝试自动播放');
      tryAutoPlay('loadedmetadata事件');
    };

    videoElement.value.addEventListener('canplay', onCanPlay);
    videoElement.value.addEventListener('loadeddata', onLoadedData);
    videoElement.value.addEventListener('loadedmetadata', onLoadedMetadata);

    // 在组件卸载时清理事件监听
    onUnmounted(() => {
      if (videoElement.value) {
        videoElement.value.removeEventListener('canplay', onCanPlay);
        videoElement.value.removeEventListener('loadeddata', onLoadedData);
        videoElement.value.removeEventListener('loadedmetadata', onLoadedMetadata);
      }
    });
  }
};

/**
 * 处理FLV播放器错误
 */
const handleFlvError = (errorType: string, errorDetail: string, errorInfo: any) => {
  console.error('FLV错误详情:', { errorType, errorDetail, errorInfo });

  // 再次检查播放器是否有效，防止在销毁过程中触发错误处理
  if (!isFlvPlayerValid()) {
    console.warn('播放器已销毁，忽略错误处理');
    return;
  }

  emit('disconnected');
  connectionStatus.value = 'disconnected';

  let shouldRetry = false;
  let errorMsg = '';

  if (errorType === flvjs.ErrorTypes.NETWORK_ERROR) {
    shouldRetry = true;
    if (errorDetail === flvjs.ErrorDetails.NETWORK_TIMEOUT) {
      errorMsg = '网络超时，请检查网络连接';
    } else if (errorDetail === flvjs.ErrorDetails.NETWORK_UNRECOVERABLE_EARLY_EOF) {
      errorMsg = '网络连接中断';
    } else if (errorDetail === flvjs.ErrorDetails.NETWORK_EXCEPTION) {
      errorMsg = '网络异常';
    } else {
      errorMsg = '网络错误: ' + errorDetail;
    }
  } else if (errorType === flvjs.ErrorTypes.MEDIA_ERROR) {
    if (errorDetail === flvjs.ErrorDetails.MEDIA_FORMAT_ERROR) {
      errorMsg = '媒体格式错误';
    } else if (errorDetail === flvjs.ErrorDetails.MEDIA_FORMAT_UNSUPPORTED) {
      errorMsg = '不支持的媒体格式';
    } else {
      errorMsg = '媒体错误: ' + errorDetail;
    }
  } else if (errorType === flvjs.ErrorTypes.OTHER_ERROR) {
    errorMsg = '其他错误: ' + errorDetail;
  } else {
    errorMsg = '播放器错误: ' + errorDetail;
  }

  // 如果是可重试的错误且启用了重试
  if (shouldRetry && props.enableRetry && retryCount.value < props.maxRetryCount && !isRetrying.value) {
    console.log('检测到可重试错误，准备重试...');
    retryConnection();
  } else {
    showError(errorMsg);
  }
};

/**
 * 销毁播放器
 */
const destroyPlayer = () => {
  console.log('开始销毁FLV播放器...');

  // 先停止播放监控
  stopPlaybackMonitor();

  // 清理重试定时器
  if (retryTimer) {
    clearTimeout(retryTimer);
    retryTimer = null;
  }

  // 清理加载超时定时器
  if (flvPlayer && (flvPlayer as any)._loadingTimeout) {
    clearTimeout((flvPlayer as any)._loadingTimeout);
    (flvPlayer as any)._loadingTimeout = null;
  }

  if (flvPlayer) {
    try {
      console.log('销毁FLV播放器实例...');

      // 1. 先暂停播放
      if (typeof flvPlayer.pause === 'function') {
        flvPlayer.pause();
      }

      // 2. 卸载数据源
      if (typeof flvPlayer.unload === 'function') {
        flvPlayer.unload();
      }

      // 3. 分离媒体元素
      if (typeof flvPlayer.detachMediaElement === 'function') {
        flvPlayer.detachMediaElement();
      }

      // 4. 标记为已销毁，防止后续访问
      (flvPlayer as any)._destroyed = true;

      // 5. 最后销毁播放器
      if (typeof flvPlayer.destroy === 'function') {
        flvPlayer.destroy();
      }

      console.log('FLV播放器销毁完成');
    } catch (err) {
      console.warn('销毁FLV播放器时出错:', err);
      // 即使出错也要清空引用和标记销毁状态
      if (flvPlayer) {
        (flvPlayer as any)._destroyed = true;
      }
    } finally {
      flvPlayer = null;
    }
  }

  // 重置状态
  isPlaying.value = false;
  loading.value = false;
  connectionStatus.value = 'disconnected';

  console.log('播放器销毁流程完成');
};

/**
 * 显示错误
 */
const showError = (msg: string) => {
  error.value = true;
  errorMessage.value = msg;
  loading.value = false;
  connectionStatus.value = 'disconnected';
  message.error(msg);
};

/**
 * 手动重试播放
 */
const retry = () => {
  error.value = false;
  errorMessage.value = '';
  isRetrying.value = false;

  // 清理重试定时器
  if (retryTimer) {
    clearTimeout(retryTimer);
    retryTimer = null;
  }

  initPlayer();
};

/**
 * 重置重试计数
 */
const resetRetry = () => {
  retryCount.value = 0;
  isRetrying.value = false;
  error.value = false;
  errorMessage.value = '';

  // 清理重试定时器
  if (retryTimer) {
    clearTimeout(retryTimer);
    retryTimer = null;
  }
};

/**
 * 开始播放
 */
const startPlay = async () => {
  if (!videoElement.value) {
    console.error('视频元素未找到');
    return;
  }

  try {
    console.log('开始播放流程...');

    // 如果播放器未初始化，先初始化
    if (!flvPlayer) {
      console.log('播放器未初始化，开始初始化...');
      await initPlayer();
    }

    // 确保播放器已经加载数据
    if (flvPlayer && connectionStatus.value === 'connected') {
      console.log('播放器已连接，开始播放视频...');
      await videoElement.value.play();
      console.log('视频播放命令已发送');
    } else {
      console.log('播放器未连接，等待连接完成...');
      // 如果播放器还未连接，等待连接完成后再播放
      const checkConnection = () => {
        if (connectionStatus.value === 'connected' && videoElement.value) {
          console.log('连接完成，开始播放...');
          videoElement.value.play().then(() => {
            console.log('延迟播放成功');
          }).catch(error => {
            console.error('延迟播放失败:', error);
            showError('播放失败: ' + error.message);
          });
        } else if (connectionStatus.value !== 'connecting') {
          console.error('连接失败，无法播放');
          showError('连接失败，无法播放视频');
        } else {
          // 继续等待
          setTimeout(checkConnection, 500);
        }
      };
      checkConnection();
    }
  } catch (error: any) {
    console.error('播放失败:', error);

    // 针对不同错误类型提供更详细的处理
    if (error.name === 'NotSupportedError') {
      console.error('视频源不支持错误，尝试重新初始化播放器');

      // 检查视频元素状态
      if (videoElement.value) {
        console.log('当前视频元素状态:', {
          src: videoElement.value.src,
          currentSrc: videoElement.value.currentSrc,
          readyState: videoElement.value.readyState,
          networkState: videoElement.value.networkState,
          error: videoElement.value.error
        });
      }

      // 尝试重新初始化播放器
      try {
        console.log('尝试重新初始化FLV播放器...');
        await handleNotSupportedError();
      } catch (reinitError) {
        console.error('重新初始化失败:', reinitError);
        showError('视频格式不支持，请检查视频流格式');
      }
    } else if (error.name === 'NotAllowedError') {
      console.log('自动播放被浏览器阻止');
      showError('自动播放被阻止，请手动点击播放按钮');
    } else {
      showError('播放失败: ' + error.message);
    }
  }
};

/**
 * 处理二进制流数据
 */
const handleBinaryStream = async (binaryData: ArrayBuffer | Uint8Array) => {
  console.log('处理二进制流数据，大小:', binaryData.byteLength);

  try {
    // 将二进制数据转换为Blob
    const blob = new Blob([binaryData], { type: 'video/x-flv' });
    const blobUrl = URL.createObjectURL(blob);

    console.log('创建Blob URL:', blobUrl);

    // 更新FLV URL
    flvUrl.value = blobUrl;

    // 重新初始化播放器
    if (flvPlayer) {
      flvPlayer.destroy();
      flvPlayer = null;
    }

    await initPlayer();

    // 清理Blob URL（在播放器销毁时）
    onUnmounted(() => {
      if (blobUrl) {
        URL.revokeObjectURL(blobUrl);
      }
    });

    return true;
  } catch (error) {
    console.error('处理二进制流失败:', error);
    return false;
  }
};

/**
 * 通过Fetch API获取二进制流数据
 */
const fetchBinaryStream = async (url: string) => {
  console.log('获取二进制流数据:', url);

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/octet-stream, video/x-flv, */*',
        'Cache-Control': 'no-cache'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    // 检查响应类型
    const contentType = response.headers.get('content-type');
    console.log('响应内容类型:', contentType);

    // 获取二进制数据
    const arrayBuffer = await response.arrayBuffer();
    console.log('接收到二进制数据，大小:', arrayBuffer.byteLength);

    return arrayBuffer;
  } catch (error) {
    console.error('获取二进制流失败:', error);
    throw error;
  }
};

/**
 * 使用ReadableStream处理流式二进制数据
 */
const handleStreamingBinary = async (url: string) => {
  console.log('处理流式二进制数据:', url);

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/octet-stream, video/x-flv, */*',
        'Cache-Control': 'no-cache'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法获取流读取器');
    }

    // 创建一个可写流来处理数据
    const chunks: Uint8Array[] = [];
    let totalSize = 0;

    while (true) {
      const { done, value } = await reader.read();

      if (done) {
        console.log('流读取完成，总大小:', totalSize);
        break;
      }

      if (value) {
        chunks.push(value);
        totalSize += value.length;
        console.log('接收数据块，大小:', value.length, '总大小:', totalSize);

        // 如果累积了足够的数据，可以开始播放
        if (totalSize > 1024 * 100) { // 100KB
          console.log('累积足够数据，开始处理...');
          const combinedData = new Uint8Array(totalSize);
          let offset = 0;
          for (const chunk of chunks) {
            combinedData.set(chunk, offset);
            offset += chunk.length;
          }

          await handleBinaryStream(combinedData);
          break;
        }
      }
    }

    return true;
  } catch (error) {
    console.error('处理流式二进制数据失败:', error);
    throw error;
  }
};

/**
 * 处理NotSupportedError的专门函数
 */
const handleNotSupportedError = async () => {
  console.log('处理NotSupportedError...');

  try {
    // 检查MSE支持
    if (!window.MediaSource || !MediaSource.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"')) {
      throw new Error('浏览器不支持所需的视频格式');
    }

    // 检查FLV播放器状态
    if (flvPlayer) {
      console.log('FLV播放器状态:', {
        type: flvPlayer.type,
        buffered: flvPlayer.buffered,
        duration: flvPlayer.duration,
        currentTime: flvPlayer.currentTime
      });

      // 尝试重新加载
      console.log('尝试重新加载FLV播放器...');
      flvPlayer.unload();
      await new Promise(resolve => setTimeout(resolve, 500));
      await flvPlayer.load();

      // 等待数据加载
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 再次尝试播放
      if (videoElement.value && videoElement.value.readyState >= 2) {
        await videoElement.value.play();
        isPlaying.value = true;
        console.log('重新加载后播放成功');
      } else {
        throw new Error('重新加载后视频数据仍未就绪');
      }
    } else {
      throw new Error('FLV播放器未初始化');
    }

  } catch (error) {
    console.error('处理NotSupportedError失败:', error);
    throw error;
  }
};

// 视频事件处理
const onLoadStart = () => {
  console.log('视频开始加载');
  emit('loadstart');
};

const onLoadedMetadata = () => {
  console.log('视频元数据加载完成');
  emit('loadedmetadata');
};

const onCanPlay = () => {
  console.log('视频可以播放');
  emit('canplay');
};

const onPlay = () => {
  console.log('视频开始播放');
  isPlaying.value = true;
  emit('play');
};

const onPause = () => {
  console.log('视频暂停');
  isPlaying.value = false;
  emit('pause');
};

const onEnded = () => {
  console.log('视频播放结束');
  isPlaying.value = false;
  emit('ended');
};

const onError = (event: Event) => {
  console.error('视频播放错误:', event);
  isPlaying.value = false;
  emit('error', event);
};

const onWaiting = () => {
  console.log('视频等待数据');
};

const onPlaying = () => {
  console.log('视频正在播放');
  isPlaying.value = true;
};

// 监听props变化
watch(() => props.rtspUrl, (newUrl) => {
  if (newUrl && newUrl !== '') {
    initPlayer();
  }
});

watch(() => props.autoplay, (autoplay) => {
  if (autoplay && !isPlaying.value) {
    startPlay();
  }
});

// 生命周期
onMounted(() => {
  if (props.rtspUrl) {
    initPlayer();
  }
});

onUnmounted(() => {
  destroyPlayer();
});

// 暴露方法给父组件
// 强制播放方法
const forcePlay = async () => {
  if (!videoElement.value) {
    console.error('视频元素未找到');
    return false;
  }

  try {
    console.log('强制播放视频...');

    // 检查视频状态
    const video = videoElement.value;
    console.log('视频状态:', {
      readyState: video.readyState,
      paused: video.paused,
      currentTime: video.currentTime,
      buffered: video.buffered.length > 0 ? video.buffered.end(0) : 0,
      networkState: video.networkState
    });

    // 如果视频已经暂停，直接播放
    if (video.paused) {
      await video.play();
      console.log('强制播放成功');
      isPlaying.value = true;
      return true;
    } else {
      console.log('视频已在播放中');
      isPlaying.value = true;
      return true;
    }
  } catch (error) {
    console.error('强制播放失败:', error);

    // 如果播放失败，尝试重新加载播放器
    if (flvPlayer) {
      try {
        console.log('尝试重新加载FLV播放器...');
        flvPlayer.unload();
        await flvPlayer.load();

        // 等待一段时间后再次尝试播放
        setTimeout(async () => {
          try {
            await videoElement.value?.play();
            console.log('重新加载后播放成功');
            isPlaying.value = true;
          } catch (retryError) {
            console.error('重新加载后播放仍然失败:', retryError);
          }
        }, 1000);
      } catch (reloadError) {
        console.error('重新加载FLV播放器失败:', reloadError);
      }
    }

    return false;
  }
};

defineExpose({
  play: startPlay,
  forcePlay,
  pause: () => videoElement.value?.pause(),
  stop: () => {
    if (videoElement.value) {
      videoElement.value.pause();
      videoElement.value.currentTime = 0;
    }
  },
  destroy: destroyPlayer,
  retry,
  resetRetry,
  isPlaying: () => isPlaying.value,
  isConnected: () => connectionStatus.value === 'connected',
  getVideoElement: () => videoElement.value,
  // 新增二进制流处理方法
  handleBinaryStream,
  fetchBinaryStream,
  handleStreamingBinary,
  // 新增流管理方法
  validateStream,
  startStreamConversion,
  waitForStreamReady
});
</script>

<style scoped>
.rtsp-flv-player-container {
  position: relative;
  background: #000;
  border-radius: 4px;
  overflow: hidden;
  min-height: 200px;
}

.video-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.video-player {
  width: 100%;
  height: 100%;
  display: block;
  background: #000;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 10;
}

.loading-text {
  margin-top: 16px;
  font-size: 14px;
}

.retry-info {
  margin-top: 8px;
  font-size: 12px;
  color: #ccc;
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.error-content {
  text-align: center;
  color: white;
  max-width: 300px;
  padding: 20px;
}

.error-icon {
  font-size: 48px;
  color: #ff4d4f;
  margin-bottom: 16px;
}

.error-text {
  margin-bottom: 16px;
  font-size: 16px;
}

.error-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-bottom: 8px;
}

.retry-count {
  font-size: 12px;
  color: #ccc;
  margin-top: 8px;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 5;
  transition: background-color 0.3s ease;
}

.play-overlay:hover {
  background: rgba(0, 0, 0, 0.7);
}

.play-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  font-size: 48px;
  transition: transform 0.3s ease;
}

.play-button:hover {
  transform: scale(1.1);
}

.play-text {
  margin-top: 8px;
  font-size: 14px;
}

.connection-status {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 12px;
  font-size: 12px;
  z-index: 15;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.status-text {
  color: white;
}

.status-connected .status-dot {
  background-color: #52c41a;
}

.status-connecting .status-dot {
  background-color: #faad14;
  animation: pulse 1.5s infinite;
}

.status-disconnected .status-dot {
  background-color: #ff4d4f;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-content {
    padding: 16px;
    max-width: 250px;
  }

  .error-icon {
    font-size: 36px;
  }

  .error-text {
    font-size: 14px;
  }

  .play-button {
    font-size: 36px;
  }

  .connection-status {
    top: 4px;
    right: 4px;
    padding: 2px 6px;
    font-size: 10px;
  }

  .status-dot {
    width: 6px;
    height: 6px;
  }
}
</style>
