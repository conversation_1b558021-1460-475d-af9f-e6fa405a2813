# Notification 层级优化文档

## 问题描述

在有弹窗（如报警视频监控弹窗）的情况下，notification 通知被隐藏在弹窗下层，用户无法看到重要的通知信息。

## 解决方案

### 1. 层级设置优化

#### 弹窗层级调整
- **报警视频监控弹窗**: z-index: 9999
- **普通弹窗**: z-index: 1000-8999
- **Notification 容器**: z-index: 9998 (默认)
- **高优先级 Notification**: z-index: 10000

#### 层级关系
```
高优先级 Notification (10000)
    ↑
报警视频监控弹窗 (9999)
    ↑
Notification 容器 (9998)
    ↑
普通弹窗 (1000-8999)
    ↑
页面内容 (1-999)
```

### 2. 全局配置优化

#### useMessage.ts 配置
```typescript
notification.config({
  placement: 'topRight',
  duration: 3,
  getContainer: () => {
    // 创建高层级容器
    let container = document.getElementById('high-priority-notification-container');
    if (!container) {
      container = document.createElement('div');
      container.id = 'high-priority-notification-container';
      container.style.position = 'fixed';
      container.style.top = '0';
      container.style.right = '0';
      container.style.zIndex = '9998';
      container.style.pointerEvents = 'none';
      document.body.appendChild(container);
    }
    return container;
  },
});
```

### 3. 高优先级通知使用

#### 基本用法
```typescript
notification.warning({
  message: '重要通知',
  description: '这是一个高优先级通知',
  style: {
    zIndex: 10000,
  },
  class: 'high-priority-notification'
});
```

#### 报警处理通知
```typescript
// 成功通知
notification.success({
  message: '报警处理成功',
  description: '报警已确认处理，相关信息已记录',
  style: { zIndex: 10000 },
  class: 'high-priority-notification'
});

// 错误通知
notification.error({
  message: '报警处理失败',
  description: '网络连接异常，请检查网络后重试',
  style: { zIndex: 10000 },
  class: 'high-priority-notification'
});
```

### 4. 样式优化

#### 全局样式 (design/index.less)
```less
// 高优先级通知容器
#high-priority-notification-container {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  z-index: 9998 !important;
  pointer-events: none !important;
}

// 高优先级通知样式
.high-priority-notification {
  z-index: 10000 !important;
  
  .ant-notification-notice {
    z-index: 10000 !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4) !important;
    backdrop-filter: blur(15px) !important;
    border-radius: 12px !important;
  }
}
```

#### 类型样式区分
- **警告**: 橙色渐变背景，黑色文字
- **成功**: 绿色渐变背景，白色文字  
- **错误**: 红色渐变背景，白色文字

### 5. 实际应用场景

#### 报警系统通知
1. **WebSocket 报警通知**: 自动使用高优先级显示
2. **报警处理反馈**: 处理成功/失败通知
3. **系统状态通知**: 重要系统消息

#### 弹窗场景
1. **报警视频监控弹窗**: z-index: 9999
2. **普通业务弹窗**: z-index: 1000-8999
3. **确保通知始终可见**: z-index: 10000

### 6. 测试验证

#### 测试页面
- `src/views/test/NotificationZIndexTest.vue`

#### 测试场景
1. 普通 notification vs 高优先级 notification
2. 弹窗 + notification 同时显示
3. 报警弹窗 + 紧急通知
4. 多层级弹窗测试

### 7. 兼容性说明

#### 浏览器支持
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

#### 特性支持
- `backdrop-filter`: 毛玻璃效果
- `z-index`: 层级控制
- `pointer-events`: 点击穿透

### 8. 注意事项

1. **性能考虑**: 高优先级通知应谨慎使用，避免滥用
2. **用户体验**: 确保通知内容简洁明了
3. **层级管理**: 新增弹窗时注意 z-index 分配
4. **样式一致性**: 保持与系统整体风格一致

### 9. 维护指南

#### 添加新的高优先级通知
```typescript
notification.type({
  message: '标题',
  description: '描述',
  style: { zIndex: 10000 },
  class: 'high-priority-notification'
});
```

#### 修改层级
- 修改 `useMessage.ts` 中的容器 z-index
- 更新 `design/index.less` 中的样式 z-index
- 确保层级关系正确

#### 样式定制
- 在 `design/index.less` 中修改 `.high-priority-notification` 样式
- 支持不同类型的样式定制
- 保持响应式设计

## 总结

通过层级优化、容器管理、样式定制等多方面的改进，确保了 notification 在任何弹窗场景下都能正确显示，提升了用户体验和系统可用性。
