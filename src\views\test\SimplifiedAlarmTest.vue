<template>
  <div class="simplified-alarm-test">
    <div class="test-header">
      <h1>简化报警处理测试</h1>
      <p>测试删除处理意见表单后的报警处理功能</p>
    </div>

    <div class="test-controls">
      <a-space size="large">
        <a-button type="primary" size="large" @click="showSimplifiedAlarm">
          显示简化报警弹窗
        </a-button>
        <a-button type="default" size="large" @click="clearLogs">
          清空日志
        </a-button>
      </a-space>
    </div>

    <div class="test-info">
      <div class="info-card">
        <h3>🎯 简化优化</h3>
        <ul>
          <li><strong>删除表单</strong> - 移除了处理意见输入框</li>
          <li><strong>固定意见</strong> - 默认使用"已确认处理"</li>
          <li><strong>简化操作</strong> - 用户只需点击确认按钮</li>
          <li><strong>保持功能</strong> - API调用和状态管理不变</li>
          <li><strong>提升体验</strong> - 减少用户操作步骤</li>
        </ul>
      </div>

      <div class="info-card">
        <h3>📋 处理流程</h3>
        <ul>
          <li><strong>步骤1</strong> - 查看报警视频监控</li>
          <li><strong>步骤2</strong> - 点击"确认处理"按钮</li>
          <li><strong>步骤3</strong> - 系统自动使用"已确认处理"</li>
          <li><strong>步骤4</strong> - 调用API提交处理信息</li>
          <li><strong>步骤5</strong> - 显示处理结果并关闭弹窗</li>
        </ul>
      </div>
    </div>

    <div class="test-logs">
      <h3>📝 操作日志</h3>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
        <div v-if="logs.length === 0" class="no-logs">暂无日志</div>
      </div>
    </div>

    <!-- 报警视频监控模态框 -->
    <AlarmVideoMonitorModal
      v-model:open="alarmModalVisible"
      :alarm-data="testAlarmData"
      @confirm="handleAlarmConfirm"
      @close="handleAlarmClose"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import AlarmVideoMonitorModal from '/@/components/AlarmVideoMonitorModal/index.vue';
import { handleUrl } from '/@/views/alarm/Alarm.api';
import { useMessage } from '/@/hooks/web/useMessage';
import { useUserStore } from '/@/store/modules/user';

const { notification } = useMessage();
const userStore = useUserStore();

// 响应式数据
const alarmModalVisible = ref(false);
const logs = ref<Array<{time: string, message: string, type: string}>>([]);

// 测试报警数据
const testAlarmData = ref({
  id: 'simplified_alarm_001',
  alarmId: 'simplified_alarm_001',
  msgTxt: '简化报警测试 - 无需输入处理意见',
  msgDesc: '这是一个简化的报警处理测试，系统将自动使用默认处理意见',
  timestamp: new Date(),
  location: '测试区域',
  deviceId: 'TEST-CAM-001',
  videoList: [
    {
      id: 'test-video-1',
      name: '测试摄像头-01',
      videoUrl: 'http://test.example.com/video1.mp4',
      streamId: 'test-stream-001',
      websocketUrl: 'ws://test.example.com/stream1',
      cameraIndexCode: 'TEST-CAM-001',
      streamType: 'preview',
      rtspUrl: 'rtsp://test.example.com/stream1'
    },
    {
      id: 'test-video-2',
      name: '测试摄像头-02',
      videoUrl: 'http://test.example.com/video2.mp4',
      streamId: 'test-stream-002',
      websocketUrl: 'ws://test.example.com/stream2',
      cameraIndexCode: 'TEST-CAM-002',
      streamType: 'preview',
      rtspUrl: 'rtsp://test.example.com/stream2'
    }
  ]
});

// 添加日志
const addLog = (message: string, type: string = 'info') => {
  const time = new Date().toLocaleTimeString();
  logs.value.unshift({ time, message, type });
  
  // 限制日志数量
  if (logs.value.length > 30) {
    logs.value = logs.value.slice(0, 30);
  }
};

// 显示简化报警
const showSimplifiedAlarm = () => {
  addLog('触发简化报警测试', 'info');
  alarmModalVisible.value = true;
};

// 清空日志
const clearLogs = () => {
  logs.value = [];
  addLog('日志已清空', 'info');
};

// 处理报警确认
const handleAlarmConfirm = async (data: any) => {
  addLog(`开始处理报警，ID: ${data.id}`, 'info');
  addLog(`使用默认处理意见: ${data.handleOpinion}`, 'info');
  
  try {
    // 构建处理参数
    const handleParams = {
      id: data.id || data.alarmId,
      handleOpinion: data.handleOpinion,
      handleStatus: 1,
      handleTime: new Date().toISOString(),
      handleUser: userStore.getUserInfo?.realname || '测试用户'
    };

    addLog(`调用API参数: ${JSON.stringify(handleParams)}`, 'info');
    
    // 调用API处理报警
    const result = await handleUrl(handleParams);
    
    if (result.success) {
      addLog('报警处理成功', 'success');
      notification.success({
        message: '报警处理成功',
        description: '报警已确认处理，相关信息已记录',
        style: { zIndex: 10000 },
        class: 'high-priority-notification'
      });
      alarmModalVisible.value = false;
    } else {
      addLog(`报警处理失败: ${result.message}`, 'error');
      notification.warning({
        message: '报警处理失败',
        description: result.message || '处理失败，请稍后重试',
        style: { zIndex: 10000 },
        class: 'high-priority-notification'
      });
    }
    
  } catch (error: any) {
    addLog(`API调用失败: ${error.message || error}`, 'error');
    notification.error({
      message: '报警处理失败',
      description: '网络连接异常，请检查网络后重试',
      style: { zIndex: 10000 },
      class: 'high-priority-notification'
    });
  }
};

// 处理报警关闭
const handleAlarmClose = () => {
  addLog('报警弹窗已关闭', 'info');
};
</script>

<style lang="less" scoped>
.simplified-alarm-test {
  padding: 32px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  min-height: 100vh;
  color: #ffffff;
}

.test-header {
  text-align: center;
  margin-bottom: 32px;
  
  h1 {
    color: #00d4ff;
    font-size: 28px;
    margin-bottom: 12px;
    font-weight: 600;
  }
  
  p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    margin: 0;
  }
}

.test-controls {
  text-align: center;
  margin-bottom: 32px;
}

.test-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.info-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 24px;
  border: 1px solid rgba(0, 212, 255, 0.3);
  
  h3 {
    color: #00d4ff;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
  }
  
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    
    li {
      padding: 6px 0;
      padding-left: 16px;
      position: relative;
      color: rgba(255, 255, 255, 0.9);
      font-size: 13px;
      line-height: 1.4;
      
      &:before {
        content: '▶';
        position: absolute;
        left: 0;
        color: #00d4ff;
        font-size: 10px;
      }
      
      strong {
        color: #ffffff;
      }
    }
  }
}

.test-logs {
  max-width: 1200px;
  margin: 0 auto;
  
  h3 {
    color: #00d4ff;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 600;
  }
}

.log-container {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid rgba(0, 212, 255, 0.2);
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 6px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-family: 'Courier New', monospace;
  font-size: 12px;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.info {
    color: rgba(255, 255, 255, 0.8);
  }
  
  &.success {
    color: #52c41a;
  }
  
  &.error {
    color: #ff4d4f;
  }
}

.log-time {
  color: #00d4ff;
  min-width: 80px;
}

.log-message {
  flex: 1;
  word-break: break-all;
}

.no-logs {
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
  padding: 20px;
}

@media (max-width: 768px) {
  .test-info {
    grid-template-columns: 1fr;
  }
}
</style>
