<template>
  <div class="command-center-fullscreen">
    <CommandCenter />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted } from 'vue';
import CommandCenter from './CommandCenter.vue';

// 进入全屏
const enterFullscreen = () => {
  if (document.documentElement.requestFullscreen) {
    document.documentElement.requestFullscreen();
  }
};

// 退出全屏
const exitFullscreen = () => {
  if (document.exitFullscreen) {
    document.exitFullscreen();
  }
};

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' || event.key === 'F11') {
    event.preventDefault();
    exitFullscreen();
  }
};

onMounted(() => {
  // 自动进入全屏
  setTimeout(enterFullscreen, 100);
  
  // 监听键盘事件
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});
</script>

<style lang="less" scoped>
.command-center-fullscreen {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: #000000;
  
  :deep(.command-center) {
    width: 1920px;
    height: 1080px;
    transform-origin: top left;
    
    // 自动缩放适应不同分辨率
    @media (max-width: 1920px) {
      transform: scale(calc(100vw / 1920));
    }
    
    @media (max-height: 1080px) {
      transform: scale(calc(100vh / 1080));
    }
    
    @media (max-width: 1920px) and (max-height: 1080px) {
      transform: scale(min(calc(100vw / 1920), calc(100vh / 1080)));
    }
  }
}
</style>
