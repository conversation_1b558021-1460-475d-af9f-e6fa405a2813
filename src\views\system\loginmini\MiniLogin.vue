<template>
  <div :class="prefixCls" class="inspection-login-container">
    <AppLocalePicker class="absolute top-4 right-4 enter-x xl:text-gray-600" :showText="false"/>
    <AppDarkModeToggle class="absolute top-3 right-7 enter-x" />

    <!-- 3D巡检系统背景动画 -->
    <div class="inspection-3d-background">
      <div class="floating-cubes">
        <div class="cube cube-1"></div>
        <div class="cube cube-2"></div>
        <div class="cube cube-3"></div>
        <div class="cube cube-4"></div>
        <div class="cube cube-5"></div>
      </div>
      <div class="inspection-grid"></div>
      <div class="scanning-lines"></div>
    </div>

    <!-- 巡检系统标题区域 -->
    <div class="inspection-header" v-if="!getIsMobile">
      <div class="inspection-title-container">
        <div class="title-3d-wrapper">
          <h1 class="main-title">智能巡检系统</h1>
          <div class="title-shadow">智能巡检系统</div>
        </div>
        <div class="subtitle-3d-wrapper">
          <h2 class="sub-title">INTELLIGENT INSPECTION SYSTEM</h2>
          <div class="subtitle-shadow">INTELLIGENT INSPECTION SYSTEM</div>
        </div>
        <div class="title-decoration-3d">
          <div class="decoration-line"></div>
          <div class="decoration-dots">
            <span class="dot"></span>
            <span class="dot"></span>
            <span class="dot"></span>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="inspection-mobile-header">
      <h1 class="mobile-main-title">智能巡检系统</h1>
      <h2 class="mobile-sub-title">INTELLIGENT INSPECTION SYSTEM</h2>
    </div>
    <div v-show="type === 'login'">
      <div class="inspection-content">
        <div class="inspection-login-card">
          <!-- 3D巡检图标 -->
          <div class="inspection-3d-icon">
            <div class="icon-container">
              <div class="inspection-symbol">
                <div class="symbol-ring ring-1"></div>
                <div class="symbol-ring ring-2"></div>
                <div class="symbol-ring ring-3"></div>
                <div class="center-dot"></div>
              </div>
            </div>
          </div>

          <div class="inspection-form-container">
            <!-- 巡检系统登录表单 -->
            <div class="inspection-form-box">
              <div class="inspection-form-header">
                <div class="login-title-3d">
                  <span class="title-text">巡检系统登录</span>
                  <div class="title-glow"></div>
                </div>
                <div class="login-subtitle-3d">INSPECTION SYSTEM ACCESS</div>
                <div class="status-indicator">
                  <div class="status-dot"></div>
                  <span class="status-text">系统在线</span>
                </div>
              </div>
              <div class="inspection-form-content">
                <a-form ref="loginRef" :model="formData" v-if="activeIndex === 'accountLogin'" @keyup.enter.native="loginHandleClick">
                  <div class="inspection-input-group">
                    <div class="inspection-input-item">
                      <div class="input-3d-wrapper">
                        <div class="input-icon-3d">
                          <div class="icon-bg"></div>
                          <i class="icon icon-code"></i>
                        </div>
                        <a-form-item>
                          <a-input
                            class="inspection-input"
                            :placeholder="t('sys.login.userName')"
                            v-model:value="formData.username"
                          />
                        </a-form-item>
                        <div class="input-scan-line"></div>
                      </div>
                    </div>

                    <div class="inspection-input-item">
                      <div class="input-3d-wrapper">
                        <div class="input-icon-3d">
                          <div class="icon-bg"></div>
                          <i class="icon icon-password"></i>
                        </div>
                        <a-form-item>
                          <a-input
                            class="inspection-input"
                            type="password"
                            :placeholder="t('sys.login.password')"
                            v-model:value="formData.password"
                          />
                        </a-form-item>
                        <div class="input-scan-line"></div>
                      </div>
                    </div>

                    <div class="inspection-input-item captcha-item">
                      <div class="input-3d-wrapper">
                        <div class="input-icon-3d">
                          <div class="icon-bg"></div>
                          <i class="icon icon-code"></i>
                        </div>
                        <a-form-item>
                          <a-input
                            class="inspection-input captcha-input"
                            type="text"
                            :placeholder="t('sys.login.inputCode')"
                            v-model:value="formData.inputCode"
                          />
                        </a-form-item>
                        <div class="captcha-3d-container">
                          <img
                            v-if="randCodeData.requestCodeSuccess"
                            :src="randCodeData.randCodeImage"
                            @click="handleChangeCheckCode"
                            class="captcha-img-3d"
                          />
                          <img
                            v-else
                            :src="codeImg"
                            @click="handleChangeCheckCode"
                            class="captcha-img-3d"
                          />
                          <div class="captcha-frame"></div>
                        </div>
                        <div class="input-scan-line"></div>
                      </div>
                    </div>
                  </div>
                </a-form>
                <a-form v-else ref="phoneFormRef" :model="phoneFormData" @keyup.enter.native="loginHandleClick">
                  <div class="inspection-input-group">
                    <div class="inspection-input-item">
                      <div class="input-3d-wrapper">
                        <a-input class="inspection-input" :placeholder="t('sys.login.mobile')" v-model:value="phoneFormData.mobile" />
                        <div class="input-scan-line"></div>
                      </div>
                    </div>
                    <div class="inspection-input-item">
                      <div class="input-3d-wrapper">
                        <a-input class="inspection-input" :maxlength="6" :placeholder="t('sys.login.smsCode')" v-model:value="phoneFormData.smscode" />
                        <div v-if="showInterval" class="sms-code-btn-3d" @click="getLoginCode">
                          <a>{{ t('component.countdown.normalText') }}</a>
                        </div>
                        <div v-else class="sms-code-btn-3d disabled">
                          <span>{{ t('component.countdown.sendText', [unref(timeRuning)]) }}</span>
                        </div>
                        <div class="input-scan-line"></div>
                      </div>
                    </div>
                  </div>
                </a-form>
              </div>

              <!-- 3D巡检登录按钮 -->
              <div class="inspection-login-button">
                <div class="button-3d-container">
                  <a-button
                    :loading="loginLoading"
                    class="inspection-btn-login"
                    type="primary"
                    @click="loginHandleClick"
                  >
                    <div class="btn-content">
                      <div class="btn-icon">
                        <div class="scan-icon"></div>
                      </div>
                      <span class="btn-text">{{ t('sys.login.loginButton') }}</span>
                    </div>
                    <div class="btn-3d-effect"></div>
                    <div class="btn-scan-line"></div>
                  </a-button>
                </div>
              </div>

              <!-- 3D巡检系统信息 -->
              <div class="inspection-system-info">
                <div class="info-3d-container">
                  <div class="company-info-3d">
                    <div class="company-name">贵州通用信创科技有限公司</div>
                    <div class="company-contact">联系电话：13885926338</div>
                  </div>
                  <div class="system-status">
                    <div class="status-grid">
                      <div class="grid-item"></div>
                      <div class="grid-item"></div>
                      <div class="grid-item"></div>
                      <div class="grid-item"></div>
                    </div>
                    <div class="status-text">巡检系统运行正常</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div v-show="type === 'forgot'" :class="`${prefixCls}-form`">
      <MiniForgotpad ref="forgotRef" @go-back="goBack" @success="handleSuccess" />
    </div>
    <div v-show="type === 'register'" :class="`${prefixCls}-form`">
      <MiniRegister ref="registerRef" @go-back="goBack" @success="handleSuccess" />
    </div>
    <div v-show="type === 'codeLogin'" :class="`${prefixCls}-form`">
      <MiniCodelogin ref="codeRef" @go-back="goBack" @success="handleSuccess" />
    </div> -->
    <!-- 第三方登录相关弹框 -->
    <!-- <ThirdModal ref="thirdModalRef"></ThirdModal> -->
    
    <!-- 图片验证码弹窗 -->
    <CaptchaModal @register="captchaRegisterModal" @ok="getLoginCode" />
  </div>
</template>
<script lang="ts" setup name="login-mini">
  import { getCaptcha, getCodeInfo } from '/@/api/sys/user';
  import { onMounted, reactive, ref, toRaw, unref } from 'vue';
  import codeImg from '/@/assets/images/checkcode.png';
  import { useUserStore } from '/@/store/modules/user';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { SmsEnum } from '/@/views/sys/login/useLogin';
  import { AppLocalePicker, AppDarkModeToggle } from '/@/components/Application';
  import { useDesign } from "/@/hooks/web/useDesign";
  import { useAppInject } from "/@/hooks/web/useAppInject";
  import CaptchaModal from '@/components/jeecg/captcha/CaptchaModal.vue';
  import { useModal } from "@/components/Modal";
  import { ExceptionEnum } from "@/enums/exceptionEnum";

  const { prefixCls } = useDesign('mini-login');
  const { notification, createMessage } = useMessage();
  const userStore = useUserStore();
  const { t } = useI18n();
  const randCodeData = reactive<any>({
    randCodeImage: '',
    requestCodeSuccess: false,
    checkKey: null,
  });
  //手机号登录还是账号登录
  const activeIndex = ref<string>('accountLogin');
  const type = ref<string>('login');
  //账号登录表单字段
  const formData = reactive<any>({
    inputCode: '',
    username: '',
    password: '',
  });
  //手机登录表单字段
  const phoneFormData = reactive<any>({
    mobile: '',
    smscode: '',
  });
  const loginRef = ref();
  //是否显示获取验证码
  const showInterval = ref<boolean>(true);
  //60s
  const timeRuning = ref<number>(60);
  //定时器
  const timer = ref<any>(null);
  const loginLoading = ref<boolean>(false);
  const { getIsMobile } = useAppInject();
  const [captchaRegisterModal, { openModal: openCaptchaModal }] = useModal();
  defineProps({
    sessionTimeout: {
      type: Boolean,
    },
  });

  /**
   * 获取验证码
   */
  function handleChangeCheckCode() {
    formData.inputCode = '';
    //update-begin---author:chenrui ---date:2025/1/7  for：[QQYUN-10775]验证码可以复用 #7674------------
    randCodeData.checkKey = new Date().getTime() + Math.random().toString(36).slice(-4); // *************;
    //update-end---author:chenrui ---date:2025/1/7  for：[QQYUN-10775]验证码可以复用 #7674------------
    getCodeInfo(randCodeData.checkKey).then((res) => {
      randCodeData.randCodeImage = res;
      randCodeData.requestCodeSuccess = true;
    });
  }



  /**
   * 账号或者手机登录
   */
  async function loginHandleClick() {
    if (unref(activeIndex) === 'accountLogin') {
      accountLogin();
    } else {
      //手机号登录
      phoneLogin();
    }
  }

  async function accountLogin() {
    if (!formData.username) {
      createMessage.warn(t('sys.login.accountPlaceholder'));
      return;
    }
    if (!formData.password) {
      createMessage.warn(t('sys.login.passwordPlaceholder'));
      return;
    }
    try {
      loginLoading.value = true;
      const userInfo = await userStore.login(
        toRaw({
          password: formData.password,
          username: formData.username,
          captcha: formData.inputCode,
          checkKey: randCodeData.checkKey,
          mode: 'none', //不要默认的错误提示
        })
      );
      if (userInfo) {
        notification.success({
          message: t('sys.login.loginSuccessTitle'),
          description: `${t('sys.login.loginSuccessDesc')}: ${userInfo.realname}`,
          duration: 3,
        });
      }
    } catch (error: any) {
      notification.error({
        message: t('sys.api.errorTip'),
        description: error.message || t('sys.login.networkExceptionMsg'),
        duration: 3,
      });
      handleChangeCheckCode();
    } finally {
      loginLoading.value = false;
    }
  }

  /**
   * 手机号登录
   */
  async function phoneLogin() {
    if (!phoneFormData.mobile) {
      createMessage.warn(t('sys.login.mobilePlaceholder'));
      return;
    }
    if (!phoneFormData.smscode) {
      createMessage.warn(t('sys.login.smsPlaceholder'));
      return;
    }
    try {
      loginLoading.value = true;
      const userInfo: any = await userStore.phoneLogin({
        mobile: phoneFormData.mobile,
        captcha: phoneFormData.smscode,
        mode: 'none', //不要默认的错误提示
      } as any);
      if (userInfo) {
        notification.success({
          message: t('sys.login.loginSuccessTitle'),
          description: `${t('sys.login.loginSuccessDesc')}: ${userInfo.realname}`,
          duration: 3,
        });
      }
    } catch (error: any) {
      notification.error({
        message: t('sys.api.errorTip'),
        description: error.message || t('sys.login.networkExceptionMsg'),
        duration: 3,
      });
    } finally {
      loginLoading.value = false;
    }
  }

  /**
   * 获取手机验证码
   */
  async function getLoginCode() {
    if (!phoneFormData.mobile) {
      createMessage.warn(t('sys.login.mobilePlaceholder'));
      return;
    }
    //update-begin---author:wangshuai---date:2024-04-18---for:【QQYUN-9005】同一个IP，1分钟超过5次短信，则提示需要验证码---
    const result = await getCaptcha({ mobile: phoneFormData.mobile, smsmode: SmsEnum.FORGET_PASSWORD }).catch((res) =>{
      if(res.code === ExceptionEnum.PHONE_SMS_FAIL_CODE){
        openCaptchaModal(true, {});
      }
    });
    //update-end---author:wangshuai---date:2024-04-18---for:【QQYUN-9005】同一个IP，1分钟超过5次短信，则提示需要验证码---
    if (result) {
      const TIME_COUNT = 60;
      if (!unref(timer)) {
        timeRuning.value = TIME_COUNT;
        showInterval.value = false;
        timer.value = setInterval(() => {
          if (unref(timeRuning) > 0 && unref(timeRuning) <= TIME_COUNT) {
            timeRuning.value = timeRuning.value - 1;
          } else {
            showInterval.value = true;
            clearInterval(unref(timer));
            timer.value = null;
          }
        }, 1000);
      }
    }
  }



  onMounted(() => {
    //加载验证码
    handleChangeCheckCode();
  });
</script>

<style lang="less" scoped>
  @import '/@/assets/loginmini/style/base.less';

  // 巡检系统登录容器
  .inspection-login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 30%, #0d1117 70%, #1a1f2e 100%);
    position: relative;
    overflow: hidden;
    perspective: 1000px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image:
        radial-gradient(circle at 25% 75%, rgba(0, 255, 127, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 75% 25%, rgba(0, 191, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(138, 43, 226, 0.08) 0%, transparent 50%);
      pointer-events: none;
      animation: backgroundPulse 8s ease-in-out infinite;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="inspectionGrid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(0,255,127,0.1)" stroke-width="0.5"/><circle cx="10" cy="10" r="1" fill="rgba(0,191,255,0.2)"/></pattern></defs><rect width="100" height="100" fill="url(%23inspectionGrid)"/></svg>');
      opacity: 0.4;
      pointer-events: none;
      animation: gridMove 20s linear infinite;
    }
  }

  // 3D背景动画
  .inspection-3d-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
  }

  // 浮动立方体
  .floating-cubes {
    position: absolute;
    width: 100%;
    height: 100%;

    .cube {
      position: absolute;
      width: 20px;
      height: 20px;
      background: linear-gradient(45deg, rgba(0, 255, 127, 0.3), rgba(0, 191, 255, 0.2));
      transform-style: preserve-3d;
      animation: floatCube 15s ease-in-out infinite;

      &::before {
        content: '';
        position: absolute;
        top: -10px;
        left: 0;
        width: 20px;
        height: 20px;
        background: linear-gradient(45deg, rgba(0, 255, 127, 0.2), rgba(0, 191, 255, 0.1));
        transform: rotateX(90deg) translateZ(10px);
      }

      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: -10px;
        width: 20px;
        height: 20px;
        background: linear-gradient(45deg, rgba(0, 255, 127, 0.1), rgba(0, 191, 255, 0.05));
        transform: rotateY(90deg) translateZ(10px);
      }

      &.cube-1 {
        top: 20%;
        left: 10%;
        animation-delay: 0s;
        animation-duration: 12s;
      }

      &.cube-2 {
        top: 60%;
        left: 80%;
        animation-delay: -3s;
        animation-duration: 15s;
      }

      &.cube-3 {
        top: 30%;
        left: 70%;
        animation-delay: -6s;
        animation-duration: 18s;
      }

      &.cube-4 {
        top: 80%;
        left: 20%;
        animation-delay: -9s;
        animation-duration: 14s;
      }

      &.cube-5 {
        top: 10%;
        left: 50%;
        animation-delay: -12s;
        animation-duration: 16s;
      }
    }
  }

  // 巡检网格
  .inspection-grid {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
      linear-gradient(rgba(0, 255, 127, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 255, 127, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridPulse 4s ease-in-out infinite;
  }

  // 扫描线
  .scanning-lines {
    position: absolute;
    width: 100%;
    height: 100%;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, transparent, rgba(0, 255, 127, 0.8), transparent);
      animation: scanVertical 6s ease-in-out infinite;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 2px;
      height: 100%;
      background: linear-gradient(180deg, transparent, rgba(0, 191, 255, 0.6), transparent);
      animation: scanHorizontal 8s ease-in-out infinite;
    }
  }

  // 巡检系统标题区域
  .inspection-header {
    position: absolute;
    top: 8%;
    left: 8%;
    z-index: 10;
    transform-style: preserve-3d;

    .inspection-title-container {
      .title-3d-wrapper {
        position: relative;
        transform-style: preserve-3d;

        .main-title {
          font-size: 2.8rem;
          font-weight: 800;
          color: #00ff7f;
          margin: 0;
          text-shadow:
            0 0 20px rgba(0, 255, 127, 0.8),
            0 0 40px rgba(0, 255, 127, 0.4),
            0 0 60px rgba(0, 255, 127, 0.2);
          letter-spacing: 3px;
          transform: translateZ(20px);
          animation: titleGlow 3s ease-in-out infinite;
        }

        .title-shadow {
          position: absolute;
          top: 3px;
          left: 3px;
          font-size: 2.8rem;
          font-weight: 800;
          color: rgba(0, 191, 255, 0.3);
          margin: 0;
          letter-spacing: 3px;
          transform: translateZ(-10px);
          z-index: -1;
        }
      }

      .subtitle-3d-wrapper {
        position: relative;
        transform-style: preserve-3d;
        margin-top: 10px;

        .sub-title {
          font-size: 1.1rem;
          font-weight: 500;
          color: #00bfff;
          margin: 0;
          letter-spacing: 2px;
          opacity: 0.9;
          transform: translateZ(15px);
          animation: subtitlePulse 4s ease-in-out infinite;
        }

        .subtitle-shadow {
          position: absolute;
          top: 2px;
          left: 2px;
          font-size: 1.1rem;
          font-weight: 500;
          color: rgba(138, 43, 226, 0.3);
          margin: 0;
          letter-spacing: 2px;
          transform: translateZ(-5px);
          z-index: -1;
        }
      }

      .title-decoration-3d {
        margin-top: 20px;
        transform-style: preserve-3d;

        .decoration-line {
          width: 80px;
          height: 3px;
          background: linear-gradient(90deg, #00ff7f, #00bfff, #8a2be2);
          border-radius: 2px;
          box-shadow:
            0 0 15px rgba(0, 255, 127, 0.6),
            0 0 30px rgba(0, 191, 255, 0.3);
          transform: translateZ(10px);
          animation: decorationGlow 2s ease-in-out infinite;
        }

        .decoration-dots {
          display: flex;
          gap: 8px;
          margin-top: 10px;
          transform: translateZ(5px);

          .dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #00ff7f;
            box-shadow: 0 0 10px rgba(0, 255, 127, 0.8);
            animation: dotPulse 1.5s ease-in-out infinite;

            &:nth-child(2) {
              animation-delay: 0.3s;
            }

            &:nth-child(3) {
              animation-delay: 0.6s;
            }
          }
        }
      }
    }
  }

  // 移动端巡检标题
  .inspection-mobile-header {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;

    .mobile-main-title {
      font-size: 1.8rem;
      font-weight: 700;
      color: #00ff7f;
      margin: 0;
      text-shadow:
        0 0 15px rgba(0, 255, 127, 0.8),
        0 0 30px rgba(0, 255, 127, 0.4);
      animation: titleGlow 3s ease-in-out infinite;
    }

    .mobile-sub-title {
      font-size: 0.9rem;
      color: #00bfff;
      margin: 4px 0 0 0;
      opacity: 0.9;
      letter-spacing: 1px;
    }
  }

  // 巡检系统内容区域
  .inspection-content {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 40px 20px;
    position: relative;
    z-index: 5;
    perspective: 1200px;
  }

  // 巡检系统登录卡片
  .inspection-login-card {
    background: rgba(10, 15, 20, 0.95);
    backdrop-filter: blur(25px);
    border: 1px solid rgba(0, 255, 127, 0.3);
    border-radius: 20px;
    box-shadow:
      0 15px 50px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(0, 255, 127, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      0 0 100px rgba(0, 255, 127, 0.1);
    width: 100%;
    max-width: 450px;
    overflow: hidden;
    position: relative;
    transform-style: preserve-3d;
    animation: cardFloat 6s ease-in-out infinite;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, transparent, #00ff7f, #00bfff, transparent);
      opacity: 0.8;
      animation: borderGlow 3s ease-in-out infinite;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle at 50% 0%, rgba(0, 255, 127, 0.1) 0%, transparent 50%);
      pointer-events: none;
    }
  }

  // 3D巡检图标
  .inspection-3d-icon {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;

    .icon-container {
      width: 60px;
      height: 60px;
      transform-style: preserve-3d;
      animation: iconRotate 8s linear infinite;

      .inspection-symbol {
        position: relative;
        width: 60px;
        height: 60px;
        transform-style: preserve-3d;

        .symbol-ring {
          position: absolute;
          border-radius: 50%;
          border: 2px solid;
          animation: ringPulse 3s ease-in-out infinite;

          &.ring-1 {
            width: 60px;
            height: 60px;
            border-color: rgba(0, 255, 127, 0.8);
            animation-delay: 0s;
          }

          &.ring-2 {
            width: 45px;
            height: 45px;
            top: 7.5px;
            left: 7.5px;
            border-color: rgba(0, 191, 255, 0.6);
            animation-delay: 0.5s;
          }

          &.ring-3 {
            width: 30px;
            height: 30px;
            top: 15px;
            left: 15px;
            border-color: rgba(138, 43, 226, 0.4);
            animation-delay: 1s;
          }
        }

        .center-dot {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 8px;
          height: 8px;
          background: #00ff7f;
          border-radius: 50%;
          transform: translate(-50%, -50%);
          box-shadow:
            0 0 15px rgba(0, 255, 127, 0.8),
            0 0 30px rgba(0, 255, 127, 0.4);
          animation: centerPulse 2s ease-in-out infinite;
        }
      }
    }
  }

  // 表单容器
  .inspection-form-container {
    padding: 50px 40px 40px 40px;
    transform-style: preserve-3d;
  }

  // 表单头部
  .inspection-form-header {
    text-align: center;
    margin-bottom: 40px;
    transform-style: preserve-3d;

    .login-title-3d {
      position: relative;
      margin-bottom: 12px;
      transform: translateZ(10px);

      .title-text {
        font-size: 2rem;
        font-weight: 700;
        color: #00ff7f;
        text-shadow:
          0 0 20px rgba(0, 255, 127, 0.8),
          0 0 40px rgba(0, 255, 127, 0.4);
        animation: titlePulse 4s ease-in-out infinite;
      }

      .title-glow {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, rgba(0, 255, 127, 0.2), rgba(0, 191, 255, 0.1));
        filter: blur(10px);
        z-index: -1;
        animation: glowPulse 3s ease-in-out infinite;
      }
    }

    .login-subtitle-3d {
      font-size: 0.95rem;
      color: #00bfff;
      opacity: 0.9;
      letter-spacing: 2px;
      font-weight: 400;
      transform: translateZ(5px);
      margin-bottom: 15px;
    }

    .status-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      transform: translateZ(8px);

      .status-dot {
        width: 8px;
        height: 8px;
        background: #00ff7f;
        border-radius: 50%;
        box-shadow: 0 0 10px rgba(0, 255, 127, 0.8);
        animation: statusBlink 2s ease-in-out infinite;
      }

      .status-text {
        font-size: 0.8rem;
        color: rgba(0, 255, 127, 0.8);
        font-weight: 500;
      }
    }
  }

  // 巡检系统输入组
  .inspection-input-group {
    .inspection-input-item {
      position: relative;
      margin-bottom: 25px;
      transform-style: preserve-3d;

      .input-3d-wrapper {
        position: relative;
        transform: translateZ(5px);

        .input-icon-3d {
          position: absolute;
          left: 15px;
          top: 50%;
          transform: translateY(-50%);
          z-index: 3;
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;

          .icon-bg {
            position: absolute;
            width: 30px;
            height: 30px;
            background: radial-gradient(circle, rgba(0, 255, 127, 0.2), transparent);
            border-radius: 50%;
            animation: iconBgPulse 3s ease-in-out infinite;
          }

          .icon {
            width: 18px;
            height: 18px;
            opacity: 0.8;
            filter: brightness(0) saturate(100%) invert(48%) sepia(79%) saturate(2476%) hue-rotate(86deg) brightness(118%) contrast(119%);
            z-index: 1;
          }
        }

        :deep(.ant-form-item) {
          margin-bottom: 0;
        }

        .inspection-input {
          height: 55px;
          background: rgba(15, 25, 35, 0.9);
          border: 2px solid rgba(0, 255, 127, 0.3);
          border-radius: 12px;
          padding-left: 55px;
          color: #ffffff;
          font-size: 15px;
          font-weight: 500;
          transition: all 0.4s ease;
          box-shadow:
            inset 0 2px 10px rgba(0, 0, 0, 0.3),
            0 0 0 0 rgba(0, 255, 127, 0);

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
            font-weight: 400;
          }

          &:hover {
            border-color: rgba(0, 255, 127, 0.5);
            box-shadow:
              inset 0 2px 10px rgba(0, 0, 0, 0.3),
              0 0 0 3px rgba(0, 255, 127, 0.1),
              0 0 20px rgba(0, 255, 127, 0.2);
            transform: translateY(-2px);
          }

          &:focus {
            border-color: #00ff7f;
            box-shadow:
              inset 0 2px 10px rgba(0, 0, 0, 0.3),
              0 0 0 3px rgba(0, 255, 127, 0.2),
              0 0 30px rgba(0, 255, 127, 0.3);
            background: rgba(15, 25, 35, 0.95);
            transform: translateY(-3px);
          }
        }

        .input-scan-line {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 0;
          height: 2px;
          background: linear-gradient(90deg, #00ff7f, #00bfff);
          transition: width 0.3s ease;
          border-radius: 1px;
        }

        &:hover .input-scan-line {
          width: 100%;
        }
      }

      // 验证码特殊样式
      &.captcha-item {
        .captcha-input {
          padding-right: 140px;
        }

        .captcha-3d-container {
          position: absolute;
          right: 10px;
          top: 50%;
          transform: translateY(-50%);

          .captcha-img-3d {
            height: 38px;
            border-radius: 8px;
            cursor: pointer;
            border: 2px solid rgba(0, 255, 127, 0.4);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);

            &:hover {
              border-color: #00ff7f;
              box-shadow:
                0 4px 15px rgba(0, 0, 0, 0.3),
                0 0 15px rgba(0, 255, 127, 0.4);
              transform: scale(1.05);
            }
          }

          .captcha-frame {
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 1px solid rgba(0, 191, 255, 0.3);
            border-radius: 8px;
            pointer-events: none;
            animation: captchaFrameGlow 2s ease-in-out infinite;
          }
        }
      }
    }
  }

  // 短信验证码按钮3D
  .sms-code-btn-3d {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    padding: 10px 18px;
    background: linear-gradient(135deg, #00ff7f, #00bfff);
    border-radius: 8px;
    cursor: pointer;
    font-size: 12px;
    color: #ffffff;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow:
      0 4px 15px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(0, 255, 127, 0.3);

    &:hover {
      box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(0, 255, 127, 0.5);
      transform: translateY(-50%) scale(1.05) translateZ(5px);
    }

    &.disabled {
      background: rgba(0, 255, 127, 0.3);
      cursor: not-allowed;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

      &:hover {
        transform: translateY(-50%);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      }
    }
  }

  // 3D巡检登录按钮
  .inspection-login-button {
    margin: 35px 0 30px 0;
    transform: translateZ(10px);

    .button-3d-container {
      perspective: 1000px;

      .inspection-btn-login {
        width: 100%;
        height: 60px;
        background: linear-gradient(135deg, #00ff7f 0%, #00bfff 50%, #8a2be2 100%);
        border: none;
        border-radius: 15px;
        position: relative;
        overflow: hidden;
        font-size: 18px;
        font-weight: 700;
        letter-spacing: 2px;
        transition: all 0.4s ease;
        transform-style: preserve-3d;
        box-shadow:
          0 10px 30px rgba(0, 0, 0, 0.4),
          0 0 50px rgba(0, 255, 127, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);

        .btn-content {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12px;
          position: relative;
          z-index: 3;
          transform: translateZ(10px);

          .btn-icon {
            .scan-icon {
              width: 20px;
              height: 20px;
              border: 2px solid #ffffff;
              border-radius: 50%;
              position: relative;
              animation: scanRotate 2s linear infinite;

              &::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 8px;
                height: 8px;
                background: #ffffff;
                border-radius: 50%;
                transform: translate(-50%, -50%);
                animation: scanPulse 1s ease-in-out infinite;
              }
            }
          }

          .btn-text {
            color: #ffffff;
            text-shadow:
              0 0 10px rgba(0, 0, 0, 0.5),
              0 0 20px rgba(0, 255, 127, 0.3);
          }
        }

        .btn-3d-effect {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent, rgba(255, 255, 255, 0.1));
          transform: translateX(-100%);
          transition: transform 0.6s ease;
        }

        .btn-scan-line {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 2px;
          background: linear-gradient(90deg, transparent, #ffffff, transparent);
          animation: btnScanLine 3s ease-in-out infinite;
        }

        &:hover {
          transform: translateY(-5px) rotateX(5deg);
          box-shadow:
            0 15px 40px rgba(0, 0, 0, 0.5),
            0 0 80px rgba(0, 255, 127, 0.5),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);

          .btn-3d-effect {
            transform: translateX(100%);
          }
        }

        &:active {
          transform: translateY(-2px) rotateX(2deg);
        }

        &:focus {
          box-shadow:
            0 15px 40px rgba(0, 0, 0, 0.5),
            0 0 80px rgba(0, 255, 127, 0.5),
            0 0 0 3px rgba(0, 255, 127, 0.4);
        }
      }
    }
  }

  // 3D巡检系统信息
  .inspection-system-info {
    text-align: center;
    margin-top: 30px;
    transform: translateZ(8px);

    .info-3d-container {
      transform-style: preserve-3d;

      .company-info-3d {
        margin-bottom: 20px;
        transform: translateZ(5px);

        .company-name {
          font-size: 15px;
          color: rgba(255, 255, 255, 0.9);
          margin-bottom: 6px;
          font-weight: 600;
          text-shadow: 0 0 10px rgba(0, 255, 127, 0.3);
        }

        .company-contact {
          font-size: 13px;
          color: rgba(0, 191, 255, 0.8);
          font-weight: 500;
        }
      }

      .system-status {
        transform: translateZ(3px);

        .status-grid {
          display: flex;
          justify-content: center;
          gap: 8px;
          margin-bottom: 10px;

          .grid-item {
            width: 8px;
            height: 8px;
            background: #00ff7f;
            border-radius: 2px;
            box-shadow: 0 0 8px rgba(0, 255, 127, 0.6);
            animation: gridItemPulse 2s ease-in-out infinite;

            &:nth-child(2) {
              animation-delay: 0.3s;
            }

            &:nth-child(3) {
              animation-delay: 0.6s;
            }

            &:nth-child(4) {
              animation-delay: 0.9s;
            }
          }
        }

        .status-text {
          font-size: 12px;
          color: rgba(0, 255, 127, 0.7);
          font-weight: 500;
          letter-spacing: 1px;
        }
      }
    }
  }

  // 动画关键帧
  @keyframes backgroundPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
  }

  @keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(20px, 20px); }
  }

  @keyframes floatCube {
    0%, 100% {
      transform: translateY(0) rotateX(0deg) rotateY(0deg);
    }
    25% {
      transform: translateY(-20px) rotateX(90deg) rotateY(90deg);
    }
    50% {
      transform: translateY(-10px) rotateX(180deg) rotateY(180deg);
    }
    75% {
      transform: translateY(-30px) rotateX(270deg) rotateY(270deg);
    }
  }

  @keyframes gridPulse {
    0%, 100% { opacity: 0.4; }
    50% { opacity: 0.8; }
  }

  @keyframes scanVertical {
    0% { top: 0; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { top: 100%; opacity: 0; }
  }

  @keyframes scanHorizontal {
    0% { left: 0; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 100%; opacity: 0; }
  }

  @keyframes titleGlow {
    0%, 100% {
      text-shadow:
        0 0 20px rgba(0, 255, 127, 0.8),
        0 0 40px rgba(0, 255, 127, 0.4),
        0 0 60px rgba(0, 255, 127, 0.2);
    }
    50% {
      text-shadow:
        0 0 30px rgba(0, 255, 127, 1),
        0 0 60px rgba(0, 255, 127, 0.6),
        0 0 90px rgba(0, 255, 127, 0.3);
    }
  }

  @keyframes subtitlePulse {
    0%, 100% { opacity: 0.9; }
    50% { opacity: 1; }
  }

  @keyframes decorationGlow {
    0%, 100% {
      box-shadow:
        0 0 15px rgba(0, 255, 127, 0.6),
        0 0 30px rgba(0, 191, 255, 0.3);
    }
    50% {
      box-shadow:
        0 0 25px rgba(0, 255, 127, 0.8),
        0 0 50px rgba(0, 191, 255, 0.5);
    }
  }

  @keyframes dotPulse {
    0%, 100% {
      box-shadow: 0 0 10px rgba(0, 255, 127, 0.8);
      transform: scale(1);
    }
    50% {
      box-shadow: 0 0 20px rgba(0, 255, 127, 1);
      transform: scale(1.2);
    }
  }

  @keyframes cardFloat {
    0%, 100% { transform: translateY(0) rotateX(0deg); }
    50% { transform: translateY(-10px) rotateX(2deg); }
  }

  @keyframes borderGlow {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
  }

  @keyframes iconRotate {
    0% { transform: rotateY(0deg); }
    100% { transform: rotateY(360deg); }
  }

  @keyframes ringPulse {
    0%, 100% {
      transform: scale(1);
      opacity: 0.8;
    }
    50% {
      transform: scale(1.1);
      opacity: 1;
    }
  }

  @keyframes centerPulse {
    0%, 100% {
      box-shadow:
        0 0 15px rgba(0, 255, 127, 0.8),
        0 0 30px rgba(0, 255, 127, 0.4);
    }
    50% {
      box-shadow:
        0 0 25px rgba(0, 255, 127, 1),
        0 0 50px rgba(0, 255, 127, 0.6);
    }
  }

  @keyframes titlePulse {
    0%, 100% {
      text-shadow:
        0 0 20px rgba(0, 255, 127, 0.8),
        0 0 40px rgba(0, 255, 127, 0.4);
    }
    50% {
      text-shadow:
        0 0 30px rgba(0, 255, 127, 1),
        0 0 60px rgba(0, 255, 127, 0.6);
    }
  }

  @keyframes glowPulse {
    0%, 100% { opacity: 0.2; }
    50% { opacity: 0.4; }
  }

  @keyframes statusBlink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
  }

  @keyframes iconBgPulse {
    0%, 100% {
      transform: scale(1);
      opacity: 0.2;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.4;
    }
  }

  @keyframes captchaFrameGlow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
  }

  @keyframes scanRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes scanPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
  }

  @keyframes btnScanLine {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(0); }
    100% { transform: translateX(100%); }
  }

  @keyframes gridItemPulse {
    0%, 100% {
      box-shadow: 0 0 8px rgba(0, 255, 127, 0.6);
      transform: scale(1);
    }
    50% {
      box-shadow: 0 0 15px rgba(0, 255, 127, 1);
      transform: scale(1.2);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .inspection-header {
      position: static;
      padding: 20px;
      text-align: center;

      .inspection-title-container {
        .title-3d-wrapper .main-title {
          font-size: 2.2rem;
        }

        .subtitle-3d-wrapper .sub-title {
          font-size: 1rem;
        }
      }
    }

    .inspection-content {
      padding: 20px 15px;
    }

    .inspection-login-card {
      margin-top: 0;

      .inspection-form-container {
        padding: 40px 25px 30px 25px;
      }
    }
  }

  @media (max-width: 480px) {
    .inspection-header {
      .inspection-title-container {
        .title-3d-wrapper .main-title {
          font-size: 1.8rem;
        }

        .subtitle-3d-wrapper .sub-title {
          font-size: 0.9rem;
        }
      }
    }

    .inspection-form-container {
      padding: 35px 20px 25px 20px;
    }

    .inspection-input-group {
      .inspection-input-item {
        &.captcha-item {
          .captcha-input {
            padding-right: 120px;
          }
        }
      }
    }
  }

  :deep(.ant-input:focus) {
    box-shadow: none;
  }

  :deep(.jeecg-dark-switch){
    position:absolute;
    margin-right: 10px;
  }

  .top-3{
    top: 0.45rem;
  }
</style>

<style lang="less">
@prefix-cls: ~'@{namespace}-mini-login';
@dark-bg: #293146;

html[data-theme='dark'] {
  .@{prefix-cls} {
    // 暗色主题下保持巡检系统设计
    .inspection-login-container {
      background: linear-gradient(135deg, #000000 0%, #0a0a0a 30%, #000000 70%, #0a0a0a 100%);

      &::before {
        background-image:
          radial-gradient(circle at 25% 75%, rgba(0, 255, 127, 0.2) 0%, transparent 50%),
          radial-gradient(circle at 75% 25%, rgba(0, 191, 255, 0.15) 0%, transparent 50%),
          radial-gradient(circle at 50% 50%, rgba(138, 43, 226, 0.1) 0%, transparent 50%);
      }
    }

    .inspection-login-card {
      background: rgba(0, 0, 0, 0.98);
      border-color: rgba(0, 255, 127, 0.4);
      box-shadow:
        0 15px 50px rgba(0, 0, 0, 0.7),
        0 0 0 1px rgba(0, 255, 127, 0.3),
        0 0 100px rgba(0, 255, 127, 0.2);
    }

    .inspection-input-group {
      .inspection-input-item {
        .inspection-input {
          background: rgba(0, 0, 0, 0.95);
          border-color: rgba(0, 255, 127, 0.4);
          color: #e6edf3;

          &::placeholder {
            color: rgba(230, 237, 243, 0.5);
          }

          &:hover {
            border-color: rgba(0, 255, 127, 0.6);
            box-shadow:
              inset 0 2px 10px rgba(0, 0, 0, 0.3),
              0 0 0 3px rgba(0, 255, 127, 0.15),
              0 0 25px rgba(0, 255, 127, 0.3);
          }

          &:focus {
            border-color: #00ff7f;
            box-shadow:
              inset 0 2px 10px rgba(0, 0, 0, 0.3),
              0 0 0 3px rgba(0, 255, 127, 0.3),
              0 0 40px rgba(0, 255, 127, 0.4);
            background: rgba(0, 0, 0, 0.98);
          }
        }
      }
    }

    .inspection-system-info {
      .company-info-3d {
        .company-name {
          color: rgba(230, 237, 243, 0.9);
        }

        .company-contact {
          color: rgba(0, 191, 255, 0.8);
        }
      }
    }

    .app-iconify {
      color: #fff !important;
    }

    .ant-checkbox-inner {
      border-color: #64c7eb;
    }
  }

  input.fix-auto-fill,
  .fix-auto-fill input {
    -webkit-text-fill-color: #e6edf3 !important;
    box-shadow: inherit !important;
  }

  .ant-divider-inner-text {
    font-size: 12px !important;
    color: @text-color-secondary !important;
  }
}
</style>
