<template>
  <div :class="prefixCls" class="tech-login-container">
    <AppLocalePicker class="absolute top-4 right-4 enter-x xl:text-gray-600" :showText="false"/>
    <AppDarkModeToggle class="absolute top-3 right-7 enter-x" />

    <!-- 科技感标题区域 -->
    <div class="tech-header" v-if="!getIsMobile">
      <div class="tech-title-container">
        <h1 class="main-title">测试监狱</h1>
        <h2 class="sub-title">值星员瞪巡管理系统</h2>
        <div class="title-decoration"></div>
      </div>
    </div>
    <div v-else class="tech-mobile-header">
      <h1 class="mobile-main-title">测试监狱</h1>
      <h2 class="mobile-sub-title">值星员瞪巡管理系统</h2>
    </div>
    <div v-show="type === 'login'">
      <div class="tech-content">
        <div class="tech-login-card">
          <div class="tech-form-container">
            <!-- 科技感登录表单 -->
            <div class="tech-form-box">
              <div class="tech-form-header">
                <div class="login-title">系统登录</div>
                <div class="login-subtitle">SYSTEM LOGIN</div>
              </div>
              <div class="tech-form-content">
                <a-form ref="loginRef" :model="formData" v-if="activeIndex === 'accountLogin'" @keyup.enter.native="loginHandleClick">
                  <div class="tech-input-group">
                    <div class="tech-input-item">
                      <div class="input-icon">
                        <i class="icon icon-code"></i>
                      </div>
                      <a-form-item>
                        <a-input
                          class="tech-input"
                          :placeholder="t('sys.login.userName')"
                          v-model:value="formData.username"
                        />
                      </a-form-item>
                    </div>

                    <div class="tech-input-item">
                      <div class="input-icon">
                        <i class="icon icon-password"></i>
                      </div>
                      <a-form-item>
                        <a-input
                          class="tech-input"
                          type="password"
                          :placeholder="t('sys.login.password')"
                          v-model:value="formData.password"
                        />
                      </a-form-item>
                    </div>

                    <div class="tech-input-item captcha-item">
                      <div class="input-icon">
                        <i class="icon icon-code"></i>
                      </div>
                      <a-form-item>
                        <a-input
                          class="tech-input captcha-input"
                          type="text"
                          :placeholder="t('sys.login.inputCode')"
                          v-model:value="formData.inputCode"
                        />
                      </a-form-item>
                      <div class="captcha-code">
                        <img
                          v-if="randCodeData.requestCodeSuccess"
                          :src="randCodeData.randCodeImage"
                          @click="handleChangeCheckCode"
                          class="captcha-img"
                        />
                        <img
                          v-else
                          :src="codeImg"
                          @click="handleChangeCheckCode"
                          class="captcha-img"
                        />
                      </div>
                    </div>
                  </div>
                </a-form>
                <a-form v-else ref="phoneFormRef" :model="phoneFormData" @keyup.enter.native="loginHandleClick">
                  <div class="tech-input-group">
                    <div class="tech-input-item">
                      <a-input class="tech-input" :placeholder="t('sys.login.mobile')" v-model:value="phoneFormData.mobile" />
                    </div>
                    <div class="tech-input-item">
                      <a-input class="tech-input" :maxlength="6" :placeholder="t('sys.login.smsCode')" v-model:value="phoneFormData.smscode" />
                      <div v-if="showInterval" class="sms-code-btn" @click="getLoginCode">
                        <a>{{ t('component.countdown.normalText') }}</a>
                      </div>
                      <div v-else class="sms-code-btn disabled">
                        <span>{{ t('component.countdown.sendText', [unref(timeRuning)]) }}</span>
                      </div>
                    </div>
                  </div>
                </a-form>
              </div>

              <!-- 科技感登录按钮 -->
              <div class="tech-login-button">
                <a-button
                  :loading="loginLoading"
                  class="tech-btn-login"
                  type="primary"
                  @click="loginHandleClick"
                >
                  <span class="btn-text">{{ t('sys.login.loginButton') }}</span>
                  <div class="btn-glow"></div>
                </a-button>
              </div>

              <!-- 科技感公司信息 -->
              <div class="tech-company-info">
                <div class="company-name">贵州通用信创科技有限公司</div>
                <div class="company-contact">联系电话：13885926338</div>
                <div class="tech-decoration-line"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div v-show="type === 'forgot'" :class="`${prefixCls}-form`">
      <MiniForgotpad ref="forgotRef" @go-back="goBack" @success="handleSuccess" />
    </div>
    <div v-show="type === 'register'" :class="`${prefixCls}-form`">
      <MiniRegister ref="registerRef" @go-back="goBack" @success="handleSuccess" />
    </div>
    <div v-show="type === 'codeLogin'" :class="`${prefixCls}-form`">
      <MiniCodelogin ref="codeRef" @go-back="goBack" @success="handleSuccess" />
    </div> -->
    <!-- 第三方登录相关弹框 -->
    <!-- <ThirdModal ref="thirdModalRef"></ThirdModal> -->
    
    <!-- 图片验证码弹窗 -->
    <CaptchaModal @register="captchaRegisterModal" @ok="getLoginCode" />
  </div>
</template>
<script lang="ts" setup name="login-mini">
  import { getCaptcha, getCodeInfo } from '/@/api/sys/user';
  import { onMounted, reactive, ref, toRaw, unref } from 'vue';
  import codeImg from '/@/assets/images/checkcode.png';
  import { useUserStore } from '/@/store/modules/user';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { SmsEnum } from '/@/views/sys/login/useLogin';
  import { AppLocalePicker, AppDarkModeToggle } from '/@/components/Application';
  import { useDesign } from "/@/hooks/web/useDesign";
  import { useAppInject } from "/@/hooks/web/useAppInject";
  import CaptchaModal from '@/components/jeecg/captcha/CaptchaModal.vue';
  import { useModal } from "@/components/Modal";
  import { ExceptionEnum } from "@/enums/exceptionEnum";

  const { prefixCls } = useDesign('mini-login');
  const { notification, createMessage } = useMessage();
  const userStore = useUserStore();
  const { t } = useI18n();
  const randCodeData = reactive<any>({
    randCodeImage: '',
    requestCodeSuccess: false,
    checkKey: null,
  });
  //手机号登录还是账号登录
  const activeIndex = ref<string>('accountLogin');
  const type = ref<string>('login');
  //账号登录表单字段
  const formData = reactive<any>({
    inputCode: '',
    username: '',
    password: '',
  });
  //手机登录表单字段
  const phoneFormData = reactive<any>({
    mobile: '',
    smscode: '',
  });
  const loginRef = ref();
  //是否显示获取验证码
  const showInterval = ref<boolean>(true);
  //60s
  const timeRuning = ref<number>(60);
  //定时器
  const timer = ref<any>(null);
  const loginLoading = ref<boolean>(false);
  const { getIsMobile } = useAppInject();
  const [captchaRegisterModal, { openModal: openCaptchaModal }] = useModal();
  defineProps({
    sessionTimeout: {
      type: Boolean,
    },
  });

  /**
   * 获取验证码
   */
  function handleChangeCheckCode() {
    formData.inputCode = '';
    //update-begin---author:chenrui ---date:2025/1/7  for：[QQYUN-10775]验证码可以复用 #7674------------
    randCodeData.checkKey = new Date().getTime() + Math.random().toString(36).slice(-4); // *************;
    //update-end---author:chenrui ---date:2025/1/7  for：[QQYUN-10775]验证码可以复用 #7674------------
    getCodeInfo(randCodeData.checkKey).then((res) => {
      randCodeData.randCodeImage = res;
      randCodeData.requestCodeSuccess = true;
    });
  }



  /**
   * 账号或者手机登录
   */
  async function loginHandleClick() {
    if (unref(activeIndex) === 'accountLogin') {
      accountLogin();
    } else {
      //手机号登录
      phoneLogin();
    }
  }

  async function accountLogin() {
    if (!formData.username) {
      createMessage.warn(t('sys.login.accountPlaceholder'));
      return;
    }
    if (!formData.password) {
      createMessage.warn(t('sys.login.passwordPlaceholder'));
      return;
    }
    try {
      loginLoading.value = true;
      const userInfo = await userStore.login(
        toRaw({
          password: formData.password,
          username: formData.username,
          captcha: formData.inputCode,
          checkKey: randCodeData.checkKey,
          mode: 'none', //不要默认的错误提示
        })
      );
      if (userInfo) {
        notification.success({
          message: t('sys.login.loginSuccessTitle'),
          description: `${t('sys.login.loginSuccessDesc')}: ${userInfo.realname}`,
          duration: 3,
        });
      }
    } catch (error: any) {
      notification.error({
        message: t('sys.api.errorTip'),
        description: error.message || t('sys.login.networkExceptionMsg'),
        duration: 3,
      });
      handleChangeCheckCode();
    } finally {
      loginLoading.value = false;
    }
  }

  /**
   * 手机号登录
   */
  async function phoneLogin() {
    if (!phoneFormData.mobile) {
      createMessage.warn(t('sys.login.mobilePlaceholder'));
      return;
    }
    if (!phoneFormData.smscode) {
      createMessage.warn(t('sys.login.smsPlaceholder'));
      return;
    }
    try {
      loginLoading.value = true;
      const userInfo: any = await userStore.phoneLogin({
        mobile: phoneFormData.mobile,
        captcha: phoneFormData.smscode,
        mode: 'none', //不要默认的错误提示
      } as any);
      if (userInfo) {
        notification.success({
          message: t('sys.login.loginSuccessTitle'),
          description: `${t('sys.login.loginSuccessDesc')}: ${userInfo.realname}`,
          duration: 3,
        });
      }
    } catch (error: any) {
      notification.error({
        message: t('sys.api.errorTip'),
        description: error.message || t('sys.login.networkExceptionMsg'),
        duration: 3,
      });
    } finally {
      loginLoading.value = false;
    }
  }

  /**
   * 获取手机验证码
   */
  async function getLoginCode() {
    if (!phoneFormData.mobile) {
      createMessage.warn(t('sys.login.mobilePlaceholder'));
      return;
    }
    //update-begin---author:wangshuai---date:2024-04-18---for:【QQYUN-9005】同一个IP，1分钟超过5次短信，则提示需要验证码---
    const result = await getCaptcha({ mobile: phoneFormData.mobile, smsmode: SmsEnum.FORGET_PASSWORD }).catch((res) =>{
      if(res.code === ExceptionEnum.PHONE_SMS_FAIL_CODE){
        openCaptchaModal(true, {});
      }
    });
    //update-end---author:wangshuai---date:2024-04-18---for:【QQYUN-9005】同一个IP，1分钟超过5次短信，则提示需要验证码---
    if (result) {
      const TIME_COUNT = 60;
      if (!unref(timer)) {
        timeRuning.value = TIME_COUNT;
        showInterval.value = false;
        timer.value = setInterval(() => {
          if (unref(timeRuning) > 0 && unref(timeRuning) <= TIME_COUNT) {
            timeRuning.value = timeRuning.value - 1;
          } else {
            showInterval.value = true;
            clearInterval(unref(timer));
            timer.value = null;
          }
        }, 1000);
      }
    }
  }



  onMounted(() => {
    //加载验证码
    handleChangeCheckCode();
  });
</script>

<style lang="less" scoped>
  @import '/@/assets/loginmini/style/base.less';

  // 科技感登录容器
  .tech-login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0f1419 100%);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
      pointer-events: none;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(120,219,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
      opacity: 0.3;
      pointer-events: none;
    }
  }

  // 科技感标题区域
  .tech-header {
    position: absolute;
    top: 8%;
    left: 8%;
    z-index: 10;

    .tech-title-container {
      .main-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: #ffffff;
        margin: 0;
        text-shadow: 0 0 20px rgba(120, 219, 255, 0.5);
        letter-spacing: 2px;
      }

      .sub-title {
        font-size: 1.2rem;
        font-weight: 400;
        color: #78dbff;
        margin: 8px 0 0 0;
        letter-spacing: 1px;
        opacity: 0.9;
      }

      .title-decoration {
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, #78dbff, #ff77c6);
        margin-top: 15px;
        border-radius: 2px;
        box-shadow: 0 0 10px rgba(120, 219, 255, 0.5);
      }
    }
  }

  // 移动端标题
  .tech-mobile-header {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;

    .mobile-main-title {
      font-size: 1.5rem;
      font-weight: 700;
      color: #ffffff;
      margin: 0;
      text-shadow: 0 0 15px rgba(120, 219, 255, 0.5);
    }

    .mobile-sub-title {
      font-size: 0.9rem;
      color: #78dbff;
      margin: 4px 0 0 0;
      opacity: 0.9;
    }
  }

  // 科技感内容区域
  .tech-content {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 40px 20px;
    position: relative;
    z-index: 5;
  }

  // 科技感登录卡片
  .tech-login-card {
    background: rgba(15, 20, 25, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(120, 219, 255, 0.2);
    border-radius: 16px;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(120, 219, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    width: 100%;
    max-width: 420px;
    overflow: hidden;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, #78dbff, transparent);
      opacity: 0.6;
    }
  }

  // 表单容器
  .tech-form-container {
    padding: 40px 35px;
  }

  // 表单头部
  .tech-form-header {
    text-align: center;
    margin-bottom: 35px;

    .login-title {
      font-size: 1.8rem;
      font-weight: 600;
      color: #ffffff;
      margin-bottom: 8px;
      text-shadow: 0 0 15px rgba(120, 219, 255, 0.3);
    }

    .login-subtitle {
      font-size: 0.9rem;
      color: #78dbff;
      opacity: 0.8;
      letter-spacing: 2px;
      font-weight: 300;
    }
  }

  // 科技感输入组
  .tech-input-group {
    .tech-input-item {
      position: relative;
      margin-bottom: 20px;

      .input-icon {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 2;

        .icon {
          width: 18px;
          height: 18px;
          opacity: 0.6;
          filter: brightness(0) invert(1);
        }
      }

      :deep(.ant-form-item) {
        margin-bottom: 0;
      }

      .tech-input {
        height: 50px;
        background: rgba(26, 35, 50, 0.8);
        border: 1px solid rgba(120, 219, 255, 0.2);
        border-radius: 8px;
        padding-left: 45px;
        color: #ffffff;
        font-size: 14px;
        transition: all 0.3s ease;

        &::placeholder {
          color: rgba(255, 255, 255, 0.4);
        }

        &:hover {
          border-color: rgba(120, 219, 255, 0.4);
          box-shadow: 0 0 0 2px rgba(120, 219, 255, 0.1);
        }

        &:focus {
          border-color: #78dbff;
          box-shadow:
            0 0 0 2px rgba(120, 219, 255, 0.2),
            0 0 15px rgba(120, 219, 255, 0.1);
          background: rgba(26, 35, 50, 0.9);
        }
      }

      // 验证码特殊样式
      &.captcha-item {
        .captcha-input {
          padding-right: 130px;
        }

        .captcha-code {
          position: absolute;
          right: 8px;
          top: 50%;
          transform: translateY(-50%);

          .captcha-img {
            height: 34px;
            border-radius: 4px;
            cursor: pointer;
            border: 1px solid rgba(120, 219, 255, 0.3);
            transition: all 0.3s ease;

            &:hover {
              border-color: #78dbff;
              box-shadow: 0 0 8px rgba(120, 219, 255, 0.3);
            }
          }
        }
      }
    }
  }

  // 短信验证码按钮
  .sms-code-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    padding: 8px 16px;
    background: linear-gradient(135deg, #78dbff, #ff77c6);
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    color: #ffffff;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 0 12px rgba(120, 219, 255, 0.4);
      transform: translateY(-50%) scale(1.05);
    }

    &.disabled {
      background: rgba(120, 219, 255, 0.3);
      cursor: not-allowed;

      &:hover {
        transform: translateY(-50%);
        box-shadow: none;
      }
    }
  }

  // 科技感登录按钮
  .tech-login-button {
    margin: 30px 0 25px 0;

    .tech-btn-login {
      width: 100%;
      height: 50px;
      background: linear-gradient(135deg, #78dbff 0%, #ff77c6 100%);
      border: none;
      border-radius: 8px;
      position: relative;
      overflow: hidden;
      font-size: 16px;
      font-weight: 600;
      letter-spacing: 1px;
      transition: all 0.3s ease;

      .btn-text {
        position: relative;
        z-index: 2;
        color: #ffffff;
        text-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
      }

      .btn-glow {
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow:
          0 8px 25px rgba(120, 219, 255, 0.3),
          0 0 0 1px rgba(120, 219, 255, 0.2);

        .btn-glow {
          left: 100%;
        }
      }

      &:active {
        transform: translateY(0);
      }

      &:focus {
        box-shadow:
          0 8px 25px rgba(120, 219, 255, 0.3),
          0 0 0 2px rgba(120, 219, 255, 0.4);
      }
    }
  }

  // 科技感公司信息
  .tech-company-info {
    text-align: center;
    margin-top: 25px;

    .company-name {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 5px;
      font-weight: 500;
    }

    .company-contact {
      font-size: 12px;
      color: rgba(120, 219, 255, 0.7);
      margin-bottom: 15px;
    }

    .tech-decoration-line {
      width: 80px;
      height: 1px;
      background: linear-gradient(90deg, transparent, #78dbff, transparent);
      margin: 0 auto;
      opacity: 0.6;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .tech-header {
      position: relative;
      top: auto;
      left: auto;
      text-align: center;
      padding: 20px;

      .tech-title-container {
        .main-title {
          font-size: 2rem;
        }

        .sub-title {
          font-size: 1rem;
        }
      }
    }

    .tech-content {
      padding: 20px 15px;
    }

    .tech-login-card {
      margin-top: 0;
    }

    .tech-form-container {
      padding: 30px 25px;
    }
  }

  @media (max-width: 480px) {
    .tech-header {
      .tech-title-container {
        .main-title {
          font-size: 1.6rem;
        }

        .sub-title {
          font-size: 0.9rem;
        }
      }
    }

    .tech-form-container {
      padding: 25px 20px;
    }

    .tech-input-group {
      .tech-input-item {
        &.captcha-item {
          .captcha-input {
            padding-right: 110px;
          }
        }
      }
    }
  }

  :deep(.ant-input:focus) {
    box-shadow: none;
  }

  :deep(.jeecg-dark-switch){
    position:absolute;
    margin-right: 10px;
  }

  .top-3{
    top: 0.45rem;
  }
</style>

<style lang="less">
@prefix-cls: ~'@{namespace}-mini-login';
@dark-bg: #293146;

html[data-theme='dark'] {
  .@{prefix-cls} {
    // 暗色主题下保持科技感设计
    .tech-login-container {
      background: linear-gradient(135deg, #0a0f1a 0%, #151b26 50%, #0d1117 100%);

      &::before {
        background-image:
          radial-gradient(circle at 20% 80%, rgba(100, 99, 178, 0.4) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(235, 99, 178, 0.2) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(100, 199, 235, 0.15) 0%, transparent 50%);
      }
    }

    .tech-login-card {
      background: rgba(10, 15, 20, 0.95);
      border-color: rgba(100, 199, 235, 0.25);
      box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(100, 199, 235, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    }

    .tech-input-group {
      .tech-input-item {
        .tech-input {
          background: rgba(16, 21, 30, 0.9);
          border-color: rgba(100, 199, 235, 0.25);
          color: #e6edf3;

          &::placeholder {
            color: rgba(230, 237, 243, 0.4);
          }

          &:hover {
            border-color: rgba(100, 199, 235, 0.5);
            box-shadow: 0 0 0 2px rgba(100, 199, 235, 0.15);
          }

          &:focus {
            border-color: #64c7eb;
            box-shadow:
              0 0 0 2px rgba(100, 199, 235, 0.25),
              0 0 15px rgba(100, 199, 235, 0.15);
            background: rgba(16, 21, 30, 0.95);
          }
        }
      }
    }

    .tech-company-info {
      .company-name {
        color: rgba(230, 237, 243, 0.8);
      }

      .company-contact {
        color: rgba(100, 199, 235, 0.7);
      }

      .tech-decoration-line {
        background: linear-gradient(90deg, transparent, #64c7eb, transparent);
      }
    }

    .app-iconify {
      color: #fff !important;
    }

    .ant-checkbox-inner {
      border-color: #64c7eb;
    }
  }

  input.fix-auto-fill,
  .fix-auto-fill input {
    -webkit-text-fill-color: #e6edf3 !important;
    box-shadow: inherit !important;
  }

  .ant-divider-inner-text {
    font-size: 12px !important;
    color: @text-color-secondary !important;
  }
}
</style>
