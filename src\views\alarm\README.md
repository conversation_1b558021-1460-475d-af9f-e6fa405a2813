# 报警详情功能实现

## 功能概述

在报警管理列表的操作列中添加了"详情"按钮，点击后弹出报警详情弹窗，展示完整的报警信息、监控截图和视频监控。

## 实现的功能

### 1. 报警详情弹窗 (AlarmDetailModal.vue)

- **科技感设计**: 采用深色主题，渐变背景，科技感十足的UI设计
- **报警信息展示**: 完整显示设备信息、报警时间、处理状态等
- **监控截图**: 网格布局展示相关监控截图，支持点击预览
- **视频监控**: 实时视频播放，支持多路视频同时显示
- **响应式设计**: 适配不同屏幕尺寸

### 2. 操作列增强 (AlarmList.vue)

- 在操作列添加"详情"按钮
- 调整操作列宽度以容纳新按钮
- 保持原有的"处理"按钮功能

### 3. API接口扩展 (Alarm.api.ts)

- 添加详情查询接口 `/alarm/alarm/detail`
- 支持根据报警ID获取详细信息

## 文件结构

```
src/views/alarm/
├── AlarmList.vue                    # 报警列表页面（已修改）
├── Alarm.api.ts                     # API接口（已扩展）
├── Alarm.data.ts                    # 数据配置
├── AlarmDetailTest.vue              # 功能测试页面（新增）
├── README.md                        # 说明文档（新增）
└── components/
    ├── AlarmDetailModal.vue         # 报警详情弹窗（新增）
    ├── AlarmModal.vue               # 原有模态框
    ├── AlarmHandleModal.vue         # 处理模态框
    └── AlarmForm.vue                # 表单组件
```

## 路由配置

在 `src/router/routes/modules/patrol.ts` 中添加了以下路由：

1. **报警管理**: `/patrol/alarms` - 报警列表页面
2. **测试页面**: `/patrol/alarm-detail-test` - 功能演示页面

## 使用方法

### 1. 在报警列表中使用

```vue
<template>
  <!-- 在表格操作列中 -->
  <TableAction :actions="getTableAction(record)" />
  
  <!-- 引入详情弹窗组件 -->
  <AlarmDetailModal ref="registerDetailModal" @success="handleSuccess" />
</template>

<script setup>
import AlarmDetailModal from './components/AlarmDetailModal.vue';

const registerDetailModal = ref();

function handleViewDetail(record) {
  registerDetailModal.value.showModal(record);
}
</script>
```

### 2. 独立使用详情弹窗

```vue
<template>
  <AlarmDetailModal ref="detailModalRef" />
</template>

<script setup>
import AlarmDetailModal from '@/views/alarm/components/AlarmDetailModal.vue';

const detailModalRef = ref();

function showDetail(alarmData) {
  detailModalRef.value.showModal(alarmData);
}
</script>
```

## 样式特点

- **科技感配色**: 深蓝色渐变背景，高对比度文字
- **动画效果**: 脉冲动画的报警图标，悬停效果
- **网格布局**: 响应式网格展示截图和视频
- **卡片设计**: 信息分组展示，层次清晰

## 数据格式

### 报警详情数据结构

```typescript
interface AlarmDetail {
  id: string;
  deviceNum: string;        // 设备编号
  deviceName: string;       // 设备名称
  lineName: string;         // 所属路线
  alarmTime: string;        // 报警时间
  status: string;           // 状态 (0-未处理, 1-已处理)
  handleUserName: string;   // 处理人
  handleTime: string;       // 处理时间
  opinion: string;          // 处理意见
}
```

### 监控截图数据结构

```typescript
interface Screenshot {
  id: string;
  name: string;             // 截图名称
  url: string;              // 图片URL
  captureTime: string;      // 截图时间
}
```

### 视频监控数据结构

```typescript
interface VideoMonitor {
  id: string;
  name: string;             // 监控点名称
  videoUrl: string;         // 视频URL
  status: string;           // 状态 (online/offline)
}
```

## 测试访问

启动项目后，可以通过以下路径访问：

1. **报警管理**: `/patrol/alarms`
2. **功能测试**: `/patrol/alarm-detail-test`

在测试页面中可以体验完整的报警详情弹窗功能。
