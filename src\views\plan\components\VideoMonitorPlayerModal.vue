<template>
  <div class="video-monitor-player-modal">
    <!-- 视频播放区域 -->
    <div class="video-player-wrapper">
      <div class="video-header">
        <div class="video-title">
          <h5>{{ videoInfo?.name || '视频监控' }}</h5>
          <a-tag v-if="videoInfo?.streamType" :color="getStreamTypeColor(videoInfo.streamType)" size="small">
            {{ getStreamTypeText(videoInfo.streamType) }}
          </a-tag>
        </div>
        <div class="video-controls" v-if="showControls">
          <a-button
              type="primary"
              @click="startVideo"
              :loading="starting"
              :disabled="isPlaying"
              size="small"
              title="开始播放"
          >
            <PlayCircleOutlined />
            <!-- {{ starting ? '启动中...' : '播放' }} -->
          </a-button>
          <a-button
              @click="stopVideo"
              :disabled="!isPlaying && !starting"
              size="small"
              title="停止播放"
          >
            <PauseCircleOutlined />
            <!-- 停止 -->
          </a-button>
          <a-button
              @click="refreshVideo"
              :disabled="starting"
              size="small"
              title="刷新视频"
          >
            <ReloadOutlined />
            <!-- 刷新 -->
          </a-button>
          <a-button
              @click="captureFrame"
              :disabled="!isPlaying || capturing"
              :loading="capturing"
              size="small"
              title="截图"
          >
            <CameraOutlined />
            <!-- {{ capturing ? '截图中...' : '截图' }} -->
          </a-button>
        </div>
      </div>

      <div class="video-content">
        <!-- 视频播放区域 -->
        <div class="video-player-area">
          <!-- 加载状态 - 只在启动时显示，不与播放器同时显示 -->
          <div v-if="starting && !isPlaying" class="video-loading">
            <a-spin size="large">
              <template #indicator>
                <LoadingOutlined style="font-size: 24px" spin />
              </template>
            </a-spin>
            <p>正在连接视频流...</p>
          </div>

          <!-- 优先使用WebRTC播放器 -->
          <div v-else-if="props.videoInfo.rtspUrl || props.videoInfo.rtspVideo || props.videoInfo.videoUrl" class="webrtc-rtsp-wrapper">
            <video
                ref="rtspVideoElement"
                id="rtspVideo"
                class="video-player"
                controls
                autoplay
                muted
                width="100%"
                height="100%"
                @play="onWebRtcPlay"
                @pause="onWebRtcPause"
                @error="onWebRtcError"
                @loadstart="onWebRtcLoadStart"
                @loadeddata="onWebRtcLoaded"
            >
              您的浏览器不支持WebRTC视频播放
            </video>

            <!-- WebRTC视频覆盖层 -->
            <div class="video-overlay" v-if="!isPlaying && !starting">
              <div class="play-button" @click="startVideo">
                <PlayCircleOutlined />
                <span class="play-text">点击播放</span>
              </div>
            </div>
          </div>



          <!-- 视频播放器（支持MP4等格式） -->
          <div v-else-if="!starting && videoFormat === 'mp4'" class="video-wrapper">
            <video
                ref="videoElement"
                class="video-player"
                controls
                autoplay
                muted
                :poster="videoPoster"
                @loadstart="onVideoLoadStart"
                @loadeddata="onVideoLoaded"
                @loadedmetadata="onVideoMetadataLoaded"
                @error="onVideoError"
                @play="onVideoPlay"
                @pause="onVideoPause"
                @ended="onVideoEnded"
                @waiting="onVideoWaiting"
                @canplay="onVideoCanPlay"
                @stalled="onVideoStalled"
            >
              您的浏览器不支持视频播放
            </video>

            <!-- 视频覆盖层 - 只在未播放且未启动时显示 -->
            <div class="video-overlay" v-if="!isPlaying && !starting">
              <div class="play-button" @click="startVideo">
                <PlayCircleOutlined />
                <span class="play-text">点击播放</span>
              </div>
            </div>
          </div>

          <!-- WebSocket视频流（备用方案） -->
          <div v-else class="video-wrapper">
            <canvas
                ref="canvasElement"
                class="video-canvas"
                :width="canvasWidth"
                :height="canvasHeight"
            ></canvas>

            <!-- Canvas覆盖层 -->
            <div class="video-overlay" v-if="!isPlaying && !starting">
              <div class="play-button" @click="startVideo">
                <PlayCircleOutlined />
              </div>
            </div>
          </div>
        </div>

        <!-- 视频信息栏 - 独立于播放区域，始终显示在底部 -->
        <div class="video-info-bar" v-if="showControls">
          <div class="status-indicators">
            <a-tag :color="isPlaying ? 'green' : 'red'" size="small">
              {{ isPlaying ? '播放中' : '已停止' }}
            </a-tag>
            <a-tag :color="wsConnected ? 'green' : 'red'" size="small">
              {{ wsConnected ? '已连接' : '未连接' }}
            </a-tag>
            <a-tag :color="getVideoFormatColor(videoFormat)" size="small">
              {{ getVideoFormatText(videoFormat) }}
            </a-tag>

          </div>
        </div>
      </div>
    </div>

    <!-- 成功信息显示 -->
    <a-alert
        v-if="successMessage"
        :message="successMessage"
        type="success"
        closable
        @close="successMessage = ''"
        style="margin-top: 8px"
        size="small"
    />

    <!-- 错误信息显示 -->
    <a-alert
        v-if="errorMessage"
        :message="errorMessage"
        type="error"
        closable
        @close="errorMessage = ''"
        style="margin-top: 8px"
        size="small"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  CameraOutlined,
  LoadingOutlined
} from '@ant-design/icons-vue';




interface VideoInfo {
  id: string;
  name: string;
  videoUrl: string;
  streamId: string;
  websocketUrl: string;
  rtspUrl?: string;
  rtspVideo?: string;
  streamType?: string;
}

interface Props {
  planId: string;
  videoInfo: VideoInfo;
  autoStart?: boolean;
  showControls?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  autoStart: false,
  showControls: true
});

// 响应式数据
const videoElement = ref<HTMLVideoElement>();
const canvasElement = ref<HTMLCanvasElement>();
const rtspVideoElement = ref<HTMLVideoElement>();
const isPlaying = ref(false);
const starting = ref(false);
const wsConnected = ref(false);
const errorMessage = ref('');
const successMessage = ref('');
const capturing = ref(false);



const webRtcServer = ref<any>(null); // WebRTC服务器实例
let reconnectTimer: NodeJS.Timeout | null = null;
let waitingTimeout: NodeJS.Timeout | null = null;
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;
let healthCheckTimer: NodeJS.Timeout | null = null;

// 计算属性
const videoFormat = computed(() => {
  // 检查 videoUrl
  if (!props.videoInfo?.videoUrl) return 'unknown';
  const url = props.videoInfo.videoUrl.toLowerCase();
  if (url.includes('.mp4')) return 'mp4';
  if (url.includes('rtmp://')) return 'rtmp';
  if (url.includes('rtsp://')) return 'rtsp';
  return 'unknown';
});
const canvasWidth = computed(() => 320);
const canvasHeight = computed(() => 240);
const videoPoster = computed(() => '/images/video-placeholder.jpg');

// 监听props变化
watch(() => props.videoInfo, (newInfo) => {
  if (newInfo && isPlaying.value) {
    stopVideo();
  }
  if (newInfo && props.autoStart) {
    nextTick(() => {
      startVideo();
    });
  }
}, { deep: true });

// 组件挂载
onMounted(() => {
  initializePlayer();



  if (props.autoStart && props.videoInfo) {
    startVideo();
  }
});

// 组件卸载
onUnmounted(() => {
  console.log('VideoMonitorPlayerModal 组件卸载，开始清理资源');

  // 销毁WebRTC实例
  if (webRtcServer.value) {
    console.log('销毁WebRTC实例');
    try {
      webRtcServer.value.disconnect();
    } catch (error) {
      console.error('销毁WebRTC实例时出错:', error);
    }
    webRtcServer.value = null;
  }

  cleanup();
});

/**
 * 初始化播放器
 */
function initializePlayer() {
  if (!props.videoInfo) {
    errorMessage.value = '视频信息未提供';
    return;
  }

  // 系统WebSocket已经在useWebSocket中初始化，这里不需要额外初始化
  wsConnected.value = true; // 假设系统WebSocket已连接
}



/**
 * 开始播放视频
 */
async function startVideo() {
  if (!props.videoInfo?.videoUrl && !props.videoInfo?.streamId) {
    errorMessage.value = '视频源地址或流ID未设置';
    console.error('视频源地址或流ID未设置');
    return;
  }

  if (isPlaying.value) {
    console.log('视频已在播放中');
    return;
  }

  starting.value = true;
  errorMessage.value = '';

  console.log('开始播放视频:', {
    videoUrl: props.videoInfo.videoUrl,
    streamId: props.videoInfo.streamId
  });

  try {
    // 首先停止任何现有的播放
    if (webRtcServer.value) {
      webRtcServer.value.disconnect();
      webRtcServer.value = null;
    }

    // 根据视频格式选择播放方式，优先使用WebRTC
    const format = videoFormat.value;
    console.log('检测到视频格式:', format, '优先级: WebRTC > MP4 > WebSocket');

    // 优先使用WebRTC播放器
    if (props.videoInfo.rtspUrl || props.videoInfo.videoUrl) {
      console.log('使用WebRTC播放器播放视频');
      await startWebRtcVideo();
    } else if (format === 'mp4') {
      console.log('使用原生播放器播放MP4视频');
      await startNativeVideo();
    } else {
      console.log('使用WebSocket播放视频');
      await startWebSocketVideo();
    }
  } catch (error: any) {
    console.error('启动视频失败:', error);
    errorMessage.value = '启动视频失败: ' + (error?.message || error);
    starting.value = false;
    console.error('启动视频失败: ' + (error?.message || error));
  }
}

/**
 * 启动WebRTC RTSP视频播放
 */
async function startWebRtcVideo() {
  if (!rtspVideoElement.value) {
    throw new Error('WebRTC视频元素未找到');
  }

  try {
    // 确定要播放的视频流地址
    let streamUrl = '';

    // 优先使用RTSP Video，然后是video URL
    if (props.videoInfo.rtspVideo) {
      streamUrl = props.videoInfo.rtspVideo;
      console.log('使用RTSP Video作为WebRTC流地址');
    } else if (props.videoInfo.videoUrl) {
      streamUrl = props.videoInfo.videoUrl;
      console.log('使用Video URL作为WebRTC流地址');
    } else {
      throw new Error('未找到可用的视频流地址');
    }

    console.log('开始启动WebRTC视频播放:', {
      streamUrl: streamUrl,
      videoFormat: videoFormat.value,
      rtspUrl: props.videoInfo.rtspUrl,
      videoUrl: props.videoInfo.videoUrl
    });

    // 检查WebRtcStreamer是否可用
    if (typeof (window as any).WebRtcStreamer === 'undefined') {
      throw new Error('WebRtcStreamer未加载，请确保webrtcstreamer.js已正确引入。请在index.html中添加script标签引入webrtcstreamer.js');
    }

    // 创建WebRTC服务器实例
    const WebRtcStreamerClass = (window as any).WebRtcStreamer;
    webRtcServer.value = new WebRtcStreamerClass('rtspVideo', "http://************:8000");

    // 连接到视频流
    webRtcServer.value.connect(streamUrl);

    console.log('WebRTC连接成功，流地址:', streamUrl);

    isPlaying.value = true;
    starting.value = false;
    clearReconnectTimer();
    startHealthCheck();

  } catch (error: any) {
    console.error('WebRTC RTSP播放失败:', error);
    errorMessage.value = 'WebRTC RTSP播放失败: ' + (error?.message || error);
    starting.value = false;
    throw error;
  }
}

/**
 * 启动原生视频播放（MP4等）
 */
async function startNativeVideo() {
  if (!videoElement.value) {
    throw new Error('视频元素未找到');
  }

  try {
    console.log('开始启动原生视频播放:', {
      videoUrl: props.videoInfo.videoUrl,
      streamId: props.videoInfo.streamId
    });

    // 设置视频源
    videoElement.value.src = props.videoInfo.videoUrl;

    // 开始播放
    await videoElement.value.play();

    isPlaying.value = true;
    starting.value = false;
    clearReconnectTimer();
    startHealthCheck();

    console.log('原生视频播放成功');

  } catch (error: any) {
    console.error('原生视频播放失败:', error);
    errorMessage.value = '原生视频播放失败: ' + (error?.message || error);
    starting.value = false;
    throw error;
  }
}

/**
 * 启动WebSocket视频播放
 */
async function startWebSocketVideo() {
  console.log('WebSocket视频播放暂不支持，请使用WebRTC或MP4格式');
  throw new Error('WebSocket视频播放暂不支持');
}

/**
 * 停止视频播放
 */
async function stopVideo() {
  console.log('停止视频播放:', props.videoInfo.streamId);

  // 立即重置状态，避免显示加载转圈
  isPlaying.value = false;
  starting.value = false;
  errorMessage.value = '';

  try {
    // 停止健康检查和定时器
    stopHealthCheck();
    clearReconnectTimer();

    // 停止WebRTC播放
    if (webRtcServer.value) {
      console.log('断开WebRTC连接');
      try {
        webRtcServer.value.disconnect();
        webRtcServer.value = null;
      } catch (error) {
        console.error('断开WebRTC连接失败:', error);
        webRtcServer.value = null;
      }
    }

    // 停止视频元素
    if (videoElement.value) {
      console.log('停止视频元素');
      videoElement.value.pause();
      videoElement.value.src = '';
      videoElement.value.load(); // 重置视频元素
    }

    // 停止WebRTC视频元素
    if (rtspVideoElement.value) {
      console.log('停止WebRTC视频元素');
      rtspVideoElement.value.pause();
      rtspVideoElement.value.src = '';
      rtspVideoElement.value.load(); // 重置视频元素
    }

    console.log('视频播放已停止');

  } catch (error) {
    console.error('停止视频失败:', error);
    // 确保即使出错也重置状态
    isPlaying.value = false;
    starting.value = false;
    errorMessage.value = '';
  }
}

/**
 * 刷新视频
 */
async function refreshVideo() {
  console.log('刷新视频...');

  try {
    // 停止当前播放
    await stopVideo();

    // 等待一段时间确保资源完全释放
    await new Promise(resolve => setTimeout(resolve, 1500));

    // 重新启动播放
    await startVideo();

    console.log('视频已刷新');
  } catch (error: any) {
    console.error('刷新视频失败:', error);
    errorMessage.value = '刷新视频失败: ' + (error?.message || error);
    console.error('刷新视频失败');
  }
}

/**
 * 截图功能 - 支持WebRTC和原生视频
 */
async function captureFrame() {
  if (!isPlaying.value) {
    errorMessage.value = '请先播放视频再进行截图';
    console.warn('视频未播放，无法截图');
    return;
  }

  if (capturing.value) {
    console.warn('截图正在进行中，请稍候');
    return;
  }

  capturing.value = true;
  errorMessage.value = '';
  successMessage.value = '';

  try {
    let videoEl: HTMLVideoElement | null = null;
    let screenshotName = '';
    let videoType = '';

    // 优先使用WebRTC视频元素
    if (rtspVideoElement.value && (props.videoInfo.rtspUrl || props.videoInfo.rtspVideo || props.videoInfo.videoUrl)) {
      videoEl = rtspVideoElement.value;
      videoType = 'WebRTC';
      screenshotName = `webrtc_screenshot_${Date.now()}.png`;
      console.log('使用WebRTC视频元素进行截图');
    }
    // 其次使用原生视频元素
    else if (videoElement.value) {
      videoEl = videoElement.value;
      videoType = '原生视频';
      screenshotName = `video_screenshot_${Date.now()}.png`;
      console.log('使用原生视频元素进行截图');
    }

    if (!videoEl) {
      throw new Error('未找到可用的视频元素');
    }

    // 等待一小段时间确保视频帧稳定
    await new Promise(resolve => setTimeout(resolve, 100));

    // 检查视频是否有有效的尺寸
    if (videoEl.videoWidth === 0 || videoEl.videoHeight === 0) {
      throw new Error(`视频尺寸无效 (${videoEl.videoWidth}x${videoEl.videoHeight})`);
    }

    // 创建canvas进行截图
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      throw new Error('无法创建Canvas上下文');
    }

    // 设置canvas尺寸
    canvas.width = videoEl.videoWidth;
    canvas.height = videoEl.videoHeight;

    console.log('截图信息:', {
      videoType,
      videoWidth: videoEl.videoWidth,
      videoHeight: videoEl.videoHeight,
      canvasWidth: canvas.width,
      canvasHeight: canvas.height
    });

    // 绘制视频帧到canvas
    ctx.drawImage(videoEl, 0, 0, canvas.width, canvas.height);

    // 转换为图片数据
    const dataUrl = canvas.toDataURL('image/png', 0.9);

    // 验证图片数据
    if (!dataUrl || dataUrl === 'data:,') {
      throw new Error('无法生成截图数据');
    }

    // 创建下载链接
    const link = document.createElement('a');
    link.download = screenshotName;
    link.href = dataUrl;
    link.style.display = 'none';

    // 添加到DOM并触发下载
    document.body.appendChild(link);
    link.click();

    // 清理DOM
    setTimeout(() => {
      document.body.removeChild(link);
    }, 100);

    console.log('截图成功:', screenshotName);
    successMessage.value = `截图成功！文件已保存为 ${screenshotName}`;

    // 3秒后清除成功消息
    setTimeout(() => {
      successMessage.value = '';
    }, 3000);

  } catch (error) {
    console.error('截图失败:', error);
    errorMessage.value = '截图失败: ' + (error instanceof Error ? error.message : '未知错误');
  } finally {
    capturing.value = false;
  }
}

/**
 * 获取流类型颜色
 */
function getStreamTypeColor(type: string) {
  switch (type) {
    case 'playback': return 'blue';
    case 'preview': return 'green';
    default: return 'default';
  }
}

/**
 * 获取流类型文本
 */
function getStreamTypeText(type: string) {
  switch (type) {
    case 'playback': return '回放流';
    case 'preview': return '预览流';
    default: return '未知';
  }
}

/**
 * 获取视频格式颜色
 */
function getVideoFormatColor(format: string) {
  switch (format) {
    case 'mp4': return 'green';
    case 'rtmp': return 'purple';
    case 'rtsp': return 'cyan';
    default: return 'default';
  }
}

/**
 * 获取视频格式文本
 */
function getVideoFormatText(format: string) {
  switch (format) {
    case 'mp4': return 'MP4';
    case 'rtmp': return 'RTMP';
    case 'rtsp': return 'RTSP';
    default: return '未知格式';
  }
}

/**
 * 视频事件处理
 */
function onVideoLoadStart() {
  console.log('视频开始加载');
}

function onVideoLoaded() {
  console.log('视频加载完成');
}

function onVideoMetadataLoaded() {
  console.log('视频元数据加载完成');

  if (videoElement.value) {
    const video = videoElement.value;
    const duration = video.duration;

    console.log('视频元数据信息:', {
      duration: duration ? duration.toFixed(2) : 'unknown',
      videoWidth: video.videoWidth,
      videoHeight: video.videoHeight,
      readyState: video.readyState
    });

    // 元数据加载完成
    console.log('视频元数据加载完成');
  }
}

function onVideoError(event: Event) {
  console.error('视频播放错误:', event);
  errorMessage.value = '视频播放错误';
  isPlaying.value = false;
  starting.value = false;
  attemptAutoReconnect();
}

function onVideoPlay() {
  console.log('视频开始播放');
  isPlaying.value = true;
  starting.value = false;
  clearReconnectTimer();
}

function onVideoPause() {
  console.log('视频暂停');
  // 注意：不要在这里设置 isPlaying.value = false，因为可能是缓冲导致的暂停
}

function onVideoEnded() {
  console.log('视频播放结束事件触发');
  // 真正的视频结束处理
  handleVideoReallyEnded();
}

/**
 * 处理视频真正结束
 */
function handleVideoReallyEnded() {
  console.log('视频真正播放结束');
  isPlaying.value = false;
  stopHealthCheck();
}

function onVideoWaiting() {
  console.log('视频等待数据...', {
    isPlaying: isPlaying.value,
    starting: starting.value
  });

  // 视频在等待数据，可能是网络问题或缓冲不足
  console.log('视频正在缓冲数据');

  // 清除之前的超时
  if (waitingTimeout) {
    clearTimeout(waitingTimeout);
  }

  // 设置一个超时，如果长时间等待则尝试恢复
  waitingTimeout = setTimeout(() => {
    if (videoElement.value && videoElement.value.readyState < 3) {
      console.warn('视频长时间等待数据，尝试恢复播放');

      // 尝试重新播放视频
      console.log('尝试重新播放视频');
      const video = videoElement.value;
      video.load();
      video.play().catch(error => {
        console.error('重新播放失败:', error);
        attemptAutoReconnect();
      });
    }
  }, 5000); // 5秒后尝试恢复
}

function onVideoCanPlay() {
  console.log('视频可以播放', {
    isPlaying: isPlaying.value,
    starting: starting.value,
    paused: videoElement.value?.paused,
    readyState: videoElement.value?.readyState
  });

  // 清除等待超时，因为视频现在可以播放了
  clearWaitingTimeout();

  // 如果正在启动且视频未暂停，设置为播放状态
  if (starting.value && videoElement.value && !videoElement.value.paused) {
    isPlaying.value = true;
    starting.value = false;
    console.log('视频播放状态已更新');
  }

  // 如果视频处于等待状态但现在可以播放，尝试播放
  if (!isPlaying.value && starting.value && videoElement.value) {
    console.log('尝试播放可用的视频');
    videoElement.value.play().then(() => {
      isPlaying.value = true;
      starting.value = false;
      console.log('视频播放成功');
    }).catch(error => {
      console.error('播放视频失败:', error);
    });
  }
}

function onVideoStalled() {
  console.log('视频播放停滞', {
    isPlaying: isPlaying.value,
    currentTime: videoElement.value?.currentTime,
    buffered: videoElement.value?.buffered.length || 0,
    networkState: videoElement.value?.networkState,
    readyState: videoElement.value?.readyState
  });

  // 视频播放停滞，尝试重新加载
  console.log('视频播放停滞，尝试恢复');

  // 如果停滞时间过长，尝试重连
  setTimeout(() => {
    if (videoElement.value && videoElement.value.readyState < 3) {
      console.warn('视频仍然停滞，尝试重连');
      attemptAutoReconnect();
    }
  }, 3000); // 3秒后检查
}







/**
 * 尝试自动重连
 */
function attemptAutoReconnect() {
  if (reconnectAttempts >= maxReconnectAttempts) {
    console.log('已达到最大重连次数，停止重连');
    errorMessage.value = '视频连接失败，已达到最大重连次数';
    return;
  }

  reconnectAttempts++;
  const delay = Math.min(1000 * reconnectAttempts, 5000); // 递增延迟，最大5秒

  console.log(`第 ${reconnectAttempts} 次自动重连，${delay}ms 后开始...`);

  reconnectTimer = setTimeout(async () => {
    try {
      console.log('开始自动重连...');
      await startVideo();
      reconnectAttempts = 0; // 重连成功，重置计数器
      console.log('视频重连成功');
    } catch (error: any) {
      console.error('自动重连失败:', error);
      // 继续尝试重连
      attemptAutoReconnect();
    }
  }, delay);
}

/**
 * 清除重连定时器
 */
function clearReconnectTimer() {
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
    reconnectTimer = null;
  }
  reconnectAttempts = 0;
}

/**
 * 开始健康检查
 */
function startHealthCheck() {
  stopHealthCheck(); // 先停止之前的检查

  healthCheckTimer = setInterval(() => {
    if (!isPlaying.value || !videoElement.value) {
      return;
    }

    // 检查视频是否真正在播放
    const video = videoElement.value;
    const currentTime = video.currentTime;

    // 等待一段时间后再次检查
    setTimeout(() => {
      if (isPlaying.value && video.currentTime === currentTime && !video.paused) {
        console.warn('视频可能已停止播放，尝试恢复...');
        // 视频时间没有变化，可能已停止，尝试重新播放
        video.play().catch(error => {
          console.error('重新播放失败:', error);
          attemptAutoReconnect();
        });
      }
    }, 3000); // 3秒后检查
  }, 10000); // 每10秒检查一次
}

/**
 * 停止健康检查
 */
function stopHealthCheck() {
  if (healthCheckTimer) {
    clearInterval(healthCheckTimer);
    healthCheckTimer = null;
  }
}











/**
 * 清理等待超时
 */
function clearWaitingTimeout() {
  if (waitingTimeout) {
    clearTimeout(waitingTimeout);
    waitingTimeout = null;
  }
}

// WebRTC 事件处理函数
function onWebRtcPlay() {
  console.log('WebRTC播放器开始播放');
  isPlaying.value = true;
  starting.value = false;
}

function onWebRtcPause() {
  console.log('WebRTC播放器暂停');
  isPlaying.value = false;
}

function onWebRtcError(error: Event) {
  console.error('WebRTC播放器错误:', error);
  errorMessage.value = 'WebRTC视频播放错误';
  isPlaying.value = false;
  starting.value = false;
}

function onWebRtcLoadStart() {
  console.log('WebRTC视频开始加载');
}

function onWebRtcLoaded() {
  console.log('WebRTC视频加载完成');
}

/**
 * 清理资源
 */
function cleanup() {
  console.log('VideoMonitorPlayerModal 开始清理资源:', props.videoInfo.streamId);

  // 立即重置状态，避免显示加载转圈
  isPlaying.value = false;
  starting.value = false;
  capturing.value = false;
  wsConnected.value = false;
  errorMessage.value = '';
  successMessage.value = '';
  reconnectAttempts = 0;

  // 清除所有定时器
  clearReconnectTimer(); // 清除重连定时器
  stopHealthCheck(); // 停止健康检查
  clearWaitingTimeout(); // 清除等待超时

  // 销毁WebRTC实例
  if (webRtcServer.value) {
    console.log('销毁WebRTC实例');
    try {
      webRtcServer.value.disconnect();
    } catch (error) {
      console.error('销毁WebRTC实例时出错:', error);
    }
    webRtcServer.value = null;
  }

  // 清理视频元素
  if (videoElement.value) {
    console.log('清理视频元素');
    try {
      const video = videoElement.value;
      video.pause();
      video.src = '';
      video.load();
    } catch (error) {
      console.error('清理视频元素时出错:', error);
    }
  }

  // 清理WebRTC视频元素
  if (rtspVideoElement.value) {
    console.log('清理WebRTC视频元素');
    try {
      const video = rtspVideoElement.value;
      video.pause();
      video.src = '';
      video.load();
    } catch (error) {
      console.error('清理WebRTC视频元素时出错:', error);
    }
  }

  console.log('VideoMonitorPlayerModal 资源清理完成:', props.videoInfo.streamId);
}

// 暴露方法给父组件
defineExpose({
  startVideo,
  stopVideo,
  refreshVideo,
  captureFrame,
  cleanup,
  isPlaying: () => isPlaying.value
});
</script>

<style scoped>
.video-monitor-player-modal {
  width: 100%;
  height: 100%;
}

.video-player-wrapper {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
  background: #000;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  flex-shrink: 0;
}

.video-title {
  display: flex;
  align-items: center;
  gap: 6px;
}

.video-title h5 {
  margin: 0;
  font-size: 12px;
  font-weight: 500;
}

.video-controls {
  display: flex;
  gap: 4px;
}

.video-content {
  position: relative;
  background: #000;
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 8px; /* 播放区域和信息栏之间的间距 */
}

.video-player-area {
  position: relative;
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #000;
  border-radius: 4px;
  overflow: hidden;
  min-height: 200px; /* 最小高度，确保有基本的显示空间 */
}

.video-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  gap: 12px;
  z-index: 10;
  border-radius: 4px;
}



.webrtc-rtsp-wrapper {
  position: relative;
  width: 100%;
  height: 100%; /* 填充整个video-player-area */
  background: #000;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-wrapper {
  position: relative;
  width: 100%;
  height: 100%; /* 填充整个video-player-area */
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.video-canvas {
  display: block;
  margin: 0 auto;
  background: #000;
  width: 100%;
  height: 100%;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: opacity 0.3s ease;
  z-index: 5;
}

.video-overlay:hover {
  background: rgba(0, 0, 0, 0.5);
}

.play-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 48px;
  color: white;
  opacity: 0.9;
  transition: all 0.3s ease;
  cursor: pointer;
  text-align: center;
}

.play-button:hover {
  opacity: 1;
  transform: scale(1.1);
}

.play-text {
  font-size: 14px;
  font-weight: 500;
  margin: 0;
}

.starting-overlay {
  background: rgba(0, 0, 0, 0.7);
}

.starting-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: white;
}

.loading-icon {
  font-size: 32px;
  animation: spin 1s linear infinite;
}

.starting-text {
  font-size: 14px;
  font-weight: 500;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.video-info-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: #f5f5f5;
  border-top: 1px solid #d9d9d9;
  flex-shrink: 0;
}

.status-indicators {
  display: flex;
  align-items: center;
  gap: 6px;
}



/* 响应式视频播放区域高度 */
@media (max-width: 768px) {
  .video-player-area {
    height: 250px; /* 移动设备较小高度 */
  }
}

@media (min-width: 769px) and (max-width: 1200px) {
  .video-player-area {
    height: 300px; /* 平板设备中等高度 */
  }
}

@media (min-width: 1201px) {
  .video-player-area {
    height: 400px; /* 桌面设备较大高度 */
  }
}

/* 确保在模态框中的视频播放区域有合适的最大高度 */
.ant-modal-body .video-player-area {
  max-height: 60vh; /* 不超过视口高度的60% */
}
</style>
