<template>
  <div class="webrtc-test">
    <h3>WebRTC RTSP 播放测试</h3>
    
    <div class="test-controls">
      <div style="margin-bottom: 10px;">
        <label>WebRTC服务器地址：</label>
        <a-input
          v-model:value="rtspUrl"
          placeholder="WebRTC服务器地址"
          style="width: 300px; margin-right: 10px;"
        />
      </div>
      <div style="margin-bottom: 10px;">
        <label>视频流地址：</label>
        <a-input
          v-model:value="videoUrl"
          placeholder="视频流地址"
          style="width: 300px; margin-right: 10px;"
        />
      </div>
      <div style="margin-bottom: 10px;">
        <label>RTSP流地址：</label>
        <a-input
          v-model:value="rtspVideo"
          placeholder="RTSP流地址"
          style="width: 300px; margin-right: 10px;"
        />
      </div>
      <div>
        <a-button type="primary" @click="testWebRtcVideo">测试视频播放</a-button>
        <a-button type="primary" @click="testWebRtcRtsp" style="margin-left: 10px;">测试RTSP播放</a-button>
        <a-button @click="stopTest" style="margin-left: 10px;">停止</a-button>
      </div>
    </div>

    <div class="video-container" style="margin-top: 20px;">
      <VideoMonitorPlayerModal
        v-if="showPlayer"
        :plan-id="'test-plan'"
        :video-info="videoInfo"
        :auto-start="false"
        :show-controls="true"
      />
    </div>

    <div class="test-info" style="margin-top: 20px;">
      <h4>使用说明：</h4>
      <ul>
        <li>确保已正确引入webrtcstreamer.js库</li>
        <li>RTSP URL格式：rtsp://username:password@ip:port/stream</li>
        <li>RTSP Video参数通常与RTSP URL相同或为流标识符</li>
        <li>点击"测试播放"按钮开始测试</li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import VideoMonitorPlayerModal from './VideoMonitorPlayerModal.vue';

// 测试数据
const rtspUrl = ref('https://localhost:8000');
const rtspVideo = ref('rtsp://admin:password@*************:554/stream1');
const videoUrl = ref('http://localhost:8080/live/stream.mp4');
const showPlayer = ref(false);

// 视频信息
const videoInfo = ref({
  id: 'test-webrtc',
  name: 'WebRTC 测试',
  videoUrl: '',
  streamId: 'test-stream',
  websocketUrl: '',
  rtspUrl: '',
  rtspVideo: '',
  streamType: 'preview'
});

// 测试WebRTC播放视频
function testWebRtcVideo() {
  if (!videoUrl.value) {
    console.error('请输入视频流地址');
    return;
  }

  // 更新视频信息
  videoInfo.value.videoUrl = videoUrl.value;
  videoInfo.value.rtspUrl = rtspUrl.value;
  videoInfo.value.rtspVideo = '';
  videoInfo.value.name = `WebRTC 视频测试 - ${videoUrl.value}`;

  // 显示播放器
  showPlayer.value = true;

  console.log('开始WebRTC视频测试:', {
    rtspUrl: rtspUrl.value,
    videoUrl: videoUrl.value
  });
}

// 测试WebRTC播放RTSP
function testWebRtcRtsp() {
  if (!rtspVideo.value) {
    console.error('请输入RTSP流地址');
    return;
  }

  // 更新视频信息
  videoInfo.value.videoUrl = '';
  videoInfo.value.rtspUrl = rtspUrl.value;
  videoInfo.value.rtspVideo = rtspVideo.value;
  videoInfo.value.name = `WebRTC RTSP 测试 - ${rtspVideo.value}`;

  // 显示播放器
  showPlayer.value = true;

  console.log('开始WebRTC RTSP测试:', {
    rtspUrl: rtspUrl.value,
    rtspVideo: rtspVideo.value
  });
}

// 停止测试
function stopTest() {
  showPlayer.value = false;
  console.log('停止WebRTC测试');
}
</script>

<style scoped>
.webrtc-test {
  padding: 20px;
}

.test-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.video-container {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.test-info {
  background: #f6f8fa;
  padding: 16px;
  border-radius: 6px;
  border-left: 4px solid #1890ff;
}

.test-info h4 {
  margin-top: 0;
  color: #1890ff;
}

.test-info ul {
  margin-bottom: 0;
}

.test-info li {
  margin-bottom: 8px;
}
</style>
