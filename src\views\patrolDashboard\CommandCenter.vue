<template>
  <div class="command-center" :class="{ 'fullscreen-mode': isFullscreen }">
   

    <!-- 主要内容 - 只有在有权限时才显示 -->
    <div  class="command-content">
      <!-- 监区名称标题 -->
      <div class="command-header">
        <div class="header-title">
          <div class="title-glow">{{ prisonName }}</div>
          <!-- <div class="subtitle">实时监控指挥中心</div> -->
        </div>
        <div class="header-actions">
          <div class="header-time">{{ currentTime }}</div>
          <a-button
            v-if="!isFullscreen"
            type="primary"
            class="fullscreen-btn"
            @click="toggleFullscreen"
            :icon="h(FullscreenOutlined)"
          >
            全屏显示
          </a-button>
          <a-button
            v-else
            type="default"
            class="exit-fullscreen-btn"
            @click="exitFullscreen"
            :icon="h(FullscreenExitOutlined)"
          >
            退出全屏
          </a-button>
        </div>
      </div>

    <!-- 巡更人员信息 -->
    <div class="staff-section">
      <div class="section-header">
        <div class="carousel-controls">
          <a-button
            type="text"
            size="small"
            @click="toggleCarouselMode"
            :class="{ 'active': carouselMode === 'section' }"
          >
            按监区
          </a-button>
          <a-button
            type="text"
            size="small"
            @click="toggleCarouselMode"
            :class="{ 'active': carouselMode === 'subsection' }"
          >
            按分监区
          </a-button>
        </div>
      </div>

      <!-- 轮播内容 -->
      <div class="carousel-container">
        <transition name="carousel-slide" mode="out-in">
          <div :key="currentCarouselIndex" class="carousel-content">
          
            <!-- 按监区轮播 -->
            <div v-if="carouselMode === 'section'" class="section-carousel" :class="getSectionContainerClass">
              <div class="section-group">
                <div class="subsections-container">
                  <div
                    v-for="subSection in currentSection.subSections"
                    :key="subSection.id"
                    class="subsection-group"
                    :class="getSubSectionClass(subSection)"
                  >
                    <div class="subsection-name">{{ subSection.lineName }}</div>
                    <div class="staff-cards">
                      <div
                        v-for="staff in subSection.cards"
                        :key="staff.id"
                        class="staff-card"
                        :class="{ 'active': staff.online }"
                      >
                        <div class="staff-avatar">
                          <img :src="getFileAccessHttpUrl(staff.patrolUserImage) || getStaffAvatar(staff.patrolUserName)" :alt="staff.patrolUserName" />
                          <!-- 状态指示器：online=true显示巡更中(绿色+动画)，false显示离线(灰色) -->
                          <div class="status-indicator" :class="{ 'online': staff.online, 'offline': !staff.online }"></div>
                        </div>
                        <div class="staff-info">
                          <div class="staff-name">{{ staff.patrolUserName }}</div>
                         
              
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 按分监区轮播 -->
            <div v-else class="subsection-carousel">
              <div class="subsection-group-large">
                <!-- <div class="subsection-header">
                  <div class="parent-section">{{ currentSubSection.parentSection }}</div>
                  <div class="subsection-name-large">{{ currentSubSection.name }}</div>
                </div> -->
                <div class="staff-cards-large">
                  <div
                    v-for="staff in currentSubSection.cards"
                    :key="staff.id"
                    class="staff-card-large"
                    :class="{ 'active': staff.online }"
                  >
                    <div class="staff-avatar-large">
                      <img :src="getFileAccessHttpUrl(staff.patrolUserImage) || getStaffAvatar(staff.patrolUserName)" :alt="staff.patrolUserName" />
                      <!-- 状态指示器：online=true显示巡更中(绿色+动画)，false显示离线(灰色) -->
                      <div class="status-indicator" :class="{ 'online': staff.online, 'offline': !staff.online }"></div>
                    </div>
                    <div class="staff-info-large">
                      <div class="staff-name">{{ staff.patrolUserName }}</div>
        
                      <div class="staff-department">{{ staff.lineName }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </transition>
      </div>

      <!-- 轮播指示器移动到下方 -->
      <div class="carousel-indicators">
        <div class="carousel-info">
          <span v-if="carouselMode === 'section'">
            {{ currentCarouselIndex + 1 }} / {{ commandData.sections.length }} 监区
          </span>
          <span v-else>
            {{ currentCarouselIndex + 1 }} / {{ totalSubSections }} 分监区
          </span>
        </div>
        <div class="carousel-dots">
          <div
            v-for="(item, index) in carouselItems"
            :key="index"
            class="dot"
            :class="{ 'active': index === currentCarouselIndex }"
            @click="setCarouselIndex(index)"
          ></div>
        </div>
        <div class="carousel-nav">
          <a-button type="text" size="small" @click="prevCarousel" :icon="h(LeftOutlined)"></a-button>
          <a-button type="text" size="small" @click="nextCarousel" :icon="h(RightOutlined)"></a-button>
        </div>
      </div>
    </div>

    <!-- 实时巡更计划状态 -->
    <div class="patrol-plans-section">
      <div class="section-title">
        <div class="title-line"></div>
        <span>实时巡更记录</span>
        <div class="title-line"></div>
      </div>

      <!-- 查询条件 -->
      <div class="query-filters">
        <div class="filter-item">
          <!-- <span class="filter-label">路线:</span> -->
          <a-select
            v-model:value="selectedRoute"
            placeholder="选择路线"
            :allowClear="true"
            @change="onRouteChange"
            class="route-select"
            :getPopupContainer="getPopupContainer"
          >
            <a-select-option
              v-for="route in routeOptions"
              :key="route.value"
              :value="route.value"
            >
              {{ route.label }}
            </a-select-option>
          </a-select>
        </div>

        <div class="filter-item">
          <!-- <span class="filter-label">查询日期:</span> -->
          <a-range-picker
            v-model:value="dateRange"
            :placeholder="['开始日期', '结束日期']"
            format="YYYY-MM-DD"
            :allowClear="true"
            @change="onDateRangeChange"
            @openChange="ensurePickerZIndex"
            class="date-range-picker"
            :getPopupContainer="getPopupContainer"
          />
        </div>
      </div>

      <div class="plans-container">

          <div v-if="commandData.plans.length === 0" class="empty-patrol-records">
            <a-empty />
          </div>
        
        <div class="plans-scroll" ref="plansScrollRef">
          <div
            v-for="plan in commandData.plans"
            :key="plan.id"
            class="plan-item"
            :class="getPlanStatusClass(plan.status)"
            @click="viewPlanDetail(plan)"
            title="点击查看巡更计划详情"
          >
            <!-- 计划基本信息 - 一行显示 -->
            <div class="plan-summary">
              <div class="summary-item">
                <span class="label">计划:</span>
                <span class="value">{{ plan.name || '巡更计划' }}</span>
              </div>
              <div class="summary-item">
                <span class="label">监区:</span>
                <span class="value">{{ plan.lineName }}</span>
              </div>
              <div class="summary-item">
                <span class="label">巡更者:</span>
                <div class="staff-info">
                  <img :src="getFileAccessHttpUrl(plan.patrolUserImage) || defaultAvatar" :alt="plan.patrolUserName" class="staff-avatar-tiny" />
                  <span class="value">{{ plan.patrolUserName || "未知"}}</span>
                </div>
              </div>
              <div class="summary-item">
                <span class="label">开始:</span>
                <span class="value">{{ plan.startTime }}</span>
              </div>
              <div class="summary-item" v-if="plan.endTime">
                <span class="label">结束:</span>
                <span class="value">{{ plan.endTime }}</span>
              </div>
              <div class="summary-item" v-if="plan.status === 'completed' && plan.patrolDuration">
                <span class="label">用时:</span>
                <span class="value">{{ plan.patrolDuration }}</span>
              </div>
              <div class="plan-status-badge" :class="getPlanStatusClass(plan.status)">
                {{ getStatusText(plan.status) }}
              </div>
            </div>

            <!-- 巡更点状态 - 铺满显示 -->
            <div class="patrol-points">
              <div
                v-for="(point, index) in plan.planCardList"
                :key="point.id"
                class="patrol-point"
                :class="{
                  'is-current': plan.status === 'in-progress' && point.status === 'current',
                  'is-next': plan.status === 'in-progress' && point.status === 'pending' && index === getCurrentPointIndex(plan) + 1
                }"
              >
                <div class="point-icon" :class="`status-${getStatusClass(point.status)}`">
                  <Icon :icon="getPointIcon(point.status)" />
                </div>
                <div class="point-info">
                  <div class="point-name">{{ point.cardName }}</div>
                  <div class="point-time" v-if="point.time">
                    {{ point.time }}
                  </div>
                </div>
                <!-- 连接线 -->
                <div v-if="index < plan.planCardList.length - 1" class="point-connector">
                  <div class="connector-line"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
  </div>

  <!-- 巡更计划详情弹窗 -->
  <PlanDetailModal
    ref="planDetailModalRef"
    :class="{ 'fullscreen-modal': isFullscreen }"
  />
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed, h } from 'vue';
import { getCommandCenterData, generateCommandCenterMockData, type CommandCenterData, type SubSection } from './CommandCenter.api';
import { UserManagedLines } from './PatrolDashboard.api';
import { FullscreenOutlined, FullscreenExitOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons-vue';
import { Icon } from '/@/components/Icon';
import dayjs from 'dayjs';
import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';
import { notification } from 'ant-design-vue';
import PlanDetailModal from '/@/views/plan/components/PlanDetailModal.vue';
import { getDictItems } from '/@/api/common/api';


// 响应式数据
const prisonName = ref('指挥中心可视化大屏');
const currentTime = ref('');
const commandData = ref<CommandCenterData>({
  prisonName: '',
  sections: [],
  plans: [],  // 巡更记录列表
  totalStatistics: {
    totalStaff: 0,
    onDutyStaff: 0,
    totalPlans: 0,
    inProgressPlans: 0,
    completedPlans: 0,
    missedPlans: 0
  }
});
const plansScrollRef = ref();
const isFullscreen = ref(false);

// 权限相关状态
const hasPermission = ref(false);
const isLoading = ref(true);

// 轮播相关状态
const carouselMode = ref<'section' | 'subsection'>('section'); // 轮播模式：按监区或按分监区
const currentCarouselIndex = ref(0); // 当前轮播索引
const isAutoCarousel = ref(true); // 是否自动轮播

// 查询相关状态
const selectedRoute = ref<string | undefined>(undefined);
const dateRange = ref<[any, any] | null>(null);
const routeOptions = ref<Array<{ label: string; value: string }>>([]);
const planDetailModalRef = ref();

// 默认头像
const defaultAvatar = '/src/assets/images/ai/avatar.jpg';

// 定时器
let timeTimer: NodeJS.Timeout;
let dataTimer: NodeJS.Timeout;
let carouselTimer: NodeJS.Timeout;

// 计算属性 - 排序后的巡更计划
// 优化说明：现在直接使用后台返回的 commandData.plans 字段，而不是从 sections 中提取
const sortedPatrolPlans = computed(() => {
  if (!commandData.value.plans || commandData.value.plans.length === 0) {
    return [];
  }

  // 按状态排序：进行中 > 待开始 > 已完成 > 已漏巡
  const statusOrder = { 'in-progress': 1, 'pending': 2, 'completed': 3, 'missed': 4 };
  return [...commandData.value.plans].sort((a, b) => statusOrder[a.status] - statusOrder[b.status]);
});

// 计算属性 - 轮播相关
const totalSubSections = computed(() => {
  return commandData.value.sections.reduce((total, section) => total + section.subSections.length, 0);
});

const carouselItems = computed(() => {
  if (carouselMode.value === 'section') {
    return commandData.value.sections;
  } else {
    const allSubSections: (SubSection & { parentSection: string })[] = [];
    commandData.value.sections.forEach(section => {
      section.subSections.forEach(subSection => {
        allSubSections.push({
          ...subSection,
          parentSection: section.name
        });
      });
    });
    return allSubSections;
  }
});

const currentSection = computed(() => {
  if (carouselMode.value === 'section' && commandData.value.sections.length > 0) {
    return commandData.value.sections[currentCarouselIndex.value] || commandData.value.sections[0];
  }
  return commandData.value.sections[0] || { name: '', subSections: [] };
});

const currentSubSection = computed(() => {
  if (carouselMode.value === 'subsection') {
    const allSubSections: (SubSection & { parentSection: string })[] = [];
    commandData.value.sections.forEach(section => {
      section.subSections.forEach(subSection => {
        allSubSections.push({
          ...subSection,
          parentSection: section.name
        });
      });
    });
    return allSubSections[currentCarouselIndex.value] || allSubSections[0] || { name: '', staff: [], patrolPlans: [], parentSection: '' };
  }
  return { name: '', staff: [], patrolPlans: [], parentSection: '' };
});

// 计算当前监区的分监区数量，用于响应式调整
const currentSectionSubSectionCount = computed(() => {
  if (carouselMode.value === 'section' && currentSection.value) {
    return currentSection.value.subSections?.length || 0;
  }
  return 0;
});

// 根据分监区数量获取容器样式类
const getSectionContainerClass = computed(() => {
  const subSectionCount = currentSectionSubSectionCount.value;
  return {
    'subsections-1': subSectionCount === 1,
    'subsections-2': subSectionCount === 2,
    'subsections-3': subSectionCount === 3,
    'subsections-4': subSectionCount === 4,
    'subsections-5': subSectionCount === 5,
    'subsections-many': subSectionCount > 5
  };
});

// 根据分监区数量和人员数量获取分监区样式类
const getSubSectionClass = (subSection: any) => {
  const subSectionCount = currentSectionSubSectionCount.value;
  const staffCount = subSection.cards?.length || 0;

  return {
    'section-single': subSectionCount === 1,
    'section-dual': subSectionCount === 2,
    'section-triple': subSectionCount === 3,
    'section-quad': subSectionCount === 4,
    'section-quint': subSectionCount === 5,
    'section-many': subSectionCount > 5,
    'staff-few': staffCount <= 2,
    'staff-normal': staffCount >= 3 && staffCount <= 4,
    'staff-many': staffCount > 4
  };
};

// 获取状态文本
const getStatusText = (status: string | number) => {
  const statusMap = {
    'on-duty': '值班中',
    'online': '在线',
    'offline': '离线',
    'in-progress': '进行中',
    'pending': '待巡',
    'completed': '已完成',
    'missed': '漏巡',
    'checked': '已巡更',
    'current': '当前位置'
  };

  // 对于数字状态，根据上下文判断
  if (typeof status === 'number' || !isNaN(Number(status))) {
    const numStatus = Number(status);
    switch (numStatus) {
      case 0: return '待巡';
      case 1: return '进行中';  // 对于计划状态，1表示进行中
      case 2: return '已完成'; // 对于计划状态，2表示已完成
    }
  }

  return statusMap[String(status)] || String(status);
};

// 获取巡更点图标
const getPointIcon = (status: number | string) => {
  const iconMap = {
    0: 'ant-design:clock-circle-outlined',      // 待巡
    1: 'ant-design:check-circle-filled',        // 已巡更
    2: 'ant-design:close-circle-filled',        // 漏巡
    'pending': 'ant-design:clock-circle-outlined',   // 待巡
    'current': 'ant-design:play-circle-filled',      // 当前巡更点
    'checked': 'ant-design:check-circle-filled',     // 已巡更
    'missed': 'ant-design:close-circle-filled'       // 漏巡
  };
  return iconMap[status] || 'ant-design:clock-circle-outlined';
};

// 获取状态对应的CSS类名（用于巡更点）
const getStatusClass = (status: string | number) => {
  if (typeof status === 'number' || !isNaN(Number(status))) {
    const numStatus = Number(status);
    switch (numStatus) {
      case 0: return 'pending';     // 待巡
      case 1: return 'checked';     // 已巡更
      case 2: return 'missed';      // 漏巡
    }
  }

  // 字符串状态处理
  switch (status) {
    case 'pending': return 'pending';
    case 'current': return 'current';
    case 'checked': return 'checked';
    case 'missed': return 'missed';
    default: return String(status);
  }
};

// 获取计划状态对应的CSS类名
const getPlanStatusClass = (status: string | number) => {
  if (typeof status === 'number' || !isNaN(Number(status))) {
    const numStatus = Number(status);
    switch (numStatus) {
      case 0: return 'pending';       // 待巡
      case 1: return 'in-progress';   // 进行中
      case 2: return 'completed';     // 已完成
    }
  }

  // 兼容旧状态
  const statusStr = String(status);
  switch (statusStr) {
    case 'pending': return 'pending';
    case 'in-progress': return 'in-progress';
    case 'completed': return 'completed';
    case 'missed': return 'missed';
    default: return 'pending';
  }
};

// 获取当前巡更点索引
const getCurrentPointIndex = (plan: any) => {
  // 查找数字状态1（已巡更）的最后一个点
  const points = plan.points || [];
  let lastCheckedIndex = -1;

  for (let i = 0; i < points.length; i++) {
    const point = points[i];
    if (Number(point.status) === 1) {
      lastCheckedIndex = i;
    }
  }

  return lastCheckedIndex;
};

// 获取人员头像
const getStaffAvatar = (staff: any) => {
  // 生成基于姓名的SVG头像
  const colors = [
    '#3373dc', '#52c41a', '#ff4d4f', '#fa8c16', '#722ed1',
    '#13c2c2', '#eb2f96', '#faad14', '#1890ff', '#f5222d'
  ];

  // 根据姓名生成颜色索引
  const nameHash = staff.name.split('').reduce((hash: number, char: string) => {
    return hash + char.charCodeAt(0);
  }, 0);
  const colorIndex = nameHash % colors.length;
  const bgColor = colors[colorIndex];

  // 获取姓名的最后一个字符作为显示文字
  const displayText = staff.name.slice(-1);

  // 生成SVG头像 - 修复编码问题
  const svgContent = `<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
<defs>
<linearGradient id="grad${staff.id}" x1="0%" y1="0%" x2="100%" y2="100%">
<stop offset="0%" style="stop-color:${bgColor};stop-opacity:1" />
<stop offset="100%" style="stop-color:${adjustBrightness(bgColor, -20)};stop-opacity:1" />
</linearGradient>
</defs>
<rect width="100" height="100" rx="12" fill="url(#grad${staff.id})"/>
<text x="50" y="65" font-family="Microsoft YaHei, Arial, sans-serif" font-size="32" font-weight="bold" fill="white" text-anchor="middle">${displayText}</text>
</svg>`;

  // 使用更简单的编码方式
  return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgContent)}`;
};

// 调整颜色亮度
const adjustBrightness = (color: string, amount: number) => {
  const usePound = color[0] === '#';
  const col = usePound ? color.slice(1) : color;
  const num = parseInt(col, 16);
  let r = (num >> 16) + amount;
  let g = (num >> 8 & 0x00FF) + amount;
  let b = (num & 0x0000FF) + amount;
  r = r > 255 ? 255 : r < 0 ? 0 : r;
  g = g > 255 ? 255 : g < 0 ? 0 : g;
  b = b > 255 ? 255 : b < 0 ? 0 : b;
  return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
};

// 轮播控制方法
const toggleCarouselMode = () => {
  carouselMode.value = carouselMode.value === 'section' ? 'subsection' : 'section';
  currentCarouselIndex.value = 0; // 重置索引
  resetCarouselTimer();
};



const setCarouselIndex = (index: number) => {
  currentCarouselIndex.value = index;
  resetCarouselTimer();
};

const nextCarousel = () => {
  const maxIndex = carouselItems.value.length - 1;
  currentCarouselIndex.value = currentCarouselIndex.value >= maxIndex ? 0 : currentCarouselIndex.value + 1;
  resetCarouselTimer();
};

const prevCarousel = () => {
  const maxIndex = carouselItems.value.length - 1;
  currentCarouselIndex.value = currentCarouselIndex.value <= 0 ? maxIndex : currentCarouselIndex.value - 1;
  resetCarouselTimer();
};

const startCarouselTimer = () => {
  if (isAutoCarousel.value && carouselItems.value.length > 1) {
    carouselTimer = setInterval(() => {
      nextCarousel();
    }, 500000); // 5秒切换一次
  }
};

const stopCarouselTimer = () => {
  if (carouselTimer) {
    clearInterval(carouselTimer);
  }
};

const resetCarouselTimer = () => {
  stopCarouselTimer();
  if (isAutoCarousel.value) {
    startCarouselTimer();
  }
};

// 更新时间
const updateTime = () => {
  currentTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
};

// 检查用户权限
const checkUserPermission = async () => {
  try {
    isLoading.value = true;
    const response = await UserManagedLines({});

    // 根据后端返回结果判断权限
    if (response && response.success !== false) {
      hasPermission.value = true;
      // 如果有权限，加载数据，初始化时使用当前筛选条件（包括默认日期范围）
      await loadData(true);
    } else {
      hasPermission.value = false;
      console.log('用户没有管理路线权限');
    }
  } catch (error) {
    console.error('检查权限失败:', error);
    hasPermission.value = false;
  } finally {
    isLoading.value = false;
  }
};

// 加载数据
const loadData = async (useCurrentFilters = false) => {
  try {
    let params: any = {};

    // 如果需要使用当前的筛选条件
    if (useCurrentFilters) {
      // 添加路线参数
      if (selectedRoute.value) {
        params.lineId = selectedRoute.value;
      }

      // 添加日期范围参数
      if (dateRange.value && dateRange.value[0] && dateRange.value[1]) {
        params.startTime = dayjs(dateRange.value[0]).format('YYYY-MM-DD 00:00:00');
        params.endTime = dayjs(dateRange.value[1]).format('YYYY-MM-DD 23:59:59');
      }

      console.log('加载数据使用筛选条件:', params);
    }

    const response = await getCommandCenterData(params);
    commandData.value = response;
  } catch (error) {
    console.warn('API调用失败，使用模拟数据:', error);
    commandData.value = generateCommandCenterMockData();
  }
};

// 初始化路线选项 - 通过字典加载
const initRouteOptions = async () => {
  try {
    console.log('开始加载路线字典数据...');

    // 调用字典API获取路线数据
    const dictData = await getDictItems('patrol_line,name,id,del_flag=0');

    console.log('字典数据加载成功:', dictData);

    // 将字典数据转换为选择器需要的格式
    routeOptions.value = dictData.map((item: any) => ({
      label: item.text,  // 显示文本
      value: item.value  // 选项值
    }));

    console.log('路线选项初始化完成:', routeOptions.value);

  } catch (error) {
    console.error('加载路线字典数据失败:', error);

    // 如果字典加载失败，使用备用方案：从commandData中提取路线选项
    console.log('使用备用方案：从commandData中提取路线选项');
    const routes: Array<{ label: string; value: string }> = [];
    commandData.value.sections.forEach(section => {
      section.subSections.forEach(subSection => {
        if (subSection.name) {
          routes.push({
            label: subSection.name,
            value: subSection.id.toString()
          });
        }
      });
    });
    routeOptions.value = routes;

    console.log('备用路线选项初始化完成:', routeOptions.value);
  }
};

// 全屏相关方法
const toggleFullscreen = () => {
  if (document.documentElement.requestFullscreen) {
    document.documentElement.requestFullscreen();
  }
};

const exitFullscreen = () => {
  if (document.exitFullscreen) {
    document.exitFullscreen();
  }
};

const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement;
};

// 查询相关方法
const onRouteChange = (value: string) => {
  console.log('路线变化:', value);
  queryPatrolRecords();
};

const onDateRangeChange = (dates: any, dateStrings: [string, string]) => {
  console.log('日期范围变化:', dates, dateStrings);
  if (dates && dates[0] && dates[1]) {
    queryPatrolRecords();
  } else if (!dates || (!dates[0] && !dates[1])) {
    // 清空日期范围，重新初始化默认日期范围并加载数据
    initDefaultDateRange();
    loadData(true);
  }
};

const queryPatrolRecords = async () => {
  try {
    const params: any = {};

    if (selectedRoute.value) {
      params.lineId = selectedRoute.value;
    }

    if (dateRange.value && dateRange.value[0] && dateRange.value[1]) {
      params.startTime = dayjs(dateRange.value[0]).format('YYYY-MM-DD 00:00:00');
      params.endTime = dayjs(dateRange.value[1]).format('YYYY-MM-DD 23:59:59');
    }

    console.log('查询参数:', params);

    // 调用API查询数据
    const response = await getCommandCenterData(params);
    commandData.value = response;

  } catch (error) {
    console.error('查询巡更记录失败:', error);
    notification.error({
      message: '查询失败',
      description: '查询巡更记录失败，请稍后重试',
      duration: 3
    });
  }
};

// 查看巡更计划详情 - 优化版本，减少不必要的调整
const viewPlanDetail = (plan: any) => {
  console.log('查看巡更计划详情:', plan, '全屏模式:', isFullscreen.value);

  if (planDetailModalRef.value && plan.id) {
    // 先打开弹窗
    planDetailModalRef.value.showModal(plan);

    // 在全屏模式下，只进行必要的z-index调整
    if (isFullscreen.value) {
      console.log('全屏模式下打开弹窗，调整层级');

      // 延迟调整，确保弹窗DOM创建完成
      setTimeout(() => {
        adjustModalZIndex();
      }, 100);

      // 再次确认调整
      setTimeout(() => {
        adjustModalZIndex();
      }, 300);
    }
  } else {
    notification.warning({
      message: '无法查看详情',
      description: '巡更计划信息不完整',
      duration: 3
    });
  }
};

// 调整弹窗z-index - 简化版本，只调整必要的z-index
const adjustModalZIndex = () => {
  if (!isFullscreen.value) return;

  console.log('全屏模式下调整弹窗z-index');

  // 基础弹窗元素 - 只在全屏模式下调整
  const modalElements = [
    { selector: '.ant-modal-mask', zIndex: '199999' },
    { selector: '.ant-modal-wrap', zIndex: '200000' },
    { selector: '.ant-modal', zIndex: '200001' }
  ];

  modalElements.forEach(({ selector, zIndex }) => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
      if (element) {
        (element as HTMLElement).style.setProperty('z-index', zIndex, 'important');
      }
    });
  });

  // 特别处理tech-modal类的弹窗
  const techModalElements = [
    { selector: '.tech-modal .ant-modal-mask', zIndex: '200009' },
    { selector: '.tech-modal .ant-modal-wrap', zIndex: '200010' },
    { selector: '.tech-modal .ant-modal', zIndex: '200011' },
    { selector: '.tech-modal .ant-modal-content', zIndex: '200012' }
  ];

  techModalElements.forEach(({ selector, zIndex }) => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
      if (element) {
        (element as HTMLElement).style.setProperty('z-index', zIndex, 'important');
      }
    });
  });

  console.log('弹窗z-index调整完成');
};



// 确保选择器弹窗的z-index
const ensurePickerZIndex = () => {
  if (!isFullscreen.value) return;

  setTimeout(() => {
    const dropdowns = document.querySelectorAll('.ant-picker-dropdown, .ant-select-dropdown');
    dropdowns.forEach(dropdown => {
      if (dropdown) {
        (dropdown as HTMLElement).style.setProperty('z-index', '200021', 'important');
      }
    });
  }, 50);
};

// 获取弹窗容器 - 全屏模式下确保弹窗显示在正确位置
const getPopupContainer = () => {
  if (isFullscreen.value) {
    const fullscreenContainer = document.querySelector('.fullscreen-mode');
    return fullscreenContainer || document.body;
  }
  return document.body;
};

// 清理弹窗样式 - 确保弹窗可以正常关闭
const cleanupModalStyles = () => {
  // 移除可能干扰弹窗关闭的强制样式
  const modals = document.querySelectorAll('.ant-modal');
  modals.forEach(modal => {
    const modalElement = modal as HTMLElement;
    modalElement.style.removeProperty('display');
    modalElement.style.removeProperty('visibility');
    modalElement.style.removeProperty('opacity');
    modalElement.style.removeProperty('pointer-events');
    modalElement.style.removeProperty('position');
    modalElement.style.removeProperty('top');
    modalElement.style.removeProperty('left');
    modalElement.style.removeProperty('transform');
  });
};





// 初始化默认日期范围
const initDefaultDateRange = () => {
  const today = dayjs();
  const yesterday = today.subtract(1, 'day');
  dateRange.value = [yesterday, today];
};

// 生命周期
onMounted(async () => {
  updateTime();

  // 初始化默认日期范围
  initDefaultDateRange();

  // 并行加载数据和路线选项，初始化时使用当前筛选条件（包括默认日期范围）
  await Promise.all([
    loadData(true),
    initRouteOptions()
  ]);

  // 启动定时器
  timeTimer = setInterval(updateTime, 1000);
  dataTimer = setInterval(() => loadData(true), 10000); // 10秒刷新一次数据，使用当前筛选条件

  // 启动轮播定时器
  setTimeout(() => {
    startCarouselTimer();
  }, 2000); // 延迟2秒开始轮播，等待数据加载

  // 监听全屏变化
  document.addEventListener('fullscreenchange', handleFullscreenChange);
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.addEventListener('msfullscreenchange', handleFullscreenChange);

  // 监听DOM变化，只在必要时调整弹窗z-index
  const observer = new MutationObserver((mutations) => {
    if (!isFullscreen.value) return;

    // 检查是否有新的弹窗元素
    let hasModalChanges = false;
    mutations.forEach(mutation => {
      mutation.addedNodes.forEach(node => {
        if (node.nodeType === 1) { // Element node
          const element = node as Element;
          if (element.classList.contains('ant-modal-wrap') ||
              element.querySelector('.ant-modal')) {
            hasModalChanges = true;
          }
        }
      });
    });

    if (hasModalChanges) {
      // 延迟调整，避免频繁操作
      setTimeout(() => {
        adjustModalZIndex();
        ensurePickerZIndex();
      }, 50);
    }
  });

  observer.observe(document.body, {
    childList: true,
    subtree: false // 减少监听范围
  });
});

onUnmounted(() => {
  if (timeTimer) clearInterval(timeTimer);
  if (dataTimer) clearInterval(dataTimer);
  if (carouselTimer) clearInterval(carouselTimer);

  // 移除全屏监听
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.removeEventListener('msfullscreenchange', handleFullscreenChange);
});
</script>

<style lang="less" scoped>
.command-center {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #2d3561 100%);
  color: #ffffff;
  font-family: 'Microsoft YaHei', sans-serif;
  overflow-x: hidden;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(0, 150, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, rgba(255, 0, 150, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }

  > * {
    position: relative;
    z-index: 1;
  }

  &.fullscreen-mode {
    width: 100vw;
    height: 100vh;
    min-height: 100vh;
    max-height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .command-content {
      display: flex;
      flex-direction: column;
      height: 100vh;
      overflow: hidden;
    }

    .command-header {
      flex-shrink: 0;
      height: auto;
      min-height: 60px; // 减少头部高度
      padding: 15px 30px; // 减少内边距
    }

    .staff-section {
      flex-shrink: 0;
      height: auto; // 改为自适应高度，跟随内容变化
      max-height: 40vh; // 设置最大高度限制，避免过高
      min-height: 200px; // 设置最小高度保证基本显示
      overflow: visible; // 确保内容完全显示
      padding: 6px 12px; // 减少内边距

      .carousel-container {
        height: auto; // 改为自适应高度
        max-height: none; // 移除最大高度限制
        //min-height: 180px; // 设置最小高度
        overflow: visible; // 确保内容完全显示
      }
    }

    
  }
}

// 下半部分巡更计划区域 - 自适应剩余空间
.patrol-plans-section {
      flex: 1; // 占用剩余空间
      height: auto; // 自适应高度
      max-height: none; // 移除最大高度限制
      min-height: 300px; // 设置最小高度保证基本显示
      overflow: hidden;
      padding: 6px 12px; // 减少内边距
    }

// 头部样式
.command-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  background: linear-gradient(90deg, rgba(0, 255, 255, 0.1) 0%, rgba(0, 150, 255, 0.1) 100%);
  border-bottom: 2px solid rgba(0, 255, 255, 0.3);
  backdrop-filter: blur(10px);

  .header-title {
    .title-glow {
      font-size: 36px;
      font-weight: bold;
      background: linear-gradient(45deg, #00ffff, #0096ff, #00ffff);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
      margin-bottom: 5px;
    }

    .subtitle {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.7);
      text-align: center;
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 20px;

    .header-time {
      font-size: 18px;
      color: #00ffff;
      font-weight: 500;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
    }

    .fullscreen-btn, .exit-fullscreen-btn {
      background: linear-gradient(45deg, #0096ff, #00ffff);
      border: none;
      color: #ffffff;
      font-weight: 500;
      box-shadow: 0 4px 15px rgba(0, 150, 255, 0.3);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 150, 255, 0.4);
      }
    }
  }
}

// 人员信息区域样式
.staff-section {
  padding: 10px 40px 0 40px;

  // 全屏模式下的缩放响应式优化
  .fullscreen-mode & {
    padding: 10px 20px; // 减少内边距，参考PrisonVisualization
    height: 100%; // 占满分配的高度
    max-height: 100%;
    min-height: 100%;
    overflow: visible; // 确保内容不被隐藏
    display: flex;
    flex-direction: column;
  }

  .section-header {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
    flex-shrink: 0;
    padding: 0 10px;

    // 全屏模式下减少间距
    .fullscreen-mode & {
      margin-bottom: 15px;
    }

    .carousel-controls {
      display: flex;
      gap: 10px;

      .ant-btn {
        color: rgba(255, 255, 255, 0.7);
        border-color: rgba(0, 255, 255, 0.3);
        background: rgba(0, 255, 255, 0.05);
        transition: all 0.3s ease;

        &:hover {
          color: #00ffff;
          border-color: #00ffff;
          background: rgba(0, 255, 255, 0.1);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 255, 255, 0.2);
        }

        &.active {
          color: #00ffff;
          border-color: #00ffff;
          background: rgba(0, 255, 255, 0.15);
          box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
        }
      }
    }
  }

  .carousel-indicators {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 5px;
    margin-bottom: 5px;
    padding: 5px 20px;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(0, 255, 255, 0.1);
    border-radius: 6px;
    backdrop-filter: blur(5px);

    // 全屏模式下的缩放优化
    .fullscreen-mode & {
      margin-top: 4px; // 进一步减少间距
      margin-bottom: 2px;
      padding: 4px 8px; // 进一步减少内边距
      border-radius: 4px; // 减少圆角
    }

    .carousel-info {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      font-weight: 500;

      // 全屏模式下的缩放优化
      .fullscreen-mode & {
        font-size: 11px; // 缩放字体
      }

      span {
        background: linear-gradient(45deg, #00ffff, #0096ff);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    .carousel-dots {
      display: flex;
      gap: 8px;
      padding: 6px 10px;
      background: rgba(0, 0, 0, 0.2);
      border-radius: 15px;

      // 全屏模式下的缩放优化
      .fullscreen-mode & {
        gap: 6px; // 减少间距
        padding: 4px 8px; // 减少内边距
        border-radius: 12px; // 减少圆角
      }

      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        cursor: pointer;
        transition: all 0.3s ease;

        // 全屏模式下的缩放优化
        .fullscreen-mode & {
          width: 6px; // 缩放尺寸
          height: 6px;
        }
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          border-radius: 50%;
          background: transparent;
          transition: all 0.3s ease;
        }

        &:hover {
          background: rgba(0, 255, 255, 0.6);
          transform: scale(1.3);

          &::before {
            background: rgba(0, 255, 255, 0.2);
          }
        }

        &.active {
          background: #00ffff;
          box-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
          transform: scale(1.2);

          &::before {
            background: rgba(0, 255, 255, 0.3);
          }
        }
      }
    }

    .carousel-nav {
      display: flex;
      gap: 6px;

      .ant-btn {
        color: rgba(255, 255, 255, 0.7);
        border-color: rgba(0, 255, 255, 0.3);
        background: rgba(0, 255, 255, 0.05);
        border-radius: 4px;
        transition: all 0.3s ease;
        padding: 4px 8px;
        min-width: 32px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          color: #00ffff;
          border-color: #00ffff;
          background: rgba(0, 255, 255, 0.1);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 255, 255, 0.2);
        }

        &:active {
          transform: translateY(0);
        }

        .anticon {
          font-size: 14px;
        }
      }
    }
  }

  .carousel-container {
    position: relative;
    // min-height: 300px;
    flex: 1;
    overflow: hidden;

    // 全屏模式下的缩放响应式优化 - 高度跟随内容变化，确保人员卡全部显示
    .fullscreen-mode & {
      height: auto; // 改为自适应高度，跟随内容变化
      //min-height: 180px; // 设置最小高度保证基本显示
      max-height: none; // 移除最大高度限制
      overflow: visible; // 确保内容不被隐藏
      flex: none; // 不占满剩余空间，使用自适应高度
    }
  }

  .carousel-content {
    width: 100%;
    height: 100%;
    overflow: hidden;

    // 全屏模式下的优化 - 确保内容完全显示
    .fullscreen-mode & {
      display: flex;
      flex-direction: column;
      height: 100%;
      overflow: visible; // 确保内容不被隐藏
    }
  }

  // 轮播模式下的监区样式
  .section-carousel {
    height: 100%;
    overflow: visible; // 改为visible，确保内容不被隐藏

    // 全屏模式下的优化
    .fullscreen-mode & {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: visible; // 确保内容完全显示
    }

    .section-group {
      height: 100%;
      overflow: visible; // 改为visible，确保内容不被隐藏

      // 全屏模式下的缩放响应式优化
      .fullscreen-mode & {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: visible; // 确保内容完全显示
        padding: 8px; // 减少内边距
      }

      .subsections-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); // 减少最小宽度，允许更多分监区显示
        gap: 15px; // 减少间距，节省空间
        height: auto; // 允许自动扩展
        overflow: visible; // 确保内容不被截断
        // min-height: 280px; // 设置合适的最小高度
        padding: 10px; // 添加内边距

        // 响应式调整
        @media (min-width: 768px) {
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 18px;
          // min-height: 320px;
        }

        @media (min-width: 1200px) {
          grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
          gap: 20px;
          // min-height: 350px;
        }

        @media (min-width: 1600px) {
          grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
          gap: 22px;
          // min-height: 380px;
        }

        // 全屏模式下的缩放响应式优化 - 高度跟随内容变化，确保人员卡全部显示
        .fullscreen-mode & {
          height: auto; // 改为自适应高度，跟随内容变化
          max-height: none; // 移除最大高度限制
          min-height: 180px; // 设置最小高度保证基本显示
          overflow: visible; // 确保内容可见，不被隐藏
          padding: 4px; // 减少内边距
          gap: 6px; // 减少间距

          // 根据分监区数量进行缩放
          grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); // 减少最小宽度
        }
      }
    }

    // 根据分监区数量调整布局
    &.subsections-1 {
      .subsections-container {
        grid-template-columns: 1fr;
        max-width: 600px;
        margin: 0 auto;

        // 全屏模式下的缩放优化 - 高度跟随内容变化
        .fullscreen-mode & {
          max-width: 100%; // 占满宽度
          margin: 0;
          grid-template-columns: 1fr;
          height: auto; // 自适应高度
          max-height: none; // 移除最大高度限制
        }

        .subsection-group {
          .staff-cards {
            justify-content: center;
            gap: 16px;
            flex-wrap: nowrap; // 不换行，一行显示
            overflow-x: auto; // 横向滚动

            // 全屏模式下的缩放
            .fullscreen-mode & {
              gap: 12px; // 减少间距
            }

            // 响应式间距调整
            @media (min-width: 768px) {
              gap: 28px;
            }

            @media (min-width: 1200px) {
              gap: 32px;
            }

            // 全屏模式下保持相同的响应式规则
            .fullscreen-mode & {
              gap: 24px;

              @media (min-width: 768px) {
                gap: 28px;
              }

              @media (min-width: 1200px) {
                gap: 32px;
              }
            }
          }
        }
      }
    }

    &.subsections-2 {
      .subsections-container {
        grid-template-columns: repeat(2, 1fr);

        // 全屏模式下的缩放优化 - 高度跟随内容变化
        .fullscreen-mode & {
          grid-template-columns: repeat(2, 1fr);
          gap: 6px; // 减少间距
          height: auto; // 自适应高度
          max-height: none; // 移除最大高度限制
        }

        .subsection-group {
          .staff-cards {
            gap: 14px;
            flex-wrap: nowrap; // 不换行，一行显示
            overflow-x: auto; // 横向滚动

            // 全屏模式下的缩放
            .fullscreen-mode & {
              gap: 10px; // 减少间距
            }

            // 响应式间距调整
            @media (min-width: 768px) {
              gap: 24px;
            }

            @media (min-width: 1200px) {
              gap: 28px;
            }

            // 全屏模式下保持相同的响应式规则
            .fullscreen-mode & {
              gap: 20px;

              @media (min-width: 768px) {
                gap: 24px;
              }

              @media (min-width: 1200px) {
                gap: 28px;
              }
            }
          }
        }
      }
    }

    &.subsections-3 {
      .subsections-container {
        grid-template-columns: repeat(3, 1fr);

        // 全屏模式下的缩放优化 - 高度跟随内容变化
        .fullscreen-mode & {
          grid-template-columns: repeat(3, 1fr);
          gap: 5px; // 减少间距
          height: auto; // 自适应高度
          max-height: none; // 移除最大高度限制
        }

        .subsection-group {
          .staff-cards {
            gap: 12px;
            flex-wrap: nowrap; // 不换行，一行显示
            overflow-x: auto; // 横向滚动

            // 全屏模式下的缩放
            .fullscreen-mode & {
              gap: 8px; // 减少间距
            }

            // 响应式间距调整
            @media (min-width: 768px) {
              gap: 22px;
            }

            @media (min-width: 1200px) {
              gap: 26px;
            }

            // 全屏模式下保持相同的响应式规则
            .fullscreen-mode & {
              gap: 18px;

              @media (min-width: 768px) {
                gap: 22px;
              }

              @media (min-width: 1200px) {
                gap: 26px;
              }
            }
          }
        }
      }
    }

    &.subsections-4 {
      .subsections-container {
        grid-template-columns: repeat(4, 1fr);

        // 全屏模式下的缩放优化 - 高度跟随内容变化
        .fullscreen-mode & {
          grid-template-columns: repeat(4, 1fr);
          gap: 4px; // 减少间距
          height: auto; // 自适应高度
          max-height: none; // 移除最大高度限制
        }

        .subsection-group {
          .staff-cards {
            gap: 10px;
            flex-wrap: nowrap; // 不换行，一行显示
            overflow-x: auto; // 横向滚动

            // 全屏模式下的缩放
            .fullscreen-mode & {
              gap: 6px; // 减少间距
            }

            // 响应式间距调整
            @media (min-width: 768px) {
              gap: 20px;
            }

            @media (min-width: 1200px) {
              gap: 24px;
            }

            // 全屏模式下保持相同的响应式规则
            .fullscreen-mode & {
              gap: 16px;

              @media (min-width: 768px) {
                gap: 20px;
              }

              @media (min-width: 1200px) {
                gap: 24px;
              }
            }
          }
        }
      }
    }

    &.subsections-5 {
      .subsections-container {
        grid-template-columns: repeat(5, 1fr);

        // 全屏模式下的缩放优化 - 高度跟随内容变化
        .fullscreen-mode & {
          grid-template-columns: repeat(5, 1fr);
          gap: 3px; // 减少间距
          height: auto; // 自适应高度
          max-height: none; // 移除最大高度限制
        }

        .subsection-group {
          .staff-cards {
            gap: 8px;
            flex-wrap: nowrap; // 不换行，一行显示
            overflow-x: auto; // 横向滚动

            // 全屏模式下的缩放
            .fullscreen-mode & {
              gap: 5px; // 减少间距
            }

            // 响应式间距调整
            @media (min-width: 768px) {
              gap: 18px;
            }

            @media (min-width: 1200px) {
              gap: 22px;
            }

            // 全屏模式下保持相同的响应式规则
            .fullscreen-mode & {
              gap: 14px;

              @media (min-width: 768px) {
                gap: 18px;
              }

              @media (min-width: 1200px) {
                gap: 22px;
              }
            }
          }
        }
      }
    }

    &.subsections-many {
      .subsections-container {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));

        // 全屏模式下的缩放优化 - 高度跟随内容变化
        .fullscreen-mode & {
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); // 更紧凑
          gap: 2px; // 最小间距
          height: auto; // 自适应高度
          max-height: none; // 移除最大高度限制
        }

        .subsection-group {
          .staff-cards {
            gap: 6px;
            flex-wrap: nowrap; // 不换行，一行显示
            overflow-x: auto; // 横向滚动

            // 全屏模式下的缩放
            .fullscreen-mode & {
              gap: 4px; // 减少间距
            }

            // 响应式间距调整
            @media (min-width: 768px) {
              gap: 16px;
            }

            @media (min-width: 1200px) {
              gap: 20px;
            }

            // 全屏模式下保持相同的响应式规则
            .fullscreen-mode & {
              gap: 12px;

              @media (min-width: 768px) {
                gap: 16px;
              }

              @media (min-width: 1200px) {
                gap: 20px;
              }
            }
          }
        }
      }
    }

  .subsection-group {
    background: rgba(255, 255, 255, 0.08); // 保持背景透明度
    border: 2px solid rgba(0, 255, 255, 0.3); // 保持边框样式
    border-radius: 12px; // 适中的圆角
    padding: 16px; // 减少内边距，节省空间
    backdrop-filter: blur(15px); // 保持模糊效果
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    overflow: visible; // 确保内容不被截断
    box-shadow: 0 4px 15px rgba(0, 255, 255, 0.1); // 适中的阴影
    height: auto; // 允许自动扩展高度
    //min-height: 200px; // 设置最小高度

    // 全屏模式下的缩放响应式优化 - 高度跟随内容变化，确保人员卡全部显示
    .fullscreen-mode & {
      padding: 6px; // 减少内边距
      border-radius: 6px; // 减少圆角
      height: auto; // 改为自适应高度，跟随内容变化
      max-height: none; // 移除最大高度限制
      min-height: 160px; // 设置最小高度保证基本显示
      overflow: visible; // 确保内容不被隐藏
    }

    &:hover {
      border-color: rgba(0, 255, 255, 0.4);
      box-shadow: 0 0 20px rgba(0, 255, 255, 0.1);
    }

    .subsection-name {
      font-size: 16px; // 适中的字体大小
      font-weight: 600; // 适中的字体粗细
      color: #00ffff; // 保持青色
      text-align: center;
      margin-bottom: 12px; // 减少底部间距
      padding: 8px 16px; // 减少内边距，更紧凑
      background: rgba(0, 255, 255, 0.12); // 稍微增加背景透明度
      border: 1px solid rgba(0, 255, 255, 0.3); // 保持边框
      border-radius: 10px; // 适中的圆角
      flex-shrink: 0;
      text-shadow: 0 0 8px rgba(0, 255, 255, 0.4); // 适中的发光效果
      letter-spacing: 0.5px; // 适中的字母间距
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap; // 防止换行

      // 全屏模式下的缩放优化
      .fullscreen-mode & {
        font-size: 11px; // 进一步减小字体
        margin-bottom: 4px; // 减少间距
        padding: 3px 6px; // 减少内边距
        border-radius: 5px; // 减少圆角
      }
    }

    .staff-cards {
      display: flex;
      gap: 6px; // 减少间距，允许更多人员在一行显示
      justify-content: center; // 居中对齐，更美观
      flex-wrap: nowrap; // 不换行，强制一行显示
      flex: 1;
      align-items: flex-start;
      overflow-x: auto; // 横向滚动，确保所有人员都能看到
      overflow-y: visible; // 纵向可见
      padding: 6px; // 减少内边距，节省空间
      // min-height: 100px; // 适中的最小高度

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        height: 4px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 255, 255, 0.3);
        border-radius: 2px;

        &:hover {
          background: rgba(0, 255, 255, 0.5);
        }
      }

      // 响应式间距调整
      @media (min-width: 768px) {
        gap: 8px;
        padding: 8px;
        // min-height: 110px;
      }

      @media (min-width: 1200px) {
        gap: 10px;
        padding: 10px;
        // min-height: 120px;
      }

      @media (min-width: 1600px) {
        gap: 12px;
        padding: 12px;
        // min-height: 130px;
      }

      // 全屏模式下的缩放响应式优化 - 高度跟随内容变化，确保人员卡全部显示
      .fullscreen-mode & {
        gap: 4px; // 紧凑间距
        padding: 2px; // 减少内边距
        height: auto; // 改为自适应高度，跟随人员卡内容变化
        max-height: none; // 移除最大高度限制
        min-height: 120px; // 设置最小高度保证基本显示
        overflow-x: auto; // 横向滚动
        overflow-y: visible; // 纵向可见，确保内容不被隐藏
        justify-content: flex-start; // 左对齐，更好利用空间
        flex-wrap: nowrap; // 强制不换行
      }

      .staff-card {
        background: rgba(255, 255, 255, 0.12); // 保持背景透明度
        border: 2px solid rgba(0, 255, 255, 0.3); // 保持边框样式
        border-radius: 10px; // 减少圆角，更紧凑
        padding: 10px; // 减少内边距，更紧凑
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: visible; // 确保内容不被截断
        flex: 1; // 平均分配宽度，响应式适应
        min-width: 110px; // 适当增加最小宽度以容纳更宽的头像
        max-width: 150px; // 适当增加最大宽度
        box-shadow: 0 2px 10px rgba(0, 255, 255, 0.1); // 适中的阴影

        // 响应式内边距调整
        @media (min-width: 768px) {
          padding: 12px;
          border-radius: 12px;
          min-width: 120px;
          max-width: 160px;
        }

        @media (min-width: 1200px) {
          padding: 14px;
          border-radius: 14px;
          min-width: 130px;
          max-width: 170px;
        }

        @media (min-width: 1600px) {
          padding: 16px;
          border-radius: 16px;
          min-width: 140px;
          max-width: 180px;
        }

        // 全屏模式下的缩放响应式优化 - 确保人员卡片完全显示
        .fullscreen-mode & {
          padding: 6px; // 增加内边距，确保内容有足够空间
          border-radius: 6px; // 减少圆角
          flex: 1; // 平均分配宽度
          min-width: 70px; // 增加最小宽度，确保内容显示
          max-width: 95px; // 增加最大宽度
          height: auto; // 自适应高度，确保所有内容都能显示
          min-height: 120px; // 设置最小高度，确保基本信息显示
        }

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
          transition: left 0.5s ease;
        }

        &:hover::before {
          left: 100%;
        }

        &.active {
          border-color: #00ffff;
          box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);

          .staff-avatar {
            animation: pulse 2s infinite;
          }
        }

        .staff-avatar {
          position: relative;
          margin: 0 auto 8px; // 减少底部间距，更紧凑
          width: 56px; // 增加宽度，保持4:5比例
          height: 70px; // 对应的半身像高度

          // 响应式尺寸调整 - 保持4:5半身像比例，适当增加宽度
          @media (min-width: 768px) {
            width: 60px; // 4:5比例，增加宽度
            height: 75px;
            margin: 0 auto 10px;
          }

          @media (min-width: 1200px) {
            width: 64px; // 4:5比例，增加宽度
            height: 80px;
            margin: 0 auto 12px;
          }

          @media (min-width: 1600px) {
            width: 68px; // 4:5比例，增加宽度
            height: 85px;
            margin: 0 auto 14px;
          }

          // 全屏模式下的缩放响应式优化 - 确保头像清晰可见
          .fullscreen-mode & {
            width: 38px; // 增加尺寸，确保清晰可见
            height: 38px;
            margin: 0 auto 3px; // 适当间距
          }

          img {
            width: 100%;
            height: 100%;
            border-radius: 6px; // 大幅减少圆角，更简洁现代
            object-fit: cover;
            border: 3px solid rgba(0, 255, 255, 0.4); // 增强边框颜色和宽度
            background: rgba(255, 255, 255, 0.08);
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 255, 255, 0.2); // 添加阴影

            // 响应式圆角调整 - 统一使用较小的圆角
            @media (min-width: 768px) {
              border-radius: 6px;
            }

            @media (min-width: 1200px) {
              border-radius: 8px;
            }

            @media (min-width: 1600px) {
              border-radius: 8px;
            }

            // 全屏模式下保持相同的响应式规则
            .fullscreen-mode & {
              border-radius: 4px;

              @media (min-width: 768px) {
                border-radius: 6px;
              }

              @media (min-width: 1200px) {
                border-radius: 6px;
              }

              @media (min-width: 1600px) {
                border-radius: 8px;
              }
            }
          }

          &:hover img {
            border-color: rgba(0, 255, 255, 0.4);
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
          }

          .status-indicator {
            position: absolute;
            bottom: -3px;
            right: -3px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid #ffffff;
            z-index: 2;

            // 响应式尺寸调整
            @media (min-width: 768px) {
              width: 22px;
              height: 22px;
              bottom: -4px;
              right: -4px;
            }

            @media (min-width: 1200px) {
              width: 24px;
              height: 24px;
              border-width: 3px;
            }

            // 全屏模式下保持相同的响应式规则
            .fullscreen-mode & {
              width: 20px;
              height: 20px;
              bottom: -3px;
              right: -3px;

              @media (min-width: 768px) {
                width: 22px;
                height: 22px;
                bottom: -4px;
                right: -4px;
              }

              @media (min-width: 1200px) {
                width: 24px;
                height: 24px;
                border-width: 3px;
              }
            }

                  &.on-duty {
                    background: #00ff00;
                    box-shadow: 0 0 10px rgba(0, 255, 0, 0.6);
                    animation: pulse 2s infinite;
                  }

                  &.online {
                    background: #00ff00; // 巡更中 - 绿色
                    box-shadow: 0 0 10px rgba(0, 255, 0, 0.6);
                    animation: pulse 2s infinite; // 脉冲动画
                  }

                  &.offline {
                    background: #666666; // 离线 - 灰色
                    box-shadow: none;
                  }
                }
              }

        .staff-info {
          .staff-name {
            font-size: 13px; // 适中的字体大小，紧凑布局
            font-weight: 600; // 适中的字体粗细
            color: #ffffff;
            margin-bottom: 3px; // 进一步减少间距，更紧凑
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5); // 保持可读性
            letter-spacing: 0.3px; // 适中的字母间距
            line-height: 1.1; // 更紧凑的行高
            overflow: hidden; // 防止文字溢出
            text-overflow: ellipsis; // 文字过长时显示省略号
            white-space: nowrap; // 不换行

            // 响应式字体调整
            @media (min-width: 768px) {
              font-size: 14px;
              margin-bottom: 4px;
            }

            @media (min-width: 1200px) {
              font-size: 15px;
              margin-bottom: 5px;
            }

            @media (min-width: 1600px) {
              font-size: 16px;
              margin-bottom: 6px;
            }

            // 全屏模式下的缩放优化 - 确保文字可读性
            .fullscreen-mode & {
              font-size: 10px; // 增加字体大小，确保可读性
              margin-bottom: 2px; // 适当间距
              letter-spacing: 0.2px; // 适当字母间距
            }

            // 全屏模式下保持相同的响应式规则
            .fullscreen-mode & {
              font-size: 15px;
              margin-bottom: 6px;

              @media (min-width: 768px) {
                font-size: 16px;
                margin-bottom: 7px;
              }

              @media (min-width: 1200px) {
                font-size: 17px;
                margin-bottom: 8px;
              }

              @media (min-width: 1600px) {
                font-size: 18px;
                margin-bottom: 9px;
              }
            }
          }

          .staff-card-id {
            font-size: 11px; // 紧凑的字体大小
            color: rgba(0, 255, 255, 0.9); // 保持青色，醒目
            margin-bottom: 2px; // 进一步减少间距，更紧凑
            font-weight: 500;
            background: rgba(0, 255, 255, 0.12); // 稍微增加背景透明度
            padding: 1px 5px; // 减少内边距，更紧凑
            border-radius: 5px; // 减少圆角，更紧凑
            display: inline-block;
            font-family: 'Courier New', monospace; // 使用等宽字体，更清晰
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%; // 限制最大宽度

            // 响应式字体调整
            @media (min-width: 768px) {
              font-size: 12px;
              margin-bottom: 3px;
              padding: 2px 6px;
            }

            @media (min-width: 1200px) {
              font-size: 13px;
              margin-bottom: 4px;
              padding: 2px 8px;
            }

            @media (min-width: 1600px) {
              font-size: 14px;
              margin-bottom: 5px;
              padding: 3px 10px;
            }

            // 全屏模式下的缩放优化 - 确保卡号可读性
            .fullscreen-mode & {
              font-size: 8px; // 增加字体大小，确保可读性
              margin-bottom: 1px; // 适当间距
              padding: 1px 3px; // 适当内边距
              border-radius: 4px; // 适当圆角
            }

            // 全屏模式下保持相同的响应式规则
            .fullscreen-mode & {
              font-size: 13px;
              margin-bottom: 5px;

              @media (min-width: 768px) {
                font-size: 14px;
                margin-bottom: 6px;
              }

              @media (min-width: 1200px) {
                font-size: 15px;
                margin-bottom: 7px;
              }

              @media (min-width: 1600px) {
                font-size: 16px;
                margin-bottom: 8px;
              }
            }
          }

          .staff-status {
            font-size: 10px; // 紧凑的字体大小
            font-weight: 700; // 加粗字体
            padding: 3px 8px; // 紧凑的内边距
            border-radius: 10px; // 圆润的圆角
            display: inline-block;
            text-transform: uppercase; // 大写字母
            letter-spacing: 0.3px; // 适中的字母间距
            border: 1px solid transparent; // 默认透明边框
            transition: all 0.3s ease; // 平滑过渡
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); // 文字阴影
            font-family: 'Arial', sans-serif; // 清晰的字体
            white-space: nowrap; // 不换行

            // 在线/巡更中状态样式
            &.online-status {
              color: #ffffff;
              background: linear-gradient(135deg, #00ff88, #00cc66); // 渐变绿色背景
              border-color: rgba(0, 255, 136, 0.5);
              box-shadow: 0 0 8px rgba(0, 255, 136, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2);
              animation: pulse-green 2s infinite;
            }

            // 离线状态样式
            &.offline-status {
              color: #ffffff;
              background: linear-gradient(135deg, #666666, #555555); // 渐变灰色背景
              border-color: rgba(102, 102, 102, 0.5);
              box-shadow: 0 0 4px rgba(102, 102, 102, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);
            }

            // 悬停效果
            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 2px 8px rgba(0, 255, 255, 0.3);
            }

            // 响应式字体调整
            @media (min-width: 768px) {
              font-size: 11px;
              padding: 4px 10px;
            }

            @media (min-width: 1200px) {
              font-size: 12px;
              padding: 5px 12px;
            }

            @media (min-width: 1600px) {
              font-size: 13px;
              padding: 6px 14px;
            }

            // 全屏模式下的缩放优化 - 确保状态文字可读性
            .fullscreen-mode & {
              font-size: 8px; // 增加字体大小，确保可读性
              padding: 2px 5px; // 适当内边距
              border-radius: 6px; // 适当圆角
              letter-spacing: 0.3px; // 适当字母间距
            }

            // 全屏模式下保持相同的响应式规则
            .fullscreen-mode & {
              font-size: 12px;

              @media (min-width: 768px) {
                font-size: 13px;
              }

              @media (min-width: 1200px) {
                font-size: 14px;
              }

              @media (min-width: 1600px) {
                font-size: 15px;
              }
            }

                  // 全屏模式下的优化
                  .fullscreen-mode & {
                    font-size: 10px;
                  }
                }
              }
            }
          }
        }
      }
    
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 255, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(0, 255, 255, 0);
  }
}

@keyframes pulse-green {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 255, 0, 0.4);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 0 8px rgba(0, 255, 0, 0);
  }
}

// 轮播动画
.carousel-slide-enter-active,
.carousel-slide-leave-active {
  transition: all 0.5s ease;
}

.carousel-slide-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.carousel-slide-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

// 分监区轮播样式
.subsection-carousel {
  .subsection-group-large {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 12px;
    padding: 30px;
    backdrop-filter: blur(10px);

    .subsection-header {
      text-align: center;
      margin-bottom: 30px;

      .parent-section {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 10px;
      }

      .subsection-name-large {
        font-size: 28px;
        font-weight: bold;
        color: #00ffff;
        text-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
      }
    }

    .staff-cards-large {
      display: flex;
      gap: 30px;
      justify-content: center;

      .staff-card-large {
        background: rgba(255, 255, 255, 0.08);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 10px; // 减少圆角，更紧凑
        padding: 20px; // 减少内边距，更紧凑
        text-align: center;
        transition: all 0.3s ease;
        flex: 1;
        max-width: 210px; // 适当增加最大宽度以容纳更宽的头像

        &.active {
          border-color: #00ffff;
          box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);

          .staff-avatar-large {
            animation: pulse 2s infinite;
          }
        }

        .staff-avatar-large {
          position: relative;
          margin: 0 auto 12px; // 减少底部间距，更紧凑
          width: 88px; // 增加宽度，保持4:5比例
          height: 110px; // 对应的半身像高度

          img {
            width: 100%;
            height: 100%;
            border-radius: 6px; // 减少圆角，更简洁现代
            object-fit: cover;
            border: 3px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.05);
            transition: all 0.3s ease;
          }

          &:hover img {
            border-color: rgba(0, 255, 255, 0.5);
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
            transform: scale(1.05);
          }

          .status-indicator {
            position: absolute;
            bottom: -3px;
            right: -3px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 3px solid #ffffff;
            z-index: 2;

            &.on-duty {
              background: #00ff00;
              box-shadow: 0 0 12px rgba(0, 255, 0, 0.6);
              animation: pulse 2s infinite;
            }

            &.online {
              background: #00ff00; // 巡更中 - 绿色
              box-shadow: 0 0 12px rgba(0, 255, 0, 0.6);
              animation: pulse 2s infinite; // 脉冲动画
            }

            &.offline {
              background: #666666; // 离线 - 灰色
              box-shadow: none;
            }
          }
        }

        .staff-info-large {
          .staff-name {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8px;
          }

          .staff-card-id {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 6px;
          }

          .staff-status {
            font-size: 12px;
            color: #00ffff;
            margin-bottom: 6px;
          }

          .staff-department {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.5);
          }
        }
      }
    }
  }
}

// 巡更计划区域样式
.patrol-plans-section {
  padding: 10px 40px 20px 40px;
  flex: 1;
  display: flex;
  flex-direction: column;

  // 查询条件样式 - 靠右显示
  .query-filters {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 16px;
    margin-bottom: 16px;
    padding: 8px 16px;
    background: linear-gradient(135deg,
      rgba(10, 14, 39, 0.4) 0%,
      rgba(26, 31, 58, 0.3) 50%,
      rgba(10, 14, 39, 0.4) 100%
    );
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 6px;
    backdrop-filter: blur(10px);
    box-shadow:
      0 2px 15px rgba(0, 255, 255, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);
    position: relative;

    // 添加科技感光效
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 255, 255, 0.6) 50%,
        transparent 100%
      );
    }

    &:hover {
      background: linear-gradient(135deg,
        rgba(10, 14, 39, 0.8) 0%,
        rgba(26, 31, 58, 0.6) 50%,
        rgba(10, 14, 39, 0.8) 100%
      );
      border-color: rgba(0, 255, 255, 0.4);
      box-shadow:
        0 6px 25px rgba(0, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    }

    .filter-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .filter-label {
        color: rgba(255, 255, 255, 0.9);
        font-size: 13px;
        font-weight: 500;
        white-space: nowrap;
        text-shadow: 0 0 8px rgba(0, 255, 255, 0.3);
      }

      .route-select {
        width: 200px;

        :deep(.ant-select-selector) {
          background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #2d3561 100%) !important;
          border: 1px solid rgba(0, 255, 255, 0.4) !important;
          border-radius: 6px !important;
          color: #ffffff !important;
          height: 32px !important;

          &:hover {
            border-color: rgba(0, 255, 255, 0.6) !important;
          }
        }

        :deep(.ant-select-selection-placeholder) {
          color: rgba(255, 255, 255, 0.5) !important;
        }

        :deep(.ant-select-selection-item) {
          color: #ffffff !important;
        }

        :deep(.ant-select-arrow) {
          color: rgba(0, 255, 255, 0.7) !important;
        }

        // 下拉框选项背景样式 - 恢复默认白色
        :deep(.ant-select-dropdown) {
          background: #ffffff !important;
          border: 1px solid #d9d9d9 !important;
          border-radius: 6px !important;
          box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;

          .ant-select-item {
            background: transparent !important;
            color: rgba(0, 0, 0, 0.85) !important;
            border-bottom: none !important;

            &:hover {
              background: #f5f5f5 !important;
              color: rgba(0, 0, 0, 0.85) !important;
            }

            &.ant-select-item-option-selected {
              background: #e6f7ff !important;
              color: #1890ff !important;
              font-weight: 600;
            }
          }
        }
      }

      .date-range-picker {
        width: 280px;

        :deep(.ant-picker) {
          background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #2d3561 100%) !important;
          border: 1px solid rgba(0, 255, 255, 0.4) !important;
          border-radius: 4px !important;
          color: #ffffff !important;
          height: 32px !important;

          &:hover {
            border-color: rgba(0, 255, 255, 0.6) !important;
          }

          &.ant-picker-focused {
            border-color: #00ffff !important;
            box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.15) !important;
          }

          .ant-picker-input > input {
            color: #ffffff !important;
            background: transparent !important;
            font-size: 13px !important;

            &::placeholder {
              color: rgba(255, 255, 255, 0.4) !important;
            }
          }

          .ant-picker-separator {
            color: rgba(0, 255, 255, 0.6) !important;
          }

          .ant-picker-suffix {
            color: rgba(0, 255, 255, 0.5) !important;
          }

          .ant-picker-clear {
            color: rgba(255, 255, 255, 0.5) !important;
            background: rgba(255, 255, 255, 0.1) !important;
            border-radius: 50% !important;

            &:hover {
              color: #ffffff !important;
              background: rgba(255, 100, 100, 0.8) !important;
              transform: scale(1.1) !important;
            }
          }
        }

        // 日期选择器下拉框背景样式
        :deep(.ant-picker-dropdown) {
          background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #2d3561 100%) !important;
          //border: 1px solid rgba(0, 255, 255, 0.4) !important;
          border-radius: 6px !important;
          box-shadow:
            0 4px 20px rgba(0, 0, 0, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;

          .ant-picker-panel {
            background: transparent !important;
            border: none !important;

            .ant-picker-header {
              border-bottom: 1px solid rgba(0, 255, 255, 0.2) !important;

              .ant-picker-header-view {
                color: #ffffff !important;
              }

              .ant-picker-prev-icon,
              .ant-picker-next-icon,
              .ant-picker-super-prev-icon,
              .ant-picker-super-next-icon {
                color: rgba(0, 255, 255, 0.7) !important;

                &:hover {
                  color: #00ffff !important;
                }
              }
            }

            .ant-picker-content {
              .ant-picker-cell {
                color: rgba(255, 255, 255, 0.8) !important;

                &:hover {
                  background: rgba(0, 255, 255, 0.1) !important;
                }

                &.ant-picker-cell-selected {
                  background: rgba(0, 255, 255, 0.2) !important;
                  color: #00ffff !important;
                }

                &.ant-picker-cell-in-range {
                  background: rgba(0, 255, 255, 0.1) !important;
                }

                &.ant-picker-cell-range-start,
                &.ant-picker-cell-range-end {
                  background: rgba(0, 255, 255, 0.3) !important;
                  color: #ffffff !important;
                }
              }
            }
          }

          .ant-picker-footer {
            border-top: 1px solid rgba(0, 255, 255, 0.2) !important;
            background: transparent !important;

            .ant-picker-today-btn {
              color: rgba(0, 255, 255, 0.8) !important;

              &:hover {
                color: #00ffff !important;
              }
            }
          }
        }
      }
    }
  }

  // 全屏模式下的优化
  .fullscreen-mode & {
    padding: 10px 30px 20px 30px;
    flex: 1;
    min-height: 0;
    overflow: hidden;
  }

  .section-title {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    flex-shrink: 0;

    // 全屏模式下的优化
    .fullscreen-mode & {
      margin-bottom: 20px;
    }

    .title-line {
      flex: 1;
      height: 2px;
      background: linear-gradient(90deg, transparent, #00ffff, transparent);
    }

    span {
      margin: 0 20px;
      font-size: 24px;
      font-weight: bold;
      color: #00ffff;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);

      // 全屏模式下的优化
      .fullscreen-mode & {
        font-size: 20px;
        margin: 0 15px;
      }
    }
  }

  .plans-container {
    flex: 1;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 12px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    min-height: 0;

    // 全屏模式下的优化
    .fullscreen-mode & {
      flex: 1;
      min-height: 0;
      overflow: hidden;
    }

    .plans-scroll {
      height: 100%;
      max-height: 500px;
      overflow-y: auto;
      padding: 20px;

      // 全屏模式下的优化
      .fullscreen-mode & {
        height: 100%;
        max-height: none;
        padding: 15px;
        overflow-y: auto;
      }

      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: linear-gradient(45deg, #00ffff, #0096ff);
        border-radius: 4px;

        &:hover {
          background: linear-gradient(45deg, #0096ff, #00ffff);
        }
      }

      .plan-item {
        padding: clamp(12px, 1.5vw, 18px);
        background: rgba(0, 0, 0, 0.4);
        border: 2px solid rgba(0, 255, 255, 0.3);
        border-radius: 12px;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        flex-shrink: 0;
        margin-bottom: clamp(12px, 1.5vh, 20px);
        cursor: pointer;

        &:hover {
          background: rgba(0, 0, 0, 0.6);
          border-color: rgba(0, 255, 255, 0.5);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 255, 255, 0.2);
        }

        &:active {
          transform: translateY(0);
          box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);
        }

        // 全屏模式下的优化
        .fullscreen-mode & {
          padding: 12px;
          margin-bottom: 10px;
          border-radius: 8px;
        }

        @media (max-width: 767px) {
          border-radius: 8px;
          margin-bottom: 15px;
          padding: 15px;
        }

        &.in-progress {
          border-color: #1890ff;
          box-shadow: 0 0 15px rgba(24, 144, 255, 0.3);

          .plan-status-badge {
            background: linear-gradient(45deg, #1890ff, #40a9ff);
          }
        }

        &.pending {
          border-color: #faad14;

          .plan-status-badge {
            background: linear-gradient(45deg, #faad14, #ffc53d);
          }
        }

        &.completed {
          border-color: #52c41a;

          .plan-status-badge {
            background: linear-gradient(45deg, #52c41a, #73d13d);
          }
        }

        &.missed {
          border-color: #ff4d4f;

          .plan-status-badge {
            background: linear-gradient(45deg, #ff4d4f, #ff7875);
          }
        }

        &:hover {
          transform: translateX(5px);
          box-shadow: 0 3px 15px rgba(0, 255, 255, 0.2);
        }

        .plan-summary {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: clamp(10px, 1.2vh, 15px);
          padding: clamp(10px, 1.2vh, 15px);
          background: rgba(0, 0, 0, 0.2);
          border-radius: 8px;
          border: 1px solid rgba(0, 255, 255, 0.2);
          position: relative;
          overflow: hidden;

          @media (max-width: 767px) {
            flex-wrap: wrap;
            gap: 8px;
          }

          // 添加实时进度条
          &::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: linear-gradient(90deg, #1890ff, #40a9ff);
            animation: progressBar 10s linear infinite;
            box-shadow: 0 0 6px rgba(24, 144, 255, 0.6);
          }

          .summary-item {
            display: flex;
            align-items: center;
            gap: 6px;

            @media (max-width: 767px) {
              gap: 4px;
            }

            .label {
              font-size: clamp(12px, 1.2vw, 14px);
              color: rgba(0, 255, 255, 0.8);
              font-weight: bold;
              white-space: nowrap;

              @media (max-width: 767px) {
                font-size: 11px;
              }
            }

            .value {
              font-size: clamp(12px, 1.2vw, 14px);
              color: #ffffff;
              font-weight: normal;
              white-space: nowrap;

              @media (max-width: 767px) {
                font-size: 11px;
              }
            }

            .staff-info {
              display: flex;
              align-items: center;
              gap: 6px;

              .staff-avatar-tiny {
                width: clamp(18px, 1.8vw, 24px);
                height: clamp(18px, 1.8vw, 24px);
                border-radius: 3px; // 减少圆角，更简洁
                border: 1px solid rgba(0, 255, 255, 0.5);
                object-fit: cover;
                flex-shrink: 0;

                @media (max-width: 767px) {
                  width: 16px;
                  height: 16px;
                }
              }
            }
          }

          .plan-status-badge {
            padding: clamp(5px, 0.8vw, 8px) clamp(12px, 1.5vw, 16px);
            border-radius: 12px;
            font-size: clamp(11px, 1.1vw, 13px);
            font-weight: bold;
            color: #ffffff;
            text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
            white-space: nowrap;
            flex-shrink: 0;

            @media (max-width: 767px) {
              padding: 4px 8px;
              font-size: 10px;
            }
          }
        }

        .patrol-points {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: clamp(8px, 1vh, 12px);
          padding: clamp(8px, 1vh, 12px) 0;
          position: relative;

          .patrol-point {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            flex: 1;
            min-width: 0;
            min-height: 60px;

            // 当前位置特殊样式
            &.is-current {
              .point-icon {
                transform: scale(1.15);
                animation: currentPointPulse 2s ease-in-out infinite;
                z-index: 15;
                position: relative;

                // 添加进度环
                &::before {
                  content: '';
                  position: absolute;
                  top: -4px;
                  left: -4px;
                  right: -4px;
                  bottom: -4px;
                  border: 2px solid transparent;
                  border-top: 2px solid #1890ff;
                  border-radius: 50%;
                  animation: progressRing 3s linear infinite;
                }
              }

              .point-name {
                color: #1890ff;
                font-weight: bold;
                text-shadow: 0 0 3px rgba(24, 144, 255, 0.8);
                animation: textGlow 2s ease-in-out infinite;
                position: relative;

                // 添加巡更中提示
                &::after {
                  content: '巡更中';
                  position: absolute;
                  top: -15px;
                  left: 50%;
                  transform: translateX(-50%);
                  font-size: 8px;
                  color: #1890ff;
                  background: rgba(24, 144, 255, 0.1);
                  padding: 1px 4px;
                  border-radius: 3px;
                  white-space: nowrap;
                  animation: patrolText 2s ease-in-out infinite;
                }
              }

              .point-time {
                animation: textGlow 2s ease-in-out infinite;
              }

              // 隐藏当前位置的连接线，避免重复显示
              .point-connector {
                opacity: 0.3; // 降低透明度，减少视觉干扰
              }
            }

            // 下一个目标特殊样式
            &.is-next {
              .point-icon {
                animation: nextPointBlink 1.5s ease-in-out infinite;
                position: relative;

                // 添加等待指示器
                &::before {
                  content: '';
                  position: absolute;
                  top: -6px;
                  right: -6px;
                  width: 12px;
                  height: 12px;
                  background: #faad14;
                  border-radius: 50%;
                  animation: countdown 1s ease-in-out infinite;
                  box-shadow: 0 0 6px rgba(250, 173, 20, 0.8);
                }
              }

              .point-name {
                color: #faad14;
                font-weight: bold;
                text-shadow: 0 0 3px rgba(250, 173, 20, 0.8);
                animation: nextTextBlink 1.5s ease-in-out infinite;
                position: relative;

                // 添加下一个提示
                &::after {
                  content: '下一个';
                  position: absolute;
                  top: -15px;
                  left: 50%;
                  transform: translateX(-50%);
                  font-size: 8px;
                  color: #faad14;
                  background: rgba(250, 173, 20, 0.1);
                  padding: 1px 4px;
                  border-radius: 3px;
                  white-space: nowrap;
                  animation: nextAlert 1.5s ease-in-out infinite;
                }
              }

              .point-time {
                animation: nextTextBlink 1.5s ease-in-out infinite;
              }
            }

            .point-icon {
              width: clamp(24px, 2.5vw, 32px);
              height: clamp(24px, 2.5vw, 32px);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-bottom: clamp(4px, 0.5vh, 6px);
              border: 2px solid rgba(255, 255, 255, 0.3);
              transition: all 0.3s ease;
              position: relative;
              z-index: 10;

              &.status-checked {
                background: linear-gradient(45deg, #52c41a, #73d13d);
                border-color: #52c41a;
                color: #ffffff;
                box-shadow: 0 0 8px rgba(82, 196, 26, 0.6);

                :deep(.anticon) {
                  color: #ffffff;
                  font-size: clamp(12px, 1.2vw, 16px);
                }
              }

              &.status-current {
                background: linear-gradient(45deg, #1890ff, #40a9ff);
                border-color: #1890ff;
                color: #ffffff;
                box-shadow: 0 0 12px rgba(24, 144, 255, 0.8);

                :deep(.anticon) {
                  color: #ffffff;
                  font-size: clamp(12px, 1.2vw, 16px);
                }
              }

              &.status-pending {
                background: linear-gradient(45deg, #faad14, #ffc53d);
                border-color: #faad14;
                color: #ffffff;
                box-shadow: 0 0 8px rgba(250, 173, 20, 0.6);

                :deep(.anticon) {
                  color: #ffffff;
                  font-size: clamp(12px, 1.2vw, 16px);
                }
              }

              &.status-missed {
                background: linear-gradient(45deg, #ff4d4f, #ff7875);
                border-color: #ff4d4f;
                color: #ffffff;
                box-shadow: 0 0 8px rgba(255, 77, 79, 0.6);

                :deep(.anticon) {
                  color: #ffffff;
                  font-size: clamp(12px, 1.2vw, 16px);
                }
              }
            }

            .point-info {
              text-align: center;
              min-height: clamp(25px, 3vh, 35px);
              display: flex;
              flex-direction: column;
              justify-content: flex-start;
              width: 100%;

              .point-name {
                font-size: clamp(10px, 1vw, 12px);
                color: #ffffff;
                margin-bottom: 2px;
                white-space: nowrap;
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;

                @media (max-width: 767px) {
                  font-size: 9px;
                  margin-bottom: 1px;
                }
              }

              .point-time {
                font-size: clamp(9px, 0.9vw, 10px);
                color: rgba(0, 255, 255, 0.8);
                font-family: 'Courier New', monospace;
                font-weight: bold;
                white-space: nowrap;

                @media (max-width: 767px) {
                  font-size: 8px;
                }
              }
            }

            // 连接线 - 采用PatrolDashboard.vue样式
            .point-connector {
              position: absolute;
              top: clamp(12px, 1.25vw, 16px); // 基于图标中心位置
              left: 50%; // 从当前点中心开始
              width: calc(100% - clamp(30px, 3vw, 40px)); // 缩短连接线，为箭头留出空间
              height: 3px; // 连接线高度
              z-index: 5; // 提高z-index
              transform: translateX(clamp(12px, 1.25vw, 16px)); // 从图标右边缘开始
              pointer-events: none; // 避免阻挡点击事件

              .connector-line {
                width: 100%; // 连接线占满整个容器
                height: 100%;
                background: linear-gradient(90deg,
                  rgba(0, 255, 255, 0.9) 0%,
                  rgba(0, 255, 255, 0.7) 50%,
                  rgba(0, 255, 255, 0.9) 100%
                );
                border-radius: 2px;
                position: relative;
                box-shadow: 0 0 6px rgba(0, 255, 255, 0.4);

                // 箭头连接在连接线末端，但不覆盖下一个点
                &::after {
                  content: '';
                  position: absolute;
                  top: 50%;
                  right: -8px; // 箭头稍微延伸，但不覆盖下一个点
                  width: 0;
                  height: 0;
                  border-left: 8px solid rgba(0, 255, 255, 0.9);
                  border-top: 4px solid transparent;
                  border-bottom: 4px solid transparent;
                  transform: translateY(-50%);
                  filter: drop-shadow(0 0 3px rgba(0, 255, 255, 0.8));
                  z-index: 10;
                }
              }
            }

            // 当前位置到下一个点的连接线 - 蓝色到黄色渐变
            &.is-current .point-connector {
              .connector-line {
                background: linear-gradient(90deg,
                  rgba(24, 144, 255, 0.9) 0%,   // 蓝色（当前位置）
                  rgba(24, 144, 255, 0.7) 30%,
                  rgba(250, 173, 20, 0.7) 70%,  // 黄色（下一个目标）
                  rgba(250, 173, 20, 0.9) 100%
                );
                box-shadow: 0 0 8px rgba(24, 144, 255, 0.3);

                &::after {
                  border-left: 8px solid rgba(250, 173, 20, 0.9); // 箭头使用下一个点的颜色
                  filter: drop-shadow(0 0 3px rgba(250, 173, 20, 0.8));
                }
              }
            }

            // 已完成的连接线 - 绿色
            &.status-checked .point-connector {
              .connector-line {
                background: linear-gradient(90deg,
                  rgba(82, 196, 26, 0.9) 0%,
                  rgba(82, 196, 26, 0.7) 50%,
                  rgba(82, 196, 26, 0.9) 100%
                );
                box-shadow: 0 0 6px rgba(82, 196, 26, 0.4);

                &::after {
                  border-left: 8px solid rgba(82, 196, 26, 0.9);
                  filter: drop-shadow(0 0 3px rgba(82, 196, 26, 0.8));
                }
              }
            }
          }
        }
      }
    }
  }
}

// 巡更计划动画效果
@keyframes progressBar {
  0% { width: 0%; }
  100% { width: 100%; }
}

@keyframes currentPointPulse {
  0%, 100% {
    transform: scale(1.15);
    box-shadow: 0 0 12px rgba(24, 144, 255, 0.8);
  }
  50% {
    transform: scale(1.25);
    box-shadow: 0 0 20px rgba(24, 144, 255, 1);
  }
}

@keyframes progressRing {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes textGlow {
  0%, 100% {
    text-shadow: 0 0 3px rgba(24, 144, 255, 0.8);
  }
  50% {
    text-shadow: 0 0 8px rgba(24, 144, 255, 1), 0 0 12px rgba(24, 144, 255, 0.8);
  }
}

@keyframes patrolText {
  0%, 100% {
    opacity: 0.8;
    transform: translateX(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) scale(1.05);
  }
}

@keyframes nextPointBlink {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes countdown {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

@keyframes nextTextBlink {
  0%, 100% {
    color: #faad14;
    text-shadow: 0 0 3px rgba(250, 173, 20, 0.8);
  }
  50% {
    color: #ffc53d;
    text-shadow: 0 0 6px rgba(250, 173, 20, 1);
  }
}

@keyframes nextAlert {
  0%, 100% {
    opacity: 0.8;
    transform: translateX(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) scale(1.1);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .command-center {
    .command-header {
      padding: 15px 20px;

      .header-title .title-glow {
        font-size: 28px;
      }
    }

    .staff-section {
      padding: 15px 20px;

      .section-header {
        .carousel-controls {
          flex-direction: column;
          gap: 5px;

          .ant-btn {
            font-size: 11px;
            padding: 3px 8px;
          }
        }
      }

      .carousel-indicators {
        flex-direction: column;
        gap: 15px;
        margin-top: 5px;
        padding: 12px 15px;

        .carousel-info {
          order: 1;
          text-align: center;
        }

        .carousel-dots {
          order: 2;
          justify-content: center;

          .dot {
            width: 8px;
            height: 8px;
          }
        }

        .carousel-nav {
          order: 3;
          justify-content: center;
        }
      }

      .section-carousel {
        .section-group .subsections-container {
          grid-template-columns: 1fr;
          gap: 15px;

          .subsection-group .staff-cards {
            flex-wrap: wrap;
            gap: 8px;

            .staff-card {
              flex: 1;
              min-width: 100px;
              max-width: 150px;

              .staff-avatar {
                width: 56px; // 增加宽度，保持4:5比例
                height: 70px; // 对应的半身像高度
              }

              .staff-info {
                .staff-name {
                  font-size: 13px;
                }

                .staff-card-id {
                  font-size: 11px;
                }

                .staff-status {
                  font-size: 10px;
                }
              }
            }
          }
        }
      }

      .subsection-carousel {
        .subsection-group-large {
          padding: 20px;

          .staff-cards-large {
            flex-direction: column;
            gap: 20px;

            .staff-card-large {
              max-width: none;

              .staff-avatar-large {
                width: 72px; // 增加宽度，保持4:5比例
                height: 90px; // 对应的半身像高度
              }

              .staff-info-large {
                .staff-name {
                  font-size: 16px;
                }

                .staff-card-id {
                  font-size: 13px;
                }
              }
            }
          }
        }
      }
    }

    .patrol-plans-section {
      padding: 20px;

      .plans-container .plans-scroll {
        max-height: 400px;

        .plan-item {
          .plan-summary {
            flex-wrap: wrap;
            gap: 10px;

            .summary-item {
              flex: 1;
              min-width: 120px;
            }
          }

          .patrol-points {
            flex-wrap: wrap;

            .patrol-point {
              .point-icon {
                width: 28px;
                height: 28px;
              }

              .point-info .point-name {
                font-size: 9px;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .command-center {
    .command-header {
      flex-direction: column;
      gap: 15px;
      text-align: center;

      .header-actions {
        justify-content: center;
      }
    }

    .staff-section {
      padding: 10px 15px;

      .section-header {
        justify-content: center;

        .carousel-controls {
          .ant-btn {
            font-size: 10px;
            padding: 2px 6px;
          }
        }
      }

      .carousel-indicators {
        padding: 8px 10px;
        margin-top: 15px;
        flex-direction: column;
        gap: 10px;

        .carousel-info {
          font-size: 12px;
          text-align: center;
          order: 1;
        }

        .carousel-dots {
          order: 2;
          justify-content: center;
          padding: 4px 8px;

          .dot {
            width: 6px;
            height: 6px;
          }
        }

        .carousel-nav {
          order: 3;
          justify-content: center;

          .ant-btn {
            font-size: 12px;
            padding: 3px 6px;
            min-width: 28px;
            height: 24px;

            .anticon {
              font-size: 12px;
            }
          }
        }
      }

      .section-carousel {
        .section-group {

          .subsections-container .subsection-group {
            padding: 15px;

            .subsection-name {
              font-size: 16px;
            }

            .staff-cards .staff-card {
              min-width: 90px;
              max-width: 120px;
              padding: 10px;

              .staff-avatar {
                width: 44px; // 增加宽度，保持4:5比例
                height: 55px; // 对应的半身像高度
              }

              .staff-info {
                .staff-name {
                  font-size: 12px;
                }

                .staff-card-id {
                  font-size: 10px;
                }

                .staff-status {
                  font-size: 9px;
                }
              }
            }
          }
        }
      }

      .subsection-carousel {
        .subsection-group-large {
          padding: 15px;

          .subsection-header {
            .parent-section {
              font-size: 14px;
            }

            .subsection-name-large {
              font-size: 22px;
            }
          }

          .staff-cards-large .staff-card-large {
            padding: 15px;

            .staff-avatar-large {
              width: 64px; // 增加宽度，保持4:5比例
              height: 80px; // 对应的半身像高度
            }

            .staff-info-large {
              .staff-name {
                font-size: 14px;
              }

              .staff-card-id {
                font-size: 12px;
              }

              .staff-status {
                font-size: 10px;
              }

              .staff-department {
                font-size: 9px;
              }
            }
          }
        }
      }
    }

    .patrol-plans-section .plans-container .plans-scroll .plan-item {
      .plan-summary {
        .summary-item {
          min-width: 100px;

          .label, .value {
            font-size: 11px;
          }
        }
      }

      .patrol-points .patrol-point {
        .point-icon {
          width: 24px;
          height: 24px;
        }

        .point-info .point-name {
          font-size: 8px;
        }
      }
    }
  }
}

// 全局选择器和弹窗样式优化
:global(.ant-select-dropdown) {
  z-index: 100000 !important;
  background: #ffffff !important;
  border: 1px solid #d9d9d9 !important;
  border-radius: 6px !important;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;

  // 全屏模式下确保更高的z-index
  .fullscreen-mode & {
    z-index: 100001 !important;
  }

  .ant-select-item {
    background: transparent !important;
    color: rgba(0, 0, 0, 0.85) !important;
    border: none !important;
    padding: 5px 12px !important;

    &:hover {
      background: #f5f5f5 !important;
      color: rgba(0, 0, 0, 0.85) !important;
    }

    &.ant-select-item-option-selected {
      background: #e6f7ff !important;
      color: #1890ff !important;
      font-weight: 600;
    }

    &.ant-select-item-option-disabled {
      background: transparent !important;
      color: rgba(0, 0, 0, 0.25) !important;
    }
  }
}

:global(.ant-picker-dropdown) {
  z-index: 100000 !important;

  // 全屏模式下确保更高的z-index
  .fullscreen-mode & {
    z-index: 100001 !important;
  }

  .ant-picker-panel-container {
    background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #2d3561 100%) !important;
    //border: 1px solid rgba(0, 255, 255, 0.4) !important;
    border-radius: 6px !important;
    box-shadow:
      0 4px 20px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  }

  .ant-picker-panel {
    background: transparent !important;
    border: none !important;
  }

  .ant-picker-header {
    background: transparent !important;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2) !important;
    border-radius: 6px 6px 0 0 !important;

    .ant-picker-header-view {
      color: #ffffff !important;

      button {
        color: rgba(255, 255, 255, 0.8) !important;
        border: none !important;
        background: transparent !important;

        &:hover {
          color: #00ffff !important;
          background: rgba(0, 255, 255, 0.1) !important;
        }
      }
    }
  }

  .ant-picker-content {
    background: transparent !important;

    th {
      color: rgba(255, 255, 255, 0.7) !important;
      background: transparent !important;
      border: none !important;
    }

    td {
      background: transparent !important;
      border: none !important;

      .ant-picker-cell-inner {
        color: rgba(255, 255, 255, 0.8) !important;
        background: transparent !important;
        border: 1px solid transparent !important;
        border-radius: 4px !important;

        &:hover {
          background: rgba(0, 255, 255, 0.2) !important;
          color: #ffffff !important;
          border-color: rgba(0, 255, 255, 0.4) !important;
        }
      }

      &.ant-picker-cell-selected .ant-picker-cell-inner {
        background: linear-gradient(45deg, #1890ff, #40a9ff) !important;
        color: #ffffff !important;
        border-color: #00ffff !important;
        box-shadow: 0 0 8px rgba(24, 144, 255, 0.5) !important;
      }

      &.ant-picker-cell-today .ant-picker-cell-inner {
        border-color: #00ffff !important;
        color: #00ffff !important;
      }

      &.ant-picker-cell-in-range .ant-picker-cell-inner {
        background: rgba(24, 144, 255, 0.2) !important;
        color: #ffffff !important;
      }
    }
  }
}



// 全屏模式下的样式优化
.fullscreen-mode {
  .command-header {
    padding: 25px 50px;

    .header-title .title-glow {
      font-size: 42px;
    }

    .header-actions .header-time {
      font-size: 20px;
    }
  }

  .staff-section {
    padding: 30px 50px;

    .section-header {
      margin-bottom: 10px;

      .carousel-controls {
        .ant-btn {
          font-size: 14px;
          // padding: 8px 18px;
          min-width: 80px;
        }
      }
    }

    .carousel-indicators {
      margin-top: 10px;
      margin-left: 10px;
      margin-right: 10px;
      padding: 12px 25px;

      .carousel-info {
        font-size: 16px;

        span {
          font-size: 16px;
        }
      }

      .carousel-dots {
        padding: 8px 12px;

        .dot {
          width: 10px;
          height: 10px;
        }
      }

      .carousel-nav .ant-btn {
        font-size: 16px;
        padding: 6px 12px;
        min-width: 36px;
        height: 32px;

        .anticon {
          font-size: 16px;
        }
      }
    }

    .section-carousel {
      .section-group {

        .subsections-container .subsection-group {
          padding: 25px;

          .subsection-name {
            font-size: 20px;
          }

          .staff-cards .staff-card {
            padding: 18px;
            max-width: 140px;

            .staff-avatar {
              width: 64px; // 半身像宽度，保持4:5比例
              height: 80px; // 半身像高度
            }

            .staff-info {
              .staff-name {
                font-size: 16px;
              }

              .staff-card-id {
                font-size: 14px;
              }

              .staff-status {
                font-size: 13px;
              }
            }
          }
        }
      }
    }

    .subsection-carousel {
      .subsection-group-large {
        padding: 10px;

        .subsection-header {
          .parent-section {
            font-size: 20px;
          }

          .subsection-name-large {
            font-size: 32px;
          }
        }

        .staff-cards-large .staff-card-large {
          padding: 30px;
          max-width: 250px;

          .staff-avatar-large {
            width: 104px; // 增加宽度，保持4:5比例
            height: 130px; // 对应的半身像高度
          }

          .staff-info-large {
            .staff-name {
              font-size: 20px;
            }

            .staff-card-id {
              font-size: 16px;
            }

            .staff-status {
              font-size: 14px;
            }

            .staff-department {
              font-size: 13px;
            }
          }
        }
      }
    }
  }

  .patrol-plans-section {
    padding: 20px;

    .plans-container .plans-scroll {
      max-height: 600px;

      .plan-item {
        padding: 18px;
        margin-bottom: 18px;

        .plan-summary {
          padding: 15px;

          .summary-item {
            .label {
              font-size: 14px;
            }

            .value {
              font-size: 15px;
            }

            .staff-info .staff-avatar-tiny {
              width: 24px;
              height: 24px;
            }
          }

          .plan-status-badge {
            padding: 6px 15px;
            font-size: 13px;
          }
        }

        .patrol-points .patrol-point {
          .point-icon {
            width: 36px;
            height: 36px;
          }

          .point-info {
            .point-name {
              font-size: 12px;
            }

            .point-time {
              font-size: 11px;
            }
          }
        }
      }
    }
  }
}

// 全屏模式下的弹窗样式 - 只设置必要的z-index
:deep(.fullscreen-modal) {
  .ant-modal-wrap {
    z-index: 200000 !important;
  }

  .ant-modal-mask {
    z-index: 199999 !important;
    background-color: rgba(0, 0, 0, 0.3) !important;
  }

  .ant-modal {
    z-index: 200001 !important;
  }
}

// 全屏模式下的弹窗样式 - 简化版本
.fullscreen-mode {
  // tech-modal特殊处理
  :global(.tech-modal) {
    .ant-modal-wrap {
      z-index: 200010 !important;
    }

    .ant-modal-mask {
      z-index: 200009 !important;
    }

    .ant-modal {
      z-index: 200011 !important;
    }
  }
}

// 全局日期选择器和下拉框弹窗样式优化
:global(.ant-picker-dropdown) {
  z-index: 200020 !important;

  // 全屏模式下确保更高的z-index
  .fullscreen-mode & {
    z-index: 200021 !important;
  }

  .ant-picker-panel-container {
    background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #2d3561 100%) !important;
    border: none !important;
    border-radius: 6px !important;
    box-shadow:
      0 4px 20px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  }

  .ant-picker-panel {
    background: transparent !important;
    border: none !important;
  }

  .ant-picker-header {
    background: transparent !important;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2) !important;
    border-radius: 6px 6px 0 0 !important;

    .ant-picker-header-view {
      color: #ffffff !important;

      button {
        color: rgba(255, 255, 255, 0.8) !important;
        border: none !important;
        background: transparent !important;

        &:hover {
          color: #00ffff !important;
          background: rgba(0, 255, 255, 0.1) !important;
        }
      }
    }
  }

  .ant-picker-content {
    background: transparent !important;

    th {
      color: rgba(255, 255, 255, 0.7) !important;
      background: transparent !important;
      border: none !important;
    }

    .ant-picker-cell {
      color: rgba(255, 255, 255, 0.8) !important;

      &:hover {
        background: rgba(0, 255, 255, 0.1) !important;
      }

      &.ant-picker-cell-selected {
        background: rgba(0, 255, 255, 0.2) !important;
        color: #00ffff !important;
      }

      &.ant-picker-cell-in-range {
        background: rgba(0, 255, 255, 0.1) !important;
      }

      &.ant-picker-cell-range-start,
      &.ant-picker-cell-range-end {
        background: rgba(0, 255, 255, 0.3) !important;
        color: #ffffff !important;
      }
    }
  }

  .ant-picker-footer {
    border-top: 1px solid rgba(0, 255, 255, 0.2) !important;
    background: transparent !important;

    .ant-picker-today-btn {
      color: rgba(0, 255, 255, 0.8) !important;

      &:hover {
        color: #00ffff !important;
      }
    }
  }
}

:global(.ant-select-dropdown) {
  z-index: 200020 !important;
  background: #ffffff !important;
  border: 1px solid #d9d9d9 !important;
  border-radius: 6px !important;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;

  // 全屏模式下确保更高的z-index
  .fullscreen-mode & {
    z-index: 200021 !important;
  }

  .ant-select-item {
    background: transparent !important;
    color: rgba(0, 0, 0, 0.85) !important;
    border: none !important;

    &:hover {
      background: #f5f5f5 !important;
      color: rgba(0, 0, 0, 0.85) !important;
    }

    &.ant-select-item-option-selected {
      background: #e6f7ff !important;
      color: #1890ff !important;
      font-weight: 600;
    }
  }
}
:deep(.ant-picker) {
        background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #0a0e27 100%);
        border: 1px solid rgba(0, 255, 255, 0.4);
        border-radius: 6px;
        color: #ffffff;
        height: 34px;
        box-shadow:
          0 2px 10px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
  :deep(.ant-picker .ant-picker-input >input){
    color: #ffffff;
  }
  :deep(.ant-picker-clear){
    background: transparent;
    color: #ffffff;
  }
  // 空数据状态样式
.empty-patrol-records {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px 20px;

  :deep(.ant-empty) {
    color: rgba(255, 255, 255, 0.8);

    .ant-empty-image {
      margin-bottom: 20px;
    }
  }

  .empty-icon {
    color: rgba(0, 255, 255, 0.4);
    margin-bottom: 20px;
    filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.3));
  }

  .empty-description {
    text-align: center;

    .empty-title {
      font-size: clamp(16px, 1.8vw, 20px);
      font-weight: bold;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 8px;
      text-shadow: 0 0 8px rgba(0, 255, 255, 0.3);
    }

    .empty-subtitle {
      font-size: clamp(14px, 1.4vw, 16px);
      color: rgba(255, 255, 255, 0.6);
      margin-bottom: 16px;
      line-height: 1.4;
    }

    .empty-tips {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      font-size: clamp(12px, 1.2vw, 14px);
      color: rgba(0, 255, 255, 0.7);
      background: rgba(0, 255, 255, 0.1);
      border: 1px solid rgba(0, 255, 255, 0.2);
      border-radius: 6px;
      padding: 8px 12px;
      backdrop-filter: blur(10px);

      :deep(.anticon) {
        font-size: 14px;
      }
    }
  }
}

</style>>
