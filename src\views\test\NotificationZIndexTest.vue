<template>
  <div class="notification-test">
    <div class="test-header">
      <h1>Notification 层级优化测试</h1>
      <p>测试 notification 在弹窗情况下的显示层级</p>
    </div>

    <div class="test-controls">
      <a-space size="large" direction="vertical">
        <div class="control-group">
          <h3>基础测试</h3>
          <a-space>
            <a-button type="primary" @click="showNormalNotification">
              普通 Notification
            </a-button>
            <a-button type="default" @click="showHighPriorityNotification">
              高优先级 Notification
            </a-button>
          </a-space>
        </div>

        <div class="control-group">
          <h3>弹窗 + Notification 测试</h3>
          <a-space>
            <a-button type="primary" @click="showModalWithNotification">
              显示弹窗 + 触发通知
            </a-button>
            <a-button type="danger" @click="showAlarmWithNotification">
              显示报警弹窗 + 触发通知
            </a-button>
          </a-space>
        </div>

        <div class="control-group">
          <h3>报警处理测试</h3>
          <a-space>
            <a-button type="primary" @click="testAlarmSuccess">
              模拟报警处理成功
            </a-button>
            <a-button type="default" @click="testAlarmError">
              模拟报警处理失败
            </a-button>
          </a-space>
        </div>
      </a-space>
    </div>

    <div class="test-info">
      <div class="info-card">
        <h3>🎯 优化内容</h3>
        <ul>
          <li><strong>层级设置</strong> - notification z-index: 10000</li>
          <li><strong>弹窗层级</strong> - 报警弹窗 z-index: 9999</li>
          <li><strong>样式优化</strong> - 毛玻璃效果和渐变背景</li>
          <li><strong>类型区分</strong> - 成功/警告/错误不同颜色</li>
          <li><strong>强制显示</strong> - 使用 !important 确保优先级</li>
        </ul>
      </div>

      <div class="info-card">
        <h3>📋 测试场景</h3>
        <ul>
          <li><strong>普通通知</strong> - 默认层级的 notification</li>
          <li><strong>高优先级通知</strong> - 自定义高层级 notification</li>
          <li><strong>弹窗遮挡测试</strong> - 在有弹窗时显示通知</li>
          <li><strong>报警处理反馈</strong> - 处理成功/失败的通知</li>
          <li><strong>多层级测试</strong> - 多个弹窗同时存在</li>
        </ul>
      </div>
    </div>

    <!-- 测试用的普通弹窗 -->
    <a-modal
      v-model:open="testModalVisible"
      title="测试弹窗"
      :z-index="1000"
      @ok="testModalVisible = false"
    >
      <p>这是一个测试弹窗，z-index: 1000</p>
      <p>点击下面的按钮测试 notification 是否能显示在弹窗之上：</p>
      <a-space>
        <a-button @click="showNormalNotification">普通通知</a-button>
        <a-button type="primary" @click="showHighPriorityNotification">高优先级通知</a-button>
      </a-space>
    </a-modal>

    <!-- 报警视频监控弹窗 -->
    <AlarmVideoMonitorModal
      v-model:open="alarmModalVisible"
      :alarm-data="testAlarmData"
      @confirm="handleAlarmConfirm"
      @close="handleAlarmClose"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useMessage } from '/@/hooks/web/useMessage';
import AlarmVideoMonitorModal from '/@/components/AlarmVideoMonitorModal/index.vue';

const { notification } = useMessage();

// 响应式数据
const testModalVisible = ref(false);
const alarmModalVisible = ref(false);

// 测试报警数据
const testAlarmData = ref({
  id: 'test_notification_001',
  alarmId: 'test_notification_001',
  msgTxt: '层级测试报警 - 请观察通知显示层级',
  msgDesc: '这是一个用于测试 notification 层级的模拟报警',
  timestamp: new Date(),
  location: '测试区域',
  deviceId: 'TEST-CAM-001',
  videoList: [
    {
      id: 'test-video-1',
      name: '测试摄像头-01',
      videoUrl: 'http://test.example.com/video1.mp4',
      streamId: 'test-stream-001',
      websocketUrl: 'ws://test.example.com/stream1',
      cameraIndexCode: 'TEST-CAM-001',
      streamType: 'preview',
      rtspUrl: 'rtsp://test.example.com/stream1'
    }
  ]
});

// 显示普通 notification
const showNormalNotification = () => {
  notification.info({
    message: '普通通知',
    description: '这是一个普通的 notification，使用默认层级',
  });
};

// 显示高优先级 notification
const showHighPriorityNotification = () => {
  notification.warning({
    message: '高优先级通知',
    description: '这是一个高优先级的 notification，应该显示在所有弹窗之上',
    style: {
      zIndex: 10000,
    },
    class: 'high-priority-notification'
  });
};

// 显示弹窗并触发通知
const showModalWithNotification = () => {
  testModalVisible.value = true;
  setTimeout(() => {
    showHighPriorityNotification();
  }, 500);
};

// 显示报警弹窗并触发通知
const showAlarmWithNotification = () => {
  alarmModalVisible.value = true;
  setTimeout(() => {
    notification.error({
      message: '紧急通知',
      description: '这是在报警弹窗显示时的紧急通知，应该显示在报警弹窗之上',
      style: {
        zIndex: 10000,
      },
      class: 'high-priority-notification'
    });
  }, 500);
};

// 测试报警处理成功
const testAlarmSuccess = () => {
  notification.success({
    message: '报警处理成功',
    description: '报警已确认处理，相关信息已记录',
    style: {
      zIndex: 10000,
    },
    class: 'high-priority-notification'
  });
};

// 测试报警处理失败
const testAlarmError = () => {
  notification.error({
    message: '报警处理失败',
    description: '网络连接异常，请检查网络后重试',
    style: {
      zIndex: 10000,
    },
    class: 'high-priority-notification'
  });
};

// 处理报警确认
const handleAlarmConfirm = (data: any) => {
  console.log('测试报警确认:', data);
  testAlarmSuccess();
  alarmModalVisible.value = false;
};

// 处理报警关闭
const handleAlarmClose = () => {
  console.log('测试报警弹窗关闭');
};
</script>

<style lang="less" scoped>
.notification-test {
  padding: 32px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  min-height: 100vh;
  color: #ffffff;
}

.test-header {
  text-align: center;
  margin-bottom: 32px;
  
  h1 {
    color: #00d4ff;
    font-size: 28px;
    margin-bottom: 12px;
    font-weight: 600;
  }
  
  p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    margin: 0;
  }
}

.test-controls {
  max-width: 800px;
  margin: 0 auto 32px;
  
  .control-group {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(0, 212, 255, 0.3);
    
    h3 {
      color: #00d4ff;
      margin-bottom: 12px;
      font-size: 16px;
      font-weight: 600;
    }
  }
}

.test-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.info-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 24px;
  border: 1px solid rgba(0, 212, 255, 0.3);
  
  h3 {
    color: #00d4ff;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
  }
  
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    
    li {
      padding: 6px 0;
      padding-left: 16px;
      position: relative;
      color: rgba(255, 255, 255, 0.9);
      font-size: 13px;
      line-height: 1.4;
      
      &:before {
        content: '•';
        position: absolute;
        left: 0;
        color: #00d4ff;
      }
      
      strong {
        color: #ffffff;
      }
    }
  }
}

@media (max-width: 768px) {
  .test-info {
    grid-template-columns: 1fr;
  }
}
</style>
