<template>
  <div class="police-patrol-dashboard fullscreen-mode">
    <!-- 标题栏 -->
    <div class="dashboard-header">
      <div class="header-title">
        <div class="title-glow">{{ dashboardData.title }}</div>
        <div class="subtitle">监区民警巡更实时监控</div>
      </div>
      <div class="header-actions">
        <div class="header-time">{{ currentTime }}</div>
        <a-button
          type="default"
          class="exit-fullscreen-btn"
          @click="exitFullscreen"
          :icon="h(FullscreenExitOutlined)"
        >
          退出全屏
        </a-button>
      </div>
    </div>

    <!-- 民警信息区域 -->
    <div class="police-info-section">
      <div class="section-title">
        <Icon icon="ant-design:team-outlined" />
        <span>在岗民警信息</span>
      </div>
      <div class="police-cards">
        <div
          v-for="police in dashboardData.policeList"
          :key="police.id"
          class="police-card"
          :class="{ 'on-duty': police.status === 'on-duty' }"
        >
          <div class="police-avatar">
            <img 
              v-if="police.avatar" 
              :src="getFileAccessHttpUrl(police.avatar)" 
              :alt="police.name"
              @error="handleImageError"
            />
            <Icon v-else icon="ant-design:user-outlined" />
            <div class="status-indicator" :class="police.status"></div>
          </div>
          <div class="police-info">
            <div class="police-name">{{ police.name }}</div>
            <div class="police-badge">警号: {{ police.badge }}</div>
            <div class="police-department">{{ police.department }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 巡更计划区域 -->
    <div class="patrol-plans-section">
      <div class="section-title">
        <Icon icon="ant-design:schedule-outlined" />
        <span>巡更计划执行情况</span>
      </div>
      
      <!-- 统计信息 -->
      <div class="statistics-bar">
        <div class="stat-item">
          <div class="stat-value">{{ dashboardData.statistics.totalPlans }}</div>
          <div class="stat-label">总计划数</div>
        </div>
        <div class="stat-item in-progress">
          <div class="stat-value">{{ dashboardData.statistics.inProgressPlans }}</div>
          <div class="stat-label">进行中</div>
        </div>
        <div class="stat-item completed">
          <div class="stat-value">{{ dashboardData.statistics.completedPlans }}</div>
          <div class="stat-label">已完成</div>
        </div>
        <div class="stat-item missed">
          <div class="stat-value">{{ dashboardData.statistics.missedPlans }}</div>
          <div class="stat-label">已超时</div>
        </div>
        <div class="stat-divider"></div>
        <div class="stat-item">
          <div class="stat-value">{{ dashboardData.statistics.totalCheckpoints }}</div>
          <div class="stat-label">总打卡点</div>
        </div>
        <div class="stat-item completed">
          <div class="stat-value">{{ dashboardData.statistics.completedCheckpoints }}</div>
          <div class="stat-label">已打卡</div>
        </div>
        <div class="stat-item missed">
          <div class="stat-value">{{ dashboardData.statistics.missedCheckpoints }}</div>
          <div class="stat-label">漏检</div>
        </div>
      </div>

      <!-- 巡更计划列表 -->
      <div class="patrol-plans-list">
        <div
          v-for="plan in sortedPatrolPlans"
          :key="plan.id"
          class="patrol-plan-item"
          :class="plan.status"
        >
          <!-- 计划基本信息 -->
          <div class="plan-header">
            <div class="plan-info">
              <div class="plan-name">{{ plan.name }}</div>
              <div class="plan-details">
                <span class="plan-route">{{ plan.routeName }}</span>
                <span class="plan-time">{{ plan.startTime }} - {{ plan.endTime }}</span>
                <span class="plan-officer">{{ plan.patrolOfficer.name }}</span>
                <span class="plan-duration">{{ plan.duration }}</span>
              </div>
            </div>
            <div class="plan-status">
              <div class="status-badge" :class="plan.status">
                <Icon :icon="getStatusIcon(plan.status)" />
                <span>{{ getStatusText(plan.status) }}</span>
              </div>
              <div v-if="plan.status === 'in-progress' && plan.progress" class="progress-info">
                {{ plan.progress }}%
              </div>
            </div>
          </div>

          <!-- 打卡点信息 -->
          <div class="checkpoints-section">
            <div class="checkpoints-title">打卡信息</div>
            <div class="checkpoints-list">
              <div
                v-for="checkpoint in plan.checkpoints"
                :key="checkpoint.id"
                class="checkpoint-item"
                :class="checkpoint.status"
              >
                <div class="checkpoint-section">{{ checkpoint.sectionName }}</div>
                <div class="checkpoint-name">{{ checkpoint.name }}</div>
                <div class="checkpoint-status">
                  <Icon :icon="getCheckpointIcon(checkpoint.status)" />
                  <span>{{ getCheckpointStatusText(checkpoint.status) }}</span>
                </div>
                <div class="checkpoint-time">
                  {{ checkpoint.checkTime || '--:--' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed, h } from 'vue';
import { useRouter } from 'vue-router';
import { Icon } from '/@/components/Icon';
import { FullscreenExitOutlined } from '@ant-design/icons-vue';
import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';
import dayjs from 'dayjs';
import { 
  generateMockPolicePatrolData, 
  type PolicePatrolDashboardData
} from './PolicePatrolDashboard.api';

const router = useRouter();

// 响应式数据
const currentTime = ref('');
const dashboardData = ref<PolicePatrolDashboardData>({
  title: '监区民警巡更数据大屏',
  policeList: [],
  patrolPlans: [],
  statistics: {
    totalPlans: 0,
    completedPlans: 0,
    inProgressPlans: 0,
    missedPlans: 0,
    totalCheckpoints: 0,
    completedCheckpoints: 0,
    missedCheckpoints: 0
  }
});

// 计算属性 - 按状态排序的巡更计划
const sortedPatrolPlans = computed(() => {
  const statusOrder = { 'in-progress': 0, 'pending': 1, 'completed': 2, 'missed': 3 };
  return [...dashboardData.value.patrolPlans].sort((a, b) => {
    return statusOrder[a.status] - statusOrder[b.status];
  });
});

// 时间更新
let timeInterval: NodeJS.Timeout;

const updateTime = () => {
  currentTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
};

// 退出全屏
const exitFullscreen = () => {
  router.push('/patrol/police-patrol');
};

// 状态图标和文本
const getStatusIcon = (status: string) => {
  const icons = {
    'pending': 'ant-design:clock-circle-outlined',
    'in-progress': 'ant-design:play-circle-outlined',
    'completed': 'ant-design:check-circle-outlined',
    'missed': 'ant-design:exclamation-circle-outlined'
  };
  return icons[status] || 'ant-design:question-circle-outlined';
};

const getStatusText = (status: string) => {
  const texts = {
    'pending': '待开始',
    'in-progress': '进行中',
    'completed': '已完成',
    'missed': '已超时'
  };
  return texts[status] || '未知';
};

const getCheckpointIcon = (status: string) => {
  const icons = {
    'pending': 'ant-design:clock-circle-outlined',
    'completed': 'ant-design:check-circle-filled',
    'missed': 'ant-design:close-circle-filled'
  };
  return icons[status] || 'ant-design:question-circle-outlined';
};

const getCheckpointStatusText = (status: string) => {
  const texts = {
    'pending': '待巡',
    'completed': '正常',
    'missed': '漏检'
  };
  return texts[status] || '未知';
};

// 图片错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.style.display = 'none';
};

// 加载数据
const loadData = async () => {
  try {
    dashboardData.value = generateMockPolicePatrolData();
  } catch (error) {
    console.error('加载数据失败:', error);
  }
};

// 生命周期
onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);
  loadData();
  
  // 自动进入全屏
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen().catch(console.error);
  }
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
  
  // 退出全屏
  if (document.fullscreenElement) {
    document.exitFullscreen().catch(console.error);
  }
});
</script>

<style lang="less" scoped>
// 复制主组件的样式并添加全屏优化
.police-patrol-dashboard {
  z-index: 100 !important;
  min-height: 100vh;
  background: linear-gradient(135deg,
    #0a0e27 0%,
    #1a1f3a 25%,
    #2a2f4a 50%,
    #1a1f3a 75%,
    #0a0e27 100%
  );
  color: #ffffff;
  padding: 30px;
  position: relative;
  overflow: hidden;

  // 科技感背景效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, rgba(0, 255, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }

  // 确保内容在背景之上
  > * {
    position: relative;
    z-index: 1;
  }

  &.fullscreen-mode {
    .dashboard-header {
      .header-title .title-glow {
        font-size: clamp(32px, 4vw, 48px);
      }

      .header-title .subtitle {
        font-size: clamp(18px, 2vw, 24px);
      }
    }

    .police-info-section .police-cards {
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }

    .patrol-plans-section .patrol-plans-list {
      max-height: 75vh;
    }
  }
}

// 标题栏样式
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px 30px;
  background: linear-gradient(135deg,
    rgba(10, 14, 39, 0.8) 0%,
    rgba(26, 31, 58, 0.6) 50%,
    rgba(10, 14, 39, 0.8) 100%
  );
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(20px);
  box-shadow:
    0 8px 32px rgba(0, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);

  .header-title {
    .title-glow {
      font-size: clamp(24px, 3vw, 36px);
      font-weight: bold;
      background: linear-gradient(45deg, #00ffff, #ffffff, #00ffff);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
      margin-bottom: 8px;
    }

    .subtitle {
      font-size: clamp(14px, 1.5vw, 18px);
      color: rgba(255, 255, 255, 0.8);
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 20px;

    .header-time {
      font-size: clamp(16px, 1.8vw, 20px);
      font-weight: 500;
      color: #00ffff;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
      font-family: 'Courier New', monospace;
    }

    .exit-fullscreen-btn {
      height: 40px;
      border-radius: 8px;
      border: 1px solid rgba(0, 255, 255, 0.4);
      background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(0, 255, 255, 0.2));
      color: #ffffff;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        border-color: rgba(0, 255, 255, 0.6);
        background: linear-gradient(135deg, rgba(0, 255, 255, 0.2), rgba(0, 255, 255, 0.3));
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
        transform: translateY(-2px);
      }
    }
  }
}

// 民警信息区域
.police-info-section {
  margin-bottom: 30px;

  .section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: clamp(18px, 2vw, 24px);
    font-weight: bold;
    color: #00ffff;
    margin-bottom: 20px;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);

    :deep(.anticon) {
      font-size: clamp(20px, 2.2vw, 26px);
    }
  }

  .police-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
  }

  .police-card {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: linear-gradient(135deg,
      rgba(10, 14, 39, 0.6) 0%,
      rgba(26, 31, 58, 0.4) 50%,
      rgba(10, 14, 39, 0.6) 100%
    );
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    backdrop-filter: blur(15px);
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(0, 255, 255, 0.4);
      box-shadow: 0 8px 25px rgba(0, 255, 255, 0.15);
      transform: translateY(-3px);
    }

    &.on-duty {
      border-color: rgba(0, 255, 136, 0.4);

      &:hover {
        border-color: rgba(0, 255, 136, 0.6);
        box-shadow: 0 8px 25px rgba(0, 255, 136, 0.2);
      }
    }

    .police-avatar {
      position: relative;
      width: 80px;
      height: 80px;
      border-radius: 8px;
      overflow: hidden;
      border: 2px solid rgba(0, 255, 255, 0.4);
      box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center top;
      }

      :deep(.anticon) {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        color: rgba(255, 255, 255, 0.6);
        background: rgba(0, 255, 255, 0.1);
      }

      .status-indicator {
        position: absolute;
        bottom: -5px;
        right: -5px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2px solid #ffffff;
        background: #ff4444;

        &.on-duty {
          background: #00ff88;
          animation: pulse 2s infinite;
        }

        &.offline {
          background: #666666;
        }
      }
    }

    .police-info {
      flex: 1;

      .police-name {
        font-size: clamp(16px, 1.8vw, 20px);
        font-weight: bold;
        color: #ffffff;
        margin-bottom: 8px;
        text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
      }

      .police-badge {
        font-size: clamp(14px, 1.4vw, 16px);
        color: #00ffff;
        margin-bottom: 4px;
        font-family: 'Courier New', monospace;
      }

      .police-department {
        font-size: clamp(13px, 1.3vw, 15px);
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}

// 巡更计划区域
.patrol-plans-section {
  .section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: clamp(18px, 2vw, 24px);
    font-weight: bold;
    color: #00ffff;
    margin-bottom: 20px;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);

    :deep(.anticon) {
      font-size: clamp(20px, 2.2vw, 26px);
    }
  }

  // 统计信息栏
  .statistics-bar {
    display: flex;
    align-items: center;
    gap: 30px;
    margin-bottom: 25px;
    padding: 20px 30px;
    background: linear-gradient(135deg,
      rgba(10, 14, 39, 0.6) 0%,
      rgba(26, 31, 58, 0.4) 50%,
      rgba(10, 14, 39, 0.6) 100%
    );
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 12px;
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 20px rgba(0, 255, 255, 0.1);

    .stat-item {
      text-align: center;

      .stat-value {
        font-size: clamp(20px, 2.5vw, 28px);
        font-weight: bold;
        color: #ffffff;
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: clamp(12px, 1.2vw, 14px);
        color: rgba(255, 255, 255, 0.8);
      }

      &.in-progress {
        .stat-value {
          color: #faad14;
          text-shadow: 0 0 10px rgba(250, 173, 20, 0.5);
        }
      }

      &.completed {
        .stat-value {
          color: #52c41a;
          text-shadow: 0 0 10px rgba(82, 196, 26, 0.5);
        }
      }

      &.missed {
        .stat-value {
          color: #ff4d4f;
          text-shadow: 0 0 10px rgba(255, 77, 79, 0.5);
        }
      }
    }

    .stat-divider {
      width: 1px;
      height: 40px;
      background: linear-gradient(to bottom,
        transparent 0%,
        rgba(0, 255, 255, 0.5) 50%,
        transparent 100%
      );
    }
  }

  // 巡更计划列表
  .patrol-plans-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-height: 60vh;
    overflow-y: auto;
    padding-right: 10px;

    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(to bottom, #00ffff, rgba(0, 255, 255, 0.6));
      border-radius: 4px;

      &:hover {
        background: linear-gradient(to bottom, #00ffff, rgba(0, 255, 255, 0.8));
      }
    }
  }

  .patrol-plan-item {
    padding: 25px;
    background: linear-gradient(135deg,
      rgba(10, 14, 39, 0.6) 0%,
      rgba(26, 31, 58, 0.4) 50%,
      rgba(10, 14, 39, 0.6) 100%
    );
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    backdrop-filter: blur(15px);
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(0, 255, 255, 0.4);
      box-shadow: 0 8px 25px rgba(0, 255, 255, 0.15);
      transform: translateY(-2px);
    }

    &.in-progress {
      border-color: rgba(250, 173, 20, 0.4);

      &:hover {
        border-color: rgba(250, 173, 20, 0.6);
        box-shadow: 0 8px 25px rgba(250, 173, 20, 0.2);
      }
    }

    &.completed {
      border-color: rgba(82, 196, 26, 0.4);

      &:hover {
        border-color: rgba(82, 196, 26, 0.6);
        box-shadow: 0 8px 25px rgba(82, 196, 26, 0.2);
      }
    }

    &.missed {
      border-color: rgba(255, 77, 79, 0.4);

      &:hover {
        border-color: rgba(255, 77, 79, 0.6);
        box-shadow: 0 8px 25px rgba(255, 77, 79, 0.2);
      }
    }

    .plan-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20px;

      .plan-info {
        flex: 1;

        .plan-name {
          font-size: clamp(16px, 1.8vw, 20px);
          font-weight: bold;
          color: #ffffff;
          margin-bottom: 12px;
          text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
        }

        .plan-details {
          display: flex;
          align-items: center;
          gap: 20px;
          flex-wrap: wrap;

          > span {
            font-size: clamp(13px, 1.3vw, 15px);
            color: rgba(255, 255, 255, 0.8);
            padding: 4px 8px;
            background: rgba(0, 255, 255, 0.1);
            border-radius: 4px;
            border: 1px solid rgba(0, 255, 255, 0.2);
          }

          .plan-route {
            color: #00ffff;
            font-weight: 500;
          }

          .plan-time {
            color: #faad14;
            font-family: 'Courier New', monospace;
          }

          .plan-officer {
            color: #52c41a;
          }

          .plan-duration {
            color: #ffffff;
          }
        }
      }

      .plan-status {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 8px;

        .status-badge {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 6px 12px;
          border-radius: 6px;
          font-size: clamp(12px, 1.2vw, 14px);
          font-weight: 500;

          &.pending {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
          }

          &.in-progress {
            background: rgba(250, 173, 20, 0.2);
            color: #faad14;
            border: 1px solid rgba(250, 173, 20, 0.4);
            animation: statusPulse 2s infinite;
          }

          &.completed {
            background: rgba(82, 196, 26, 0.2);
            color: #52c41a;
            border: 1px solid rgba(82, 196, 26, 0.4);
          }

          &.missed {
            background: rgba(255, 77, 79, 0.2);
            color: #ff4d4f;
            border: 1px solid rgba(255, 77, 79, 0.4);
          }
        }

        .progress-info {
          font-size: clamp(12px, 1.2vw, 14px);
          color: #faad14;
          font-weight: 500;
          font-family: 'Courier New', monospace;
        }
      }
    }

    // 打卡点区域
    .checkpoints-section {
      .checkpoints-title {
        font-size: clamp(14px, 1.4vw, 16px);
        font-weight: bold;
        color: #00ffff;
        margin-bottom: 15px;
        text-shadow: 0 0 8px rgba(0, 255, 255, 0.3);
      }

      .checkpoints-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
      }

      .checkpoint-item {
        padding: 15px;
        background: linear-gradient(135deg,
          rgba(0, 0, 0, 0.3) 0%,
          rgba(26, 31, 58, 0.2) 50%,
          rgba(0, 0, 0, 0.3) 100%
        );
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          border-color: rgba(0, 255, 255, 0.3);
          box-shadow: 0 4px 15px rgba(0, 255, 255, 0.1);
        }

        &.completed {
          border-color: rgba(82, 196, 26, 0.4);

          &:hover {
            border-color: rgba(82, 196, 26, 0.6);
            box-shadow: 0 4px 15px rgba(82, 196, 26, 0.2);
          }
        }

        &.missed {
          border-color: rgba(255, 77, 79, 0.4);

          &:hover {
            border-color: rgba(255, 77, 79, 0.6);
            box-shadow: 0 4px 15px rgba(255, 77, 79, 0.2);
          }
        }

        .checkpoint-section {
          font-size: clamp(12px, 1.2vw, 14px);
          color: #00ffff;
          font-weight: 500;
          margin-bottom: 6px;
        }

        .checkpoint-name {
          font-size: clamp(13px, 1.3vw, 15px);
          color: #ffffff;
          font-weight: 500;
          margin-bottom: 8px;
        }

        .checkpoint-status {
          display: flex;
          align-items: center;
          gap: 6px;
          margin-bottom: 6px;

          :deep(.anticon) {
            font-size: 14px;
          }

          span {
            font-size: clamp(12px, 1.2vw, 14px);
            font-weight: 500;
          }
        }

        .checkpoint-time {
          font-size: clamp(12px, 1.2vw, 14px);
          color: #faad14;
          font-family: 'Courier New', monospace;
          text-align: right;
        }

        // 根据状态设置不同的文字颜色
        &.pending .checkpoint-status {
          color: rgba(255, 255, 255, 0.6);
        }

        &.completed .checkpoint-status {
          color: #52c41a;
        }

        &.missed .checkpoint-status {
          color: #ff4d4f;
        }
      }
    }
  }
}

// 动画效果
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
}

@keyframes statusPulse {
  0%, 100% {
    box-shadow: 0 0 5px rgba(250, 173, 20, 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(250, 173, 20, 0.6);
  }
}
</style>
