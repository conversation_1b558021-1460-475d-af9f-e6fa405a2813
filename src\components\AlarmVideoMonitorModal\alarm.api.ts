import { defHttp } from '/@/utils/http/axios';

enum Api {
  // 获取报警相关的视频监控列表
  getAlarmVideoList = '/patrol/alarm/getVideoList',
  // 获取设备相关的视频监控
  getDeviceVideoList = '/patrol/device/getVideoList',
  // 获取位置相关的视频监控
  getLocationVideoList = '/patrol/location/getVideoList',
}

/**
 * 获取报警相关的视频监控列表
 */
export const getAlarmVideoList = (params: {
  location?: string;
  deviceId?: string;
  alarmType?: string;
}) => {
  // 模拟数据，实际应该调用真实API
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟返回一些视频监控数据
      const mockVideoList = [
        {
          id: 'video_001',
          name: '监控点1 - 主入口',
          videoUrl: 'rtsp://admin:admin123@*************:554/h264/ch1/main/av_stream',
          streamId: 'stream_001',
          websocketUrl: 'ws://localhost:8080/video/stream_001',
          cameraIndexCode: 'CAM_001',
          streamType: 'rtsp',
          rtspUrl: 'rtsp://admin:admin123@*************:554/h264/ch1/main/av_stream'
        },
        {
          id: 'video_002',
          name: '监控点2 - 走廊',
          videoUrl: 'rtsp://admin:admin123@*************:554/h264/ch1/main/av_stream',
          streamId: 'stream_002',
          websocketUrl: 'ws://localhost:8080/video/stream_002',
          cameraIndexCode: 'CAM_002',
          streamType: 'rtsp',
          rtspUrl: 'rtsp://admin:admin123@*************:554/h264/ch1/main/av_stream'
        },
        {
          id: 'video_003',
          name: '监控点3 - 安全出口',
          videoUrl: 'rtsp://admin:admin123@*************:554/h264/ch1/main/av_stream',
          streamId: 'stream_003',
          websocketUrl: 'ws://localhost:8080/video/stream_003',
          cameraIndexCode: 'CAM_003',
          streamType: 'rtsp',
          rtspUrl: 'rtsp://admin:admin123@*************:554/h264/ch1/main/av_stream'
        },
        {
          id: 'video_004',
          name: '监控点4 - 后门',
          videoUrl: 'rtsp://admin:admin123@*************:554/h264/ch1/main/av_stream',
          streamId: 'stream_004',
          websocketUrl: 'ws://localhost:8080/video/stream_004',
          cameraIndexCode: 'CAM_004',
          streamType: 'rtsp',
          rtspUrl: 'rtsp://admin:admin123@*************:554/h264/ch1/main/av_stream'
        }
      ];
      
      resolve(mockVideoList);
    }, 500);
  });
  
  // 真实API调用代码（注释掉）
  // return defHttp.get<any>({
  //   url: Api.getAlarmVideoList,
  //   params,
  // });
};

/**
 * 获取设备相关的视频监控
 */
export const getDeviceVideoList = (deviceId: string) => {
  return defHttp.get<any>({
    url: Api.getDeviceVideoList,
    params: { deviceId },
  });
};

/**
 * 获取位置相关的视频监控
 */
export const getLocationVideoList = (location: string) => {
  return defHttp.get<any>({
    url: Api.getLocationVideoList,
    params: { location },
  });
};
