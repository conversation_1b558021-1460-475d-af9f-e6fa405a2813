<template>
  <j-modal :title="title" :width="width" v-model:open="open" :footer="null" @cancel="handleCancel" :z-index="10" style="margin-top: 50px;">
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection" style="margin-top:5px;margin-left: 2px;">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'cardVideo:patrol_card_video:add'"  @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
      <template v-slot:bodyCell="{ column, record, index, text }">
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <CardVideoModal ref="registerModal" @success="handleSuccess"></CardVideoModal>
  </j-modal>
</template>

<script setup lang="ts">
import {nextTick, ref, defineExpose, reactive} from "vue"
import JModal from "@/components/Modal/src/JModal/JModal.vue";

import { defHttp } from '/@/utils/http/axios';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage';
import { columns } from '../../cardVideo/CardVideo.data';
import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from '../../cardVideo/CardVideo.api';
import CardVideoModal from '../../cardVideo/components/CardVideoModal.vue'
import { useUserStore } from '/@/store/modules/user';

const width = ref<number>(1200);
const title = ref<string>('监控');
const open = ref<boolean>(false)
const queryParam = reactive<any>({});
const info = ref<any>([]);
const emit = defineEmits(['register', 'success']);
const registerModal = ref();
const cardId = ref("");

function showModal(record) {
  open.value = true
  cardId.value = record.id;
  queryParam.cardId = record.id;
  nextTick(() => {
    reload();
  });
}

const formRef = ref();
const toggleSearchStatus = ref<boolean>(false);
const userStore = useUserStore();

//注册table数据
const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
  tableProps: {
    title: '卡片监控表',
    api: list,
    columns,
    canResize:false,
    useSearchForm: false,
    actionColumn: {
      width: 120,
      fixed: 'right',
    },
    beforeFetch: async (params) => {
      return Object.assign(params, queryParam);
    },
  },
  exportConfig: {
    name: "卡片监控表",
    url: getExportUrl,
    params: queryParam,
  },
  importConfig: {
    url: getImportUrl,
    success: handleSuccess
  },
});

const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] = tableContext;

const labelCol = reactive({
  xs:24,
  sm:4,
  xl:6,
  xxl:4
});

const wrapperCol = reactive({
  xs: 24,
  sm: 20,
});

/**
 * 新增事件
 */
function handleAdd() {
  registerModal.value.disableSubmit = false;
  registerModal.value.add({cardId: queryParam.cardId});
}

/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  registerModal.value.disableSubmit = false;
  registerModal.value.edit(record);
}

/**
 * 详情
 */
function handleDetail(record: Recordable) {
  registerModal.value.disableSubmit = true;
  registerModal.value.edit(record);
}

/**
 * 删除事件
 */
async function handleDelete(record) {
  await deleteOne({ id: record.id }, handleSuccess);
}

/**
 * 批量删除事件
 */
async function batchHandleDelete() {
  await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
}

/**
 * 成功回调
 */
function handleSuccess() {
  selectedRowKeys.value = [];
  nextTick(() => {
    reload();
  });
}

/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      auth: 'cardVideo:patrol_card_video:edit'
    },
    {
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDelete.bind(null, record),
        placement: 'topLeft',
      },
      auth: 'cardVideo:patrol_card_video:delete'
    }
  ];
}

/**
 * 下拉操作栏
 */
function getDropDownAction(record) {
  return [
    {
      label: '详情',
      onClick: handleDetail.bind(null, record),
    }, {
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDelete.bind(null, record),
        placement: 'topLeft',
      },
      auth: 'cardVideo:patrol_card_video:delete'
    }
  ]
}

/**
 * 重置
 */
function searchReset() {
  formRef.value.resetFields();
  selectedRowKeys.value = [];
  //刷新数据
  nextTick(() => {
    reload();
  });
}

/**
 * 取消按钮回调时间
 */
function handleCancel() {
  open.value = false;
  // 触发父组件刷新数据
  emit('success');
}

defineExpose({
  showModal
});
</script>

<style scoped lang="less">
.container {
  min-height: 100vh;
  background: #f0f2f5;
}

.content {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}
</style>
