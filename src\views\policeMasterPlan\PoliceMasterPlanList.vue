<template>
  <div class="p-2">
    <a-row :gutter="24">
      <!-- 左侧路线列表 -->
      <a-col :xs="24" :sm="24" :md="5" :lg="4" :xl="3">
        <div class="route-list-container">
          <h3 style="margin-bottom: 16px;">路线列表</h3>
          <a-menu
            mode="inline"
            :selectedKeys="[queryParam.lineId || 'all']"
            @select="handleLineSelect"
          >
            <!-- 全部选项 -->
            <a-menu-item key="all">全部</a-menu-item>
            <!-- 动态路线列表 -->
            <a-menu-item
              v-for="route in routeList"
              :key="route.id"
            >
              {{ route.name }}
            </a-menu-item>
          </a-menu>
        </div>
      </a-col>

      <!-- 右侧主要内容区域 -->
      <a-col :xs="24" :sm="24" :md="19" :lg="20" :xl="21">
        <!--查询区域-->
        <div class="jeecg-basic-table-form-container">
          <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
            <a-row :gutter="24">
              <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8" :xxl="6">
                <a-form-item label="总计划名称">
                  <a-input
                    v-model:value="queryParam.masterPlanName"
                    placeholder="请输入总计划名称"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8" :xxl="6">
                <a-form-item label="日期">
                  <a-date-picker
                    v-model:value="queryParam.createDate"
                    placeholder="请选择日期"
                    style="width: 100%"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    allow-clear
                    @change="handleDateChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8" :xxl="6">
                <a-form-item >
                  <a-space>
                    <a-button type="primary" @click="searchQuery" preIcon="ant-design:search-outlined">
                      查询
                    </a-button>
                    <a-button @click="searchReset" preIcon="ant-design:reload-outlined">
                      重置
                    </a-button>
                  </a-space>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!--引用表格-->
        <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'policeMasterPlan:patrol_police_master_plan:add'"  @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <a-button  type="primary" v-auth="'policeMasterPlan:patrol_police_master_plan:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
        <j-upload-button  type="primary" v-auth="'policeMasterPlan:patrol_police_master_plan:importExcel'"  preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'policeMasterPlan:patrol_police_master_plan:deleteBatch'">批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
       
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
          <template v-slot:bodyCell="{ column, record, index, text }">
          </template>
        </BasicTable>
        <!-- 表单区域 -->
        <PoliceMasterPlanModal ref="registerModal" @success="handleSuccess"></PoliceMasterPlanModal>
      </a-col>
    </a-row>

    <!-- 巡更记录弹窗 -->
    <a-modal
      title="查看巡更记录"
      :width="1400"
      v-model:open="patrolRecordModalVisible"
      @cancel="handlePatrolRecordCancel"
      :footer="null"
      :destroyOnClose="true"
    >
      <div style="height: 600px; overflow: auto;">
        <PolicePlanList
          v-if="patrolRecordModalVisible"
          :embedded="true"
          :masterPlanId="currentMasterPlanId"
          :hideToolbar="true"
        />
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" name="policeMasterPlan-policeMasterPlan" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, superQuerySchema } from './PoliceMasterPlan.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from './PoliceMasterPlan.api';
  import PoliceMasterPlanModal from './components/PoliceMasterPlanModal.vue'
  import PolicePlanList from '/@/views/policePlan/PolicePlanList.vue'
  import { getPoliceLines } from '/@/views/line/Line.api';
  import dayjs from 'dayjs';

  const formRef = ref();
  const queryParam = reactive<any>({
    lineId: null,
    masterPlanName: '',
    createDate: dayjs().format('YYYY-MM-DD') // 默认为今天
  });
  const registerModal = ref();

  // 路线相关变量
  const routeList = ref<any[]>([]);

  // 巡更记录弹窗相关变量
  const patrolRecordModalVisible = ref(false);
  const currentMasterPlanId = ref('');
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '民警巡更总表',
      api: list,
      columns,
      canResize:false,
      useSearchForm: false,
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: async (params) => {
        // 处理查询参数
        const searchParams = { ...queryParam };

        // 处理创建日期查询
        if (searchParams.createDate) {
          searchParams.createTime_begin = searchParams.createDate + ' 00:00:00';
          searchParams.createTime_end = searchParams.createDate + ' 23:59:59';
          delete searchParams.createDate;
        }

        return Object.assign(params, searchParams);
      },
    },
    exportConfig: {
      name: "民警巡更总表",
      url: getExportUrl,
      params: queryParam,
    },
	  importConfig: {
	    url: getImportUrl,
	    success: handleSuccess
	  },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] = tableContext;
  const labelCol = reactive({
    xs: { span: 24 },
    sm: { span: 6 },
    md: { span: 6 },
    lg: { span: 6 },
    xl: { span: 6 },
    xxl: { span: 6 }
  });
  const wrapperCol = reactive({
    xs: { span: 24 },
    sm: { span: 18 },
    md: { span: 18 },
    lg: { span: 18 },
    xl: { span: 18 },
    xxl: { span: 18 }
  });

  // 初始化路线数据
  onMounted(async () => {
    try {
      console.log('开始获取用户管理的路线数据...');

      // 使用 getUserManagedLines API 获取当前用户管理的路线
      const response = await getPoliceLines({});
      console.log('getUserManagedLines API 返回数据:', response);

      if (response && response.success && response.result) {
        // 处理返回的路线数据
        const lines = response.result;
        if (Array.isArray(lines)) {
          routeList.value = lines.map(item => ({
            id: item.id,
            name: item.name || item.lineName
          }));
        } else {
          console.warn('返回的路线数据不是数组格式:', lines);
          routeList.value = [];
        }
      } else {
        console.warn('获取路线数据失败或返回格式不正确:', response);
        routeList.value = [];
      }

      console.log('处理后的路线数据:', routeList.value);

    } catch (error) {
      console.error('获取用户管理的路线失败:', error);
      routeList.value = [];
    }
  });

  // 路线选择事件处理
  const handleLineSelect = ({ key }: { key: string }) => {
    queryParam.lineId = key === 'all' ? null : key;  // 'all' 对应全部
    searchQuery();  // 触发查询
  };

  // 日期变化事件处理
  const handleDateChange = (date: any, dateString: string) => {
    console.log('日期变化:', date, dateString);
    // 当日期变化时自动触发查询
    if (dateString !== undefined) {  // 包括清空日期的情况
      searchQuery();
    }
  };

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    searchQuery();
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    registerModal.value.disableSubmit = false;
    registerModal.value.add();
  }
  
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    registerModal.value.disableSubmit = false;
    registerModal.value.edit(record);
  }
   
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    registerModal.value.disableSubmit = true;
    registerModal.value.edit(record);
  }

  /**
   * 查看巡更记录
   */
  function handlePlan(record: Recordable){
    console.log('查看巡更记录:', record);
    currentMasterPlanId.value = record.id;
    patrolRecordModalVisible.value = true;
  }

  /**
   * 关闭巡更记录弹窗
   */
  function handlePatrolRecordCancel() {
    patrolRecordModalVisible.value = false;
    currentMasterPlanId.value = '';
  }
   
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
   
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }
   
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
   
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '查看巡更记录',
        onClick: handlePlan.bind(null, record),
      },
      // {
      //   label: '编辑',
      //   onClick: handleEdit.bind(null, record),
      //   auth: 'policeMasterPlan:patrol_police_master_plan:edit'
      // },
    ];
  }
   
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      }, {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'policeMasterPlan:patrol_police_master_plan:delete'
      }
    ]
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload();
  }
  
  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    // 重置查询参数
    queryParam.masterPlanName = '';
    queryParam.createDate = dayjs().format('YYYY-MM-DD'); // 重置为今天
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }
  




</script>

<style lang="less" scoped>
  .route-list-container {
    background: #fff;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    h3 {
      color: #333;
      font-weight: 600;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 8px;
    }

    :deep(.ant-menu) {
      border: none;

      .ant-menu-item {
        margin: 4px 0;
        border-radius: 4px;

        &:hover {
          background-color: #f5f5f5;
        }

        &.ant-menu-item-selected {
          background-color: #e6f7ff;
          color: #1890ff;
        }
      }
    }
  }

  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
    .query-group-cust{
      min-width: 100px !important;
    }
    .query-group-split-cust{
      width: 30px;
      display: inline-block;
      text-align: center
    }
    .ant-form-item:not(.ant-form-item-with-help){
      margin-bottom: 16px;
      height: 32px;
    }
    :deep(.ant-picker),:deep(.ant-input-number){
      width: 100%;
    }
  }
</style>
