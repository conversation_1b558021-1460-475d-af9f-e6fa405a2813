<template>
  <j-modal :title="'视频播放测试'" :width="800" v-model:open="open" :footer="null" @cancel="handleCancel" class="tech-modal">
    <div class="p-6">
      <div class="mb-4">
        <h3 class="text-lg font-semibold mb-2">测试视频格式支持</h3>
        <div class="bg-gray-50 p-4 rounded-lg">
          <p class="text-sm text-gray-600 mb-2">测试URL格式：</p>
          <code class="text-xs bg-white p-2 rounded border block">
            http://***********:554/openUrl/3whQVPi.m3u8?beginTime=20170615T000000&endTime=20170617T000000&playBackMode=1
          </code>
        </div>
      </div>

      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          输入视频URL进行测试：
        </label>
        <input 
          v-model="testVideoUrl" 
          type="text" 
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="请输入视频URL"
        />
        <button 
          @click="loadTestVideo" 
          class="mt-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          加载测试视频
        </button>
      </div>

      <div class="mb-4">
        <h4 class="text-md font-medium mb-2">检测结果：</h4>
        <div class="bg-gray-50 p-3 rounded-lg text-sm">
          <p><strong>HLS.js支持：</strong> {{ hlsSupported ? '✅ 支持' : '❌ 不支持' }}</p>
          <p><strong>URL格式检测：</strong> {{ isHlsFormat ? '✅ HLS格式' : '❌ 非HLS格式' }}</p>
          <p><strong>浏览器原生HLS支持：</strong> {{ nativeHlsSupported ? '✅ 支持' : '❌ 不支持' }}</p>
          <p><strong>URL连通性：</strong> {{ urlConnectivity }}</p>
        </div>
      </div>

      <!-- 诊断面板 -->
      <div class="mb-4" v-if="showDiagnostics">
        <h4 class="text-md font-medium mb-2">诊断信息：</h4>
        <div class="bg-blue-50 p-3 rounded-lg text-sm">
          <div class="mb-2">
            <strong>常见HLS错误解决方案：</strong>
          </div>
          <ul class="list-disc list-inside space-y-1 text-xs">
            <li><strong>manifestLoadError:</strong> 检查URL是否正确，服务器是否支持CORS</li>
            <li><strong>networkError:</strong> 检查网络连接，确认服务器在线</li>
            <li><strong>manifestParsingError:</strong> 检查m3u8文件格式是否正确</li>
            <li><strong>fragLoadError:</strong> 视频片段加载失败，可能是网络问题</li>
          </ul>
          <div class="mt-2 pt-2 border-t border-blue-200">
            <strong>调试建议：</strong>
            <ul class="list-disc list-inside space-y-1 text-xs mt-1">
              <li>在浏览器中直接访问m3u8 URL查看内容</li>
              <li>检查浏览器控制台的网络请求</li>
              <li>确认服务器返回正确的Content-Type</li>
              <li>尝试使用VLC等播放器测试URL</li>
            </ul>
          </div>
        </div>
        <button
          @click="showDiagnostics = false"
          class="mt-2 text-xs text-blue-600 hover:text-blue-800"
        >
          隐藏诊断信息
        </button>
      </div>
      <div v-else class="mb-4">
        <button
          @click="showDiagnostics = true"
          class="text-xs text-blue-600 hover:text-blue-800"
        >
          显示诊断信息
        </button>
      </div>

      <div class="video-container">
        <h4 class="text-md font-medium mb-2">视频播放测试：</h4>
        <div class="relative bg-black rounded-lg overflow-hidden" style="aspect-ratio: 16/9;">
          <video 
            ref="testVideoElement"
            class="w-full h-full"
            controls
            preload="metadata"
            @loadstart="onLoadStart"
            @loadedmetadata="onLoadedMetadata"
            @canplay="onCanPlay"
            @error="onError"
          >
            您的浏览器不支持视频播放
          </video>
          <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div class="text-white">加载中...</div>
          </div>
        </div>
        
        <div v-if="errorMessage" class="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p class="text-red-700 text-sm">{{ errorMessage }}</p>
        </div>
        
        <div v-if="successMessage" class="mt-2 p-3 bg-green-50 border border-green-200 rounded-lg">
          <p class="text-green-700 text-sm">{{ successMessage }}</p>
        </div>
      </div>
    </div>
  </j-modal>
</template>

<script setup lang="ts">
import { ref, onBeforeUnmount } from 'vue';
import Hls from 'hls.js';

const open = ref<boolean>(false);
const testVideoElement = ref<HTMLVideoElement | null>(null);
const hlsInstance = ref<Hls | null>(null);

// 测试数据
const testVideoUrl = ref('http://***********:554/openUrl/3whQVPi.m3u8?beginTime=20170615T000000&endTime=20170617T000000&playBackMode=1');
const hlsSupported = ref(false);
const isHlsFormat = ref(false);
const nativeHlsSupported = ref(false);
const loading = ref(false);
const errorMessage = ref('');
const successMessage = ref('');
const urlConnectivity = ref('未测试');
const showDiagnostics = ref(false);

// 检测支持情况
const checkSupport = () => {
  hlsSupported.value = Hls.isSupported();

  if (testVideoElement.value) {
    nativeHlsSupported.value = testVideoElement.value.canPlayType('application/vnd.apple.mpegurl') !== '';
  }

  isHlsFormat.value = testVideoUrl.value && (
    testVideoUrl.value.includes('.m3u8') ||
    testVideoUrl.value.includes('openUrl')
  );
};

// 测试URL连通性
const testUrlConnectivity = async (url: string): Promise<boolean> => {
  try {
    urlConnectivity.value = '测试中...';
    await fetch(url, {
      method: 'HEAD',
      mode: 'no-cors'
    });
    urlConnectivity.value = '✅ 可连接';
    return true;
  } catch (error) {
    console.error('URL连通性测试失败:', error);
    urlConnectivity.value = '❌ 连接失败';
    return false;
  }
};

// 加载测试视频
const loadTestVideo = async () => {
  if (!testVideoElement.value || !testVideoUrl.value) return;

  loading.value = true;
  errorMessage.value = '';
  successMessage.value = '';

  // 清理之前的实例
  if (hlsInstance.value) {
    hlsInstance.value.destroy();
    hlsInstance.value = null;
  }

  checkSupport();

  // 先测试URL连通性
  successMessage.value = '正在测试URL连通性...';
  const isConnectable = await testUrlConnectivity(testVideoUrl.value);

  if (!isConnectable) {
    errorMessage.value = '无法连接到视频服务器，请检查：\n1. 网络连接是否正常\n2. 服务器地址是否正确\n3. 服务器是否在线';
    loading.value = false;
    return;
  }

  if (isHlsFormat.value && hlsSupported.value) {
    // 使用HLS.js
    const hls = new Hls({
      maxBufferLength: 30,
      maxMaxBufferLength: 60,
      lowLatencyMode: false, // 关闭低延迟模式，提高兼容性
      enableWorker: true,
      xhrSetup: function(xhr, url) {
        console.log('正在请求:', url);
        xhr.withCredentials = false;
        // 设置超时时间
        xhr.timeout = 10000;
      },
      manifestLoadingTimeOut: 10000,
      manifestLoadingMaxRetry: 3,
      levelLoadingTimeOut: 10000,
      levelLoadingMaxRetry: 3,
      fragLoadingTimeOut: 20000,
      fragLoadingMaxRetry: 3
    });

    hlsInstance.value = hls;

    // 添加更详细的事件监听
    hls.on(Hls.Events.MANIFEST_LOADING, () => {
      console.log('开始加载HLS清单文件...');
      successMessage.value = '正在加载HLS清单文件...';
    });

    hls.on(Hls.Events.MANIFEST_PARSED, (_, data) => {
      console.log('HLS清单解析完成:', data);
      successMessage.value = `HLS清单解析成功，找到 ${data.levels.length} 个质量级别`;
      testVideoElement.value?.play().catch(e => {
        console.error('播放失败:', e);
        errorMessage.value = `播放失败: ${e.message}`;
        loading.value = false;
      });
    });

    hls.on(Hls.Events.LEVEL_LOADED, (_, data) => {
      console.log('质量级别加载完成:', data);
      successMessage.value = '视频片段信息加载完成';
    });

    hls.on(Hls.Events.FRAG_LOADED, (_, data) => {
      console.log('视频片段加载完成:', data.frag.url);
    });

    hls.on(Hls.Events.ERROR, (_, data) => {
      console.error('HLS错误详情:', data);
      loading.value = false;

      let errorMsg = `HLS错误: ${data.type} - ${data.details}`;

      // 根据具体错误类型提供解决建议
      switch (data.details) {
        case 'manifestLoadError':
          errorMsg += '\n\n可能的解决方案：\n1. 检查URL是否正确\n2. 检查服务器是否支持CORS\n3. 确认服务器是否在线\n4. 尝试在浏览器中直接访问URL';
          break;
        case 'manifestLoadTimeOut':
          errorMsg += '\n\n解决方案：\n1. 检查网络连接\n2. 服务器响应可能过慢\n3. 尝试增加超时时间';
          break;
        case 'manifestParsingError':
          errorMsg += '\n\n解决方案：\n1. 检查m3u8文件格式是否正确\n2. 确认服务器返回的是有效的HLS清单';
          break;
        case 'networkError':
          errorMsg += '\n\n解决方案：\n1. 检查网络连接\n2. 确认服务器地址正确\n3. 检查防火墙设置';
          break;
        default:
          errorMsg += '\n\n建议：\n1. 检查控制台详细错误信息\n2. 确认视频格式和编码\n3. 尝试其他播放方式';
      }

      errorMessage.value = errorMsg;

      // 如果HLS失败，尝试原生播放
      if (data.fatal) {
        console.log('HLS播放失败，尝试原生播放...');
        setTimeout(() => {
          tryNativePlayback();
        }, 1000);
      }
    });

    hls.loadSource(testVideoUrl.value);
    hls.attachMedia(testVideoElement.value);

  } else if (nativeHlsSupported.value) {
    // 使用原生HLS
    tryNativePlayback();
  } else {
    // 尝试普通播放
    tryNativePlayback();
  }
};

// 尝试原生播放
const tryNativePlayback = () => {
  if (!testVideoElement.value) return;

  successMessage.value = '尝试使用浏览器原生播放...';
  testVideoElement.value.src = testVideoUrl.value;

  testVideoElement.value.load();

  testVideoElement.value.addEventListener('loadeddata', () => {
    successMessage.value = '原生播放加载成功';
  }, { once: true });

  testVideoElement.value.addEventListener('error', (e) => {
    console.error('原生播放失败:', e);
    errorMessage.value = '原生播放也失败了，可能的原因：\n1. 视频格式不支持\n2. 编码格式不兼容\n3. 服务器配置问题';
  }, { once: true });
};

// 视频事件处理
const onLoadStart = () => {
  console.log('开始加载视频');
  loading.value = true;
};

const onLoadedMetadata = () => {
  console.log('视频元数据加载完成');
  loading.value = false;
  successMessage.value = '视频元数据加载成功';
};

const onCanPlay = () => {
  console.log('视频可以播放');
  loading.value = false;
  successMessage.value = '视频准备就绪，可以播放';
};

const onError = (event: any) => {
  console.error('视频加载错误:', event);
  loading.value = false;
  errorMessage.value = '视频加载失败，请检查URL是否正确';
};

// 显示模态框
const showModal = () => {
  open.value = true;
  setTimeout(() => {
    checkSupport();
  }, 100);
};

// 关闭模态框
const handleCancel = () => {
  open.value = false;
  if (hlsInstance.value) {
    hlsInstance.value.destroy();
    hlsInstance.value = null;
  }
  if (testVideoElement.value) {
    testVideoElement.value.pause();
    testVideoElement.value.src = '';
  }
};

// 组件销毁时清理
onBeforeUnmount(() => {
  if (hlsInstance.value) {
    hlsInstance.value.destroy();
  }
});

defineExpose({
  showModal
});
</script>

<style scoped>
.tech-modal .ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
}

.video-container {
  position: relative;
}
</style>
