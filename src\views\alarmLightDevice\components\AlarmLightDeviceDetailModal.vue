<template>
  <j-modal
    :title="title"
    :width="width"
    v-model:open="open"
    :footer="null"
    @cancel="handleCancel"
    class="tech-modal"
  >
    <div class="tech-container">
    

      <!-- 设备状态头部 -->
      <div class="device-status-header">
        <div class="device-title">
          <div class="device-name">{{ info.deviceName || '未知设备' }}</div>
          <div class="device-model">{{ info.model || '未知型号' }}</div>
        </div>
        <div class="device-status">
          <div class="status-indicator" :class="getStatusClass(info.status)">
            <div class="status-pulse" v-if="info.status === '1'"></div>
          </div>
          <span class="status-text">{{ getStatusText(info.status) }}</span>
        </div>
      </div>

      <!-- 基本信息 -->
      <div class="tech-card info-card">
        <div class="card-header">
          <Icon icon="ant-design:info-circle-outlined" />
          <span>基本信息</span>
        </div>

        <!-- 设备图片和基本信息 -->
        <div class="device-info-section">
          <!-- 设备图片作为图标 -->
          <div class="device-image-icon" v-if="info.image">
            <img :src="getFileAccessHttpUrl(info.image)" :alt="info.deviceName" />
            <div class="image-overlay-badge">
              <Icon icon="ant-design:camera-outlined" />
            </div>
          </div>

          <!-- 基本信息网格 -->
          <div class="info-grid">
            <div class="info-item">
              <div class="info-icon">
                <Icon icon="ant-design:tag-outlined" />
              </div>
              <div class="info-content">
                <div class="info-label">设备名称</div>
                <div class="info-value">{{ info.deviceName || '-' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">
                <Icon icon="ant-design:barcode-outlined" />
              </div>
              <div class="info-content">
                <div class="info-label">SN码</div>
                <div class="info-value">{{ info.snCode || '-' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">
                <Icon icon="ant-design:setting-outlined" />
              </div>
              <div class="info-content">
                <div class="info-label">设备型号</div>
                <div class="info-value">{{ info.model || '-' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon">
                <Icon icon="ant-design:sound-outlined" />
              </div>
              <div class="info-content">
                <div class="info-label">音量</div>
                <div class="info-value">{{ info.volume || 0 }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 网络配置 -->
      <div class="tech-card network-card">
        <div class="card-header">
          <Icon icon="ant-design:wifi-outlined" />
          <span>网络配置</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-icon">
              <Icon icon="ant-design:global-outlined" />
            </div>
            <div class="info-content">
              <div class="info-label">IP地址</div>
              <div class="info-value">{{ info.ipAddress || '-' }}</div>
            </div>
          </div>
          <div class="info-item">
            <div class="info-icon">
              <Icon icon="ant-design:gateway-outlined" />
            </div>
            <div class="info-content">
              <div class="info-label">网关</div>
              <div class="info-value">{{ info.gatewary || '-' }}</div>
            </div>
          </div>
          <div class="info-item">
            <div class="info-icon">
              <Icon icon="ant-design:cluster-outlined" />
            </div>
            <div class="info-content">
              <div class="info-label">子网掩码</div>
              <div class="info-value">{{ info.subnetMask || '-' }}</div>
            </div>
          </div>
          <div class="info-item">
            <div class="info-icon">
              <Icon icon="ant-design:cloud-server-outlined" />
            </div>
            <div class="info-content">
              <div class="info-label">DNS</div>
              <div class="info-value">{{ info.dns || '-' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- MQTT配置 -->
      <div class="tech-card mqtt-card" v-if="info.clientId || info.username || info.sendTopic || info.receiveTopic">
        <div class="card-header">
          <Icon icon="ant-design:api-outlined" />
          <span>MQTT配置</span>
        </div>
        <div class="info-grid">
          <div class="info-item" v-if="info.clientId">
            <div class="info-icon">
              <Icon icon="ant-design:user-outlined" />
            </div>
            <div class="info-content">
              <div class="info-label">客户端ID</div>
              <div class="info-value">{{ info.clientId }}</div>
            </div>
          </div>
          <div class="info-item" v-if="info.username">
            <div class="info-icon">
              <Icon icon="ant-design:user-outlined" />
            </div>
            <div class="info-content">
              <div class="info-label">用户名</div>
              <div class="info-value">{{ info.username }}</div>
            </div>
          </div>
          <div class="info-item" v-if="info.sendTopic">
            <div class="info-icon">
              <Icon icon="ant-design:upload-outlined" />
            </div>
            <div class="info-content">
              <div class="info-label">发送Topic</div>
              <div class="info-value">{{ info.sendTopic }}</div>
            </div>
          </div>
          <div class="info-item" v-if="info.receiveTopic">
            <div class="info-icon">
              <Icon icon="ant-design:download-outlined" />
            </div>
            <div class="info-content">
              <div class="info-label">接收Topic</div>
              <div class="info-value">{{ info.receiveTopic }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统信息 -->
      <div class="tech-card system-card">
        <div class="card-header">
          <Icon icon="ant-design:clock-circle-outlined" />
          <span>系统信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-icon">
              <Icon icon="ant-design:user-outlined" />
            </div>
            <div class="info-content">
              <div class="info-label">创建人</div>
              <div class="info-value">{{ info.createUserName || '-' }}</div>
            </div>
          </div>
          <div class="info-item">
            <div class="info-icon">
              <Icon icon="ant-design:calendar-outlined" />
            </div>
            <div class="info-content">
              <div class="info-label">创建时间</div>
              <div class="info-value">{{ formatTime(info.createTime) || '-' }}</div>
            </div>
          </div>
          <div class="info-item" v-if="info.updateTime">
            <div class="info-icon">
              <Icon icon="ant-design:edit-outlined" />
            </div>
            <div class="info-content">
              <div class="info-label">更新时间</div>
              <div class="info-value">{{ formatTime(info.updateTime) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </j-modal>
</template>

<script setup lang="ts">
import { ref, defineExpose } from "vue"
import JModal from "@/components/Modal/src/JModal/JModal.vue";
import { detailUrl } from "@/views/alarmLightDevice/AlarmLightDevice.api";
import { Icon } from '/@/components/Icon';
import { getFileAccessHttpUrl } from "/@/utils/common/compUtils";

const width = ref<number>(1200);
const title = ref<string>('设备详情');
const open = ref<boolean>(false);
const info = ref<any>({});
const emit = defineEmits(['register', 'success']);

// 获取设备状态样式类
function getStatusClass(status: string) {
  switch (status) {
    case '1': // 在线
      return 'status-online';
    case '0': // 离线
      return 'status-offline';
    case '2': // 故障
      return 'status-error';
    default:
      return 'status-unknown';
  }
}

// 获取设备状态文本
function getStatusText(status: string) {
  switch (status) {
    case '1':
      return '在线';
    case '0':
      return '离线';
    case '2':
      return '故障';
    default:
      return '未知';
  }
}



// 格式化时间
function formatTime(time: string) {
  if (!time) return '-';
  return new Date(time).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}




function showModal(record: any) {
  open.value = true;
  // 如果传入的是完整记录，直接使用；否则通过ID获取详情
  detail(record.id);
}

function detail(id: string | number) {
  detailUrl({ id: id })
    .then((res: any) => {
      if (res.success) {
        info.value = res.result;
      } else {
        console.log(res.message);
      }
    })
    .finally(() => {
      // 可以在这里添加加载状态的处理
    });
}



/**
 * 取消按钮回调时间
 */
function handleCancel() {
  open.value = false;
}

defineExpose({
  showModal,
  detail
});
</script>

<style scoped lang="less">
/* 科技感模态框 */
.tech-modal {
  :deep(.ant-modal-content) {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    border: 1px solid rgba(59, 130, 246, 0.15);
    box-shadow:
      0 25px 50px rgba(59, 130, 246, 0.1),
      0 10px 30px rgba(6, 182, 212, 0.05);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 16px;
    overflow: hidden;
  }

  :deep(.ant-modal-header) {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 16px 16px 0 0;
  }

  :deep(.ant-modal-title) {
    color: #1e293b;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
  }

  :deep(.ant-modal-body) {
    padding: 0;
  }
}

/* 科技感容器 */
.tech-container {
  position: relative;
  padding: 24px;
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.03) 0%, transparent 50%);
}

/* 科技网格背景 */
.tech-grid-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  z-index: 0;
}

/* 设备状态头部 */
.device-status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  position: relative;
  z-index: 1;
}

.device-title {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.device-name {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  text-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
}

.device-model {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.device-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  position: relative;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  &.status-online {
    background: #10b981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  }

  &.status-offline {
    background: #6b7280;
    box-shadow: 0 0 0 2px rgba(107, 114, 128, 0.2);
  }

  &.status-error {
    background: #ef4444;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
  }

  &.status-unknown {
    background: #f59e0b;
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
  }
}

.status-pulse {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: inherit;
  animation: pulse-glow 2s infinite;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

/* 科技卡片 */
.tech-card {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow:
    0 4px 20px rgba(59, 130, 246, 0.08),
    0 2px 10px rgba(6, 182, 212, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  z-index: 1;
}

.tech-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 30px rgba(59, 130, 246, 0.12),
    0 4px 20px rgba(6, 182, 212, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border-color: rgba(59, 130, 246, 0.2);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);

  .anticon {
    color: #3b82f6;
    font-size: 18px;
  }
}

/* 设备信息区域 */
.device-info-section {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

/* 设备图片图标 */
.device-image-icon {
  position: relative;
  flex-shrink: 0;
  width: 120px;
  height: 120px;
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.6) 100%);
  border: 2px solid rgba(59, 130, 246, 0.1);
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.08),
    0 4px 15px rgba(6, 182, 212, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow:
      0 12px 35px rgba(59, 130, 246, 0.12),
      0 6px 20px rgba(6, 182, 212, 0.08);
    border-color: rgba(59, 130, 246, 0.2);
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  &:hover img {
    transform: scale(1.05);
  }
}

.image-overlay-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .anticon {
    font-size: 12px;
    color: #3b82f6;
  }
}



/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
  flex: 1;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.6) 0%, rgba(248, 250, 252, 0.4) 100%);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.08);
    border-color: rgba(59, 130, 246, 0.1);
  }
}

.info-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.1);

  .anticon {
    font-size: 16px;
  }
}

.info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;

  .info-label {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
  }

  .info-value {
    font-size: 14px;
    color: #1e293b;
    font-weight: 600;
    word-break: break-all;
  }
}

/* 动画效果 */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tech-container {
    padding: 16px;
  }

  .device-status-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .device-info-section {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .device-image-icon {
    width: 100px;
    height: 100px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .tech-card {
    padding: 16px;
  }
}

/* 特殊卡片样式 */
.network-card {
  border-left: 3px solid #06b6d4;
}

.mqtt-card {
  border-left: 3px solid #8b5cf6;
}

.system-card {
  border-left: 3px solid #10b981;
}

.info-card {
  border-left: 3px solid #3b82f6;
}
</style>
