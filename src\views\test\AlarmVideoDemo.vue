<template>
  <div class="alarm-video-demo">
    <div class="demo-header">
      <h1>报警视频监控优化演示</h1>
      <p>展示优化后的2x2视频网格布局</p>
    </div>

    <div class="demo-controls">
      <a-space size="large">
        <a-button type="primary" size="large" @click="showFullVideoModal">
          显示4个视频 (完整)
        </a-button>
        <a-button type="default" size="large" @click="showPartialVideoModal">
          显示2个视频 (部分)
        </a-button>
        <a-button type="default" size="large" @click="showEmptyVideoModal">
          显示空视频框
        </a-button>
      </a-space>
    </div>

    <div class="demo-info">
      <div class="info-card">
        <h3>🎯 优化亮点</h3>
        <ul>
          <li><strong>2x2网格布局</strong> - 同时显示4个视频框</li>
          <li><strong>紧凑设计</strong> - 视频框更小，信息密度更高</li>
          <li><strong>自适应填充</strong> - 不足4个视频时自动显示空位置</li>
          <li><strong>保留控制</strong> - 每个视频框都有独立的播放控制</li>
          <li><strong>科技美学</strong> - 保持原有的科技风格设计</li>
        </ul>
      </div>

      <div class="info-card">
        <h3>📐 布局规格</h3>
        <ul>
          <li><strong>模态框宽度</strong> - 1400px</li>
          <li><strong>视频网格高度</strong> - 500px</li>
          <li><strong>网格间距</strong> - 12px</li>
          <li><strong>视频框最大高度</strong> - 200px</li>
          <li><strong>响应式设计</strong> - 适配不同屏幕尺寸</li>
        </ul>
      </div>
    </div>

    <!-- 报警视频监控模态框 -->
    <AlarmVideoMonitorModal
      v-model:open="alarmModalVisible"
      :alarm-data="currentAlarmData"
      @confirm="handleAlarmConfirm"
      @close="handleAlarmClose"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import AlarmVideoMonitorModal from '/@/components/AlarmVideoMonitorModal/index.vue';

// 响应式数据
const alarmModalVisible = ref(false);
const currentAlarmData = ref({});

// 完整的4个视频数据
const fullVideoData = {
  id: 'alarm_full_001',
  alarmId: 'alarm_full_001',
  msgTxt: '监控区域检测到异常活动，正在调取4路监控视频',
  msgDesc: '系统自动检测到可疑行为',
  timestamp: new Date(),
  location: '监控区域A-01',
  deviceId: 'CAM-001',
  videoList: [
    {
      id: 'video-1',
      name: '大门监控',
      videoUrl: 'http://example.com/video1.mp4',
      streamId: 'stream-001',
      websocketUrl: 'ws://example.com/stream1',
      cameraIndexCode: 'CAM-001',
      streamType: 'preview',
      rtspUrl: 'rtsp://example.com/stream1'
    },
    {
      id: 'video-2',
      name: '走廊监控',
      videoUrl: 'http://example.com/video2.mp4',
      streamId: 'stream-002',
      websocketUrl: 'ws://example.com/stream2',
      cameraIndexCode: 'CAM-002',
      streamType: 'preview',
      rtspUrl: 'rtsp://example.com/stream2'
    },
    {
      id: 'video-3',
      name: '活动区监控',
      videoUrl: 'http://example.com/video3.mp4',
      streamId: 'stream-003',
      websocketUrl: 'ws://example.com/stream3',
      cameraIndexCode: 'CAM-003',
      streamType: 'playback',
      rtspUrl: 'rtsp://example.com/stream3'
    },
    {
      id: 'video-4',
      name: '后门监控',
      videoUrl: 'http://example.com/video4.mp4',
      streamId: 'stream-004',
      websocketUrl: 'ws://example.com/stream4',
      cameraIndexCode: 'CAM-004',
      streamType: 'preview',
      rtspUrl: 'rtsp://example.com/stream4'
    }
  ]
};

// 部分视频数据（2个）
const partialVideoData = {
  ...fullVideoData,
  msgTxt: '监控区域检测到异常活动，调取2路监控视频',
  videoList: fullVideoData.videoList.slice(0, 2)
};

// 空视频数据
const emptyVideoData = {
  ...fullVideoData,
  msgTxt: '暂无可用的监控视频',
  videoList: []
};

// 显示完整视频模态框
const showFullVideoModal = () => {
  currentAlarmData.value = fullVideoData;
  alarmModalVisible.value = true;
};

// 显示部分视频模态框
const showPartialVideoModal = () => {
  currentAlarmData.value = partialVideoData;
  alarmModalVisible.value = true;
};

// 显示空视频模态框
const showEmptyVideoModal = () => {
  currentAlarmData.value = emptyVideoData;
  alarmModalVisible.value = true;
};

// 处理报警确认
const handleAlarmConfirm = (alarmData: any) => {
  console.log('报警已确认处理:', alarmData);
  alarmModalVisible.value = false;
};

// 处理报警关闭
const handleAlarmClose = () => {
  console.log('报警模态框已关闭');
  alarmModalVisible.value = false;
};
</script>

<style lang="less" scoped>
.alarm-video-demo {
  padding: 32px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  min-height: 100vh;
  color: #ffffff;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
  
  h1 {
    color: #00d4ff;
    font-size: 32px;
    margin-bottom: 12px;
    font-weight: 600;
  }
  
  p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    margin: 0;
  }
}

.demo-controls {
  text-align: center;
  margin-bottom: 40px;
}

.demo-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.info-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 24px;
  border: 1px solid rgba(0, 212, 255, 0.3);
  
  h3 {
    color: #00d4ff;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 600;
  }
  
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    
    li {
      padding: 8px 0;
      padding-left: 20px;
      position: relative;
      color: rgba(255, 255, 255, 0.9);
      font-size: 14px;
      line-height: 1.5;
      
      &:before {
        content: '▶';
        position: absolute;
        left: 0;
        color: #00d4ff;
        font-size: 10px;
      }
      
      strong {
        color: #ffffff;
      }
    }
  }
}

@media (max-width: 768px) {
  .demo-info {
    grid-template-columns: 1fr;
  }
}
</style>
