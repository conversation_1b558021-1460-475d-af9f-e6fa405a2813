<template>
  <div class="plan-video-monitor-modal">
    <!-- 工具栏 -->
    <div class="monitor-toolbar">
      <div class="toolbar-left">
        <h4>视频监控 ({{ videoList.length }})</h4>
        <a-tag color="blue">{{ planInfo?.name || '巡更计划' }}</a-tag>
      </div>
      <div class="toolbar-right">
        <a-button @click="startAllVideos" :loading="startingAll" size="small">
          <PlayCircleOutlined /> 全部播放
        </a-button>
        <a-button @click="stopAllVideos" size="small">
          <PauseCircleOutlined /> 全部停止
        </a-button>
        <a-button @click="refreshAllVideos" size="small">
          <ReloadOutlined /> 全部刷新
        </a-button>
      </div>
    </div>

    <!-- 视频网格 -->
    <div class="video-grid">
      <div
          v-for="(video, index) in videoList"
          :key="video.id || index"
          class="video-grid-item"
      >
        <VideoMonitorPlayerModal
            :ref="el => setVideoPlayerRef(el, index)"
            :plan-id="planInfo?.id || ''"
            :video-info="video"
            :auto-start="false"
            :show-controls="true"
            @error="onVideoError(index, $event)"
        />
      </div>
    </div>

  </div>
</template>

<script lang="ts" setup>
import { ref, onUnmounted, defineExpose } from 'vue';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue';
import VideoMonitorPlayerModal from './VideoMonitorPlayerModal.vue';

interface VideoInfo {
  id: string;
  name: string;
  videoUrl: string;
  streamId: string;
  websocketUrl: string;
  rtspUrl?: string;
  rtspVideo?: string;
  streamType?: string;
}

interface PlanInfo {
  id: string;
  name: string;
}

interface Props {
  planInfo: PlanInfo;
  videoList: VideoInfo[];
}

const props = defineProps<Props>();

// 响应式数据
const startingAll = ref(false);

// 视频播放器引用
const videoPlayerRefs = ref<any[]>([]);

/**
 * 清理所有视频播放器资源
 */
function cleanupAllVideos() {
  console.log('PlanVideoMonitorModal 清理所有视频播放器资源');

  videoPlayerRefs.value.forEach((player, index) => {
    if (player) {
      console.log(`清理视频播放器 ${index}`);

      // 停止视频
      if (typeof player.stopVideo === 'function') {
        try {
          player.stopVideo();
        } catch (error) {
          console.error(`停止视频播放器 ${index} 时出错:`, error);
        }
      }

      // 清理资源
      if (typeof player.cleanup === 'function') {
        try {
          player.cleanup();
        } catch (error) {
          console.error(`清理视频播放器 ${index} 资源时出错:`, error);
        }
      }
    }
  });

  // 清空引用数组
  videoPlayerRefs.value = [];

  console.log('PlanVideoMonitorModal 所有视频播放器资源清理完成');
}

// 组件卸载
onUnmounted(() => {
  cleanupAllVideos();
});

/**
 * 设置视频播放器引用
 */
function setVideoPlayerRef(el: any, index: number) {
  if (el) {
    videoPlayerRefs.value[index] = el;
  }
}



/**
 * 开始所有视频
 */
async function startAllVideos() {
  if (props.videoList.length === 0) {
    console.warn('没有可播放的视频');
    return;
  }

  startingAll.value = true;

  try {
    const promises = videoPlayerRefs.value.map(player => {
      if (player && player.startVideo) {
        return player.startVideo();
      }
      return Promise.resolve();
    });

    await Promise.allSettled(promises);
    console.log('已启动所有视频');
  } catch (error) {
    console.error('启动视频失败');
  } finally {
    startingAll.value = false;
  }
}

/**
 * 停止所有视频
 */
function stopAllVideos() {
  cleanupAllVideos();
  console.log('已停止所有视频');
}

/**
 * 刷新所有视频
 */
function refreshAllVideos() {
  videoPlayerRefs.value.forEach(player => {
    if (player && player.refreshVideo) {
      player.refreshVideo();
    }
  });
  console.log('已刷新所有视频');
}





/**
 * 视频错误处理
 */
function onVideoError(index: number, error: any) {
  console.error(`视频 ${index + 1} 错误:`, error);
  console.error(`视频 ${props.videoList[index]?.name || index + 1} 播放错误`);
}

// 暴露方法给父组件
defineExpose({
  startAllVideos,
  stopAllVideos,
  refreshAllVideos,
  cleanupAllVideos
});
</script>

<style scoped>
.plan-video-monitor-modal {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.monitor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-left h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.video-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  overflow: auto;
  min-height: 300px;
}

.video-grid-item {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  min-height: 300px;
  height: auto;
}

.video-grid-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.video-grid-item.active {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}



.video-detail {
  padding: 16px 0;
}

.video-actions {
  margin-top: 16px;
}
</style>
