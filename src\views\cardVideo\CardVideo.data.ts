import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  // {
  //   title: '卡片id',
  //   align: "center",
  //   dataIndex: 'cardId'
  // },
  {
    title: '监控名称',
    align: "center",
    dataIndex: 'name'
  },
  {
    title: '海康监控唯一标识',
    align: "center",
    dataIndex: 'cameraIndexCode'
  },
  {
    title: '是否主监控',
    align: "center",
    dataIndex: 'isMain',
    customRender: ({ text }) => {
      const isMain = text === "1" || text === 1;
      return render.renderTag(isMain ? '是' : '否', isMain ? 'success' : 'default');
    }
  },
];

// 高级查询数据
export const superQuerySchema = {
  cardId: {title: '卡片id',order: 0,view: 'text', type: 'string',},
  name: {title: '监控名称',order: 1,view: 'text', type: 'string',},
  cameraIndexCode: {title: '海康监控唯一标识',order: 2,view: 'text', type: 'string',},
  isMain: {title: '是否主监控',order: 3,view: 'list', type: 'string', dictCode: 'yes_no'},
};
