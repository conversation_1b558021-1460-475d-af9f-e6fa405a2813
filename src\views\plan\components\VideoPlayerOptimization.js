/**
 * 视频播放器优化配置
 * 用于解决HLS视频播放问题
 */

/**
 * 优化的HLS.js配置
 */
export const optimizedHlsConfig = {
  debug: false,
  enableWorker: false,
  lowLatencyMode: false,

  // 缓冲配置 - 针对播放问题优化
  maxBufferLength: 30, // 减少缓冲长度，避免内存问题
  maxMaxBufferLength: 60,
  maxBufferSize: 30 * 1000 * 1000, // 30MB
  maxBufferHole: 0.5,

  // 缓冲管理
  highBufferWatchdogPeriod: 2,
  nudgeOffset: 0.1,
  nudgeMaxRetry: 5,
  maxFragLookUpTolerance: 0.25,

  // 播放列表加载配置
  manifestLoadingTimeOut: 10000, // 10秒超时
  manifestLoadingMaxRetry: 3,
  manifestLoadingRetryDelay: 1000,

  // 级别加载配置
  levelLoadingTimeOut: 10000,
  levelLoadingMaxRetry: 4,
  levelLoadingRetryDelay: 1000,

  // 片段加载配置 - 关键优化
  fragLoadingTimeOut: 20000, // 20秒超时
  fragLoadingMaxRetry: 6, // 增加重试次数
  fragLoadingRetryDelay: 500, // 减少重试延迟

  // 自动恢复配置
  autoStartLoad: true,
  startPosition: -1,
  capLevelToPlayerSize: false,

  // 播放配置
  liveDurationInfinity: false,
  liveBackBufferLength: 0,
  maxLiveSyncPlaybackRate: 1,

  // 错误恢复
  enableSoftwareAES: true,
  forceKeyFrameOnDiscontinuity: true,

  // 优化配置
  progressive: false, // 禁用渐进式加载，避免播放问题
  enableDateRangeMetadataCues: false,
  enableEmsgMetadataCues: false,
  enableID3MetadataCues: false,

  // 自定义加载器配置
  xhrSetup: function(xhr, url) {
    xhr.timeout = 15000; // 15秒超时
    xhr.withCredentials = false;
  }
};

/**
 * 视频元素配置
 */
export const videoElementConfig = {
  preload: 'metadata',
  controls: true,
  muted: false,
  autoplay: false,
  crossOrigin: 'anonymous',
  playsInline: true
};

/**
 * 播放器事件处理优化
 */
export const setupOptimizedEventHandlers = (hls, videoElement) => {
  // 清单加载成功
  hls.on(Hls.Events.MANIFEST_LOADED, (event, data) => {
    console.log('播放列表加载成功:', {
      url: data.url,
      levels: data.levels?.length || 0,
      duration: data.totalduration
    });
  });

  // 清单加载错误
  hls.on(Hls.Events.MANIFEST_LOADING_ERROR, (event, data) => {
    console.error('播放列表加载错误:', data);
    
    // 尝试重新加载
    setTimeout(() => {
      if (hls && !hls.destroyed) {
        console.log('尝试重新加载播放列表');
        hls.startLoad();
      }
    }, 2000);
  });

  // 片段加载成功
  hls.on(Hls.Events.FRAG_LOADED, (event, data) => {
    console.log('片段加载成功:', {
      frag: data.frag.sn,
      url: data.frag.url,
      duration: data.frag.duration,
      size: data.payload?.byteLength || 0
    });
  });

  // 片段加载错误
  hls.on(Hls.Events.FRAG_LOAD_ERROR, (event, data) => {
    console.error('片段加载错误:', {
      frag: data.frag?.sn || 'unknown',
      url: data.frag?.url || 'unknown',
      response: data.response,
      error: data.error
    });

    // 对于404错误，等待后重试
    if (data.response && data.response.code === 404) {
      console.log('片段404，可能正在生成中，等待重试');
    }
  });

  // 级别切换
  hls.on(Hls.Events.LEVEL_SWITCHED, (event, data) => {
    console.log('切换到级别:', data.level);
  });

  // 错误处理
  hls.on(Hls.Events.ERROR, (event, data) => {
    console.error('HLS错误:', data);

    if (data.fatal) {
      switch (data.type) {
        case Hls.ErrorTypes.NETWORK_ERROR:
          console.log('网络错误，尝试恢复');
          hls.startLoad();
          break;
        case Hls.ErrorTypes.MEDIA_ERROR:
          console.log('媒体错误，尝试恢复');
          hls.recoverMediaError();
          break;
        default:
          console.log('无法恢复的错误，销毁播放器');
          hls.destroy();
          break;
      }
    }
  });

  // 视频元素事件
  videoElement.addEventListener('loadstart', () => {
    console.log('开始加载视频');
  });

  videoElement.addEventListener('loadedmetadata', () => {
    console.log('视频元数据加载完成:', {
      duration: videoElement.duration,
      videoWidth: videoElement.videoWidth,
      videoHeight: videoElement.videoHeight
    });
  });

  videoElement.addEventListener('canplay', () => {
    console.log('视频可以开始播放');
  });

  videoElement.addEventListener('playing', () => {
    console.log('视频开始播放');
  });

  videoElement.addEventListener('waiting', () => {
    console.log('视频缓冲中');
  });

  videoElement.addEventListener('error', (e) => {
    console.error('视频元素错误:', e);
  });

  videoElement.addEventListener('stalled', () => {
    console.warn('视频加载停滞');
  });
};

/**
 * 播放器初始化优化
 */
export const initializeOptimizedPlayer = (videoElement, hlsUrl) => {
  return new Promise((resolve, reject) => {
    try {
      console.log('初始化优化播放器:', hlsUrl);

      if (!Hls.isSupported()) {
        // 原生HLS支持
        if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
          videoElement.src = hlsUrl;
          resolve({ type: 'native', player: videoElement });
        } else {
          reject(new Error('HLS不被支持'));
        }
        return;
      }

      // 使用HLS.js
      const hls = new Hls(optimizedHlsConfig);
      
      // 设置事件处理
      setupOptimizedEventHandlers(hls, videoElement);

      // 加载源
      hls.loadSource(hlsUrl);
      hls.attachMedia(videoElement);

      // 等待清单加载
      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('播放列表解析完成，可以开始播放');
        resolve({ type: 'hls.js', player: hls });
      });

      // 错误处理
      hls.on(Hls.Events.ERROR, (event, data) => {
        if (data.fatal) {
          reject(new Error(`HLS致命错误: ${data.type} - ${data.details}`));
        }
      });

    } catch (error) {
      console.error('播放器初始化失败:', error);
      reject(error);
    }
  });
};

/**
 * 播放器状态监控
 */
export const monitorPlayerStatus = (hls, videoElement) => {
  const monitor = {
    interval: null,
    
    start() {
      this.interval = setInterval(() => {
        if (videoElement && hls && !hls.destroyed) {
          const currentTime = videoElement.currentTime;
          const duration = videoElement.duration;
          const buffered = videoElement.buffered;
          
          let bufferedEnd = 0;
          if (buffered.length > 0) {
            bufferedEnd = buffered.end(buffered.length - 1);
          }
          
          const bufferedAhead = bufferedEnd - currentTime;
          
          console.debug('播放状态:', {
            currentTime: currentTime.toFixed(2),
            duration: duration ? duration.toFixed(2) : 'unknown',
            bufferedAhead: bufferedAhead.toFixed(2),
            readyState: videoElement.readyState,
            networkState: videoElement.networkState
          });
          
          // 检查是否需要重新加载
          if (bufferedAhead < 5 && videoElement.readyState < 3) {
            console.log('缓冲不足，尝试重新加载');
            hls.startLoad();
          }
        }
      }, 5000); // 每5秒检查一次
    },
    
    stop() {
      if (this.interval) {
        clearInterval(this.interval);
        this.interval = null;
      }
    }
  };
  
  return monitor;
};

/**
 * 播放器清理
 */
export const cleanupPlayer = (hls, monitor) => {
  try {
    if (monitor) {
      monitor.stop();
    }
    
    if (hls && !hls.destroyed) {
      hls.destroy();
    }
    
    console.log('播放器清理完成');
  } catch (error) {
    console.error('播放器清理失败:', error);
  }
};
