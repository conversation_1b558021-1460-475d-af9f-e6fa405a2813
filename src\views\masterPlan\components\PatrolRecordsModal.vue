<template>
  <a-modal
    :title="title"
    :width="1600"
    v-model:open="open"
    :footer="null"
    @cancel="handleCancel"
    class="tech-modal patrol-records-modal"
  >
    <!-- 嵌入PlanList组件 -->
    <div class="tech-card plan-list-container">
        <!-- 直接嵌入PlanList组件 -->
        <PlanList
          v-if="open && masterPlanInfo.id"
          ref="planListRef"
          :key="masterPlanInfo.id"
          :embedded="true"
          :masterPlanId="masterPlanInfo.id"
          :hideRouteList="true"
          :hideToolbar="true"
        />
      </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, defineExpose, nextTick } from 'vue';
import PlanList from '../../plan/PlanList.vue';

const title = ref<string>('查看巡更记录');
const open = ref<boolean>(false);
const masterPlanInfo = ref<any>({});
const planListRef = ref();

// 显示模态框
function showModal(record: any) {
  console.log('打开巡更记录弹窗，总计划ID:', record.id, '总计划名称:', record.masterPlanName);

  // 先关闭弹窗并清空数据
  open.value = false;
  masterPlanInfo.value = {};

  // 等待DOM更新，确保组件完全卸载
  nextTick(() => {
    // 设置新数据
    masterPlanInfo.value = { ...record };
    title.value = `查看巡更记录 - ${record.masterPlanName}`;

    // 再次等待DOM更新后打开弹窗
    nextTick(() => {
      open.value = true;
      console.log('弹窗已打开，传递的masterPlanId:', masterPlanInfo.value.id);
    });
  });
}

// 关闭模态框
function handleCancel() {
  open.value = false;

  // 延迟清空数据，确保组件完全卸载
  setTimeout(() => {
    masterPlanInfo.value = {};
    planListRef.value = null;
  }, 100);
}

defineExpose({
  showModal
});
</script>

<style scoped lang="less">
.tech-modal {
  :deep(.ant-modal-content) {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #cbd5e1;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  :deep(.ant-modal-header) {
    background: linear-gradient(90deg, #0ea5e9 0%, #06b6d4 100%);
    border-bottom: 1px solid #0891b2;

    .ant-modal-title {
      color: white;
      font-weight: 600;
    }
  }

  :deep(.ant-modal-close) {
    color: white;

    &:hover {
      color: #f1f5f9;
    }
  }
}

.tech-container {
  position: relative;
  min-height: 400px;
}

.tech-grid-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(6, 182, 212, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(6, 182, 212, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  z-index: 0;
}

.tech-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  // border: 1px solid #e2e8f0;
  // border-radius: 12px;
  // padding: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 1;

  &.main-info-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(240, 249, 255, 0.95) 100%);
    border: 1px solid #0ea5e9;
  }
}

.decoration-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, #06b6d4 50%, transparent 100%);

  &.top-line {
    top: 0;
  }

  &.bottom-line {
    bottom: 0;
  }
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
  animation: pulse 2s infinite;

  &.active {
    background: #06b6d4;
    box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.2);
  }
}

.patrol-record-item {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(6, 182, 212, 0.15);
  }
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;

  &.status-pending {
    background: #f59e0b;
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
  }

  &.status-normal {
    background: #10b981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  }

  &.status-missed {
    background: #ef4444;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
  }

  &.status-unknown {
    background: #6b7280;
    box-shadow: 0 0 0 2px rgba(107, 114, 128, 0.2);
  }
}

.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;

    &:hover {
      background: #94a3b8;
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
