/**
 * FLV播放器全局配置
 * 用于统一管理FLV播放器的配置参数
 */

/**
 * FLV播放器默认配置
 */
export const defaultFlvConfig = {
  type: 'flv',
  isLive: true,
  hasAudio: true,
  hasVideo: true,
  cors: true,
  withCredentials: false
};

/**
 * FLV播放器选项配置
 */
export const defaultFlvOptions = {
  // 基础配置
  enableWorker: false, // 禁用Worker以提高兼容性
  enableStashBuffer: true, // 启用缓存缓冲区
  stashInitialSize: 128, // 初始缓存大小（KB）

  // 自动清理配置 - 优化内存使用
  autoCleanupSourceBuffer: true,
  autoCleanupMaxBackwardDuration: 30, // 保留30秒历史数据
  autoCleanupMinBackwardDuration: 10, // 最少保留10秒

  // 音视频同步
  fixAudioTimestampGap: true,

  // 禁用精确定位以提高性能
  accurateSeek: false,

  // HTTP范围请求配置
  seekType: 'range',
  seekParamStart: 'bstart',
  seekParamEnd: 'bend',
  rangeLoadZeroStart: false,

  // 懒加载配置
  lazyLoad: false, // 禁用懒加载以提高实时性
  lazyLoadMaxDuration: 3 * 60,
  lazyLoadRecoverDuration: 30,
  deferLoadAfterSourceOpen: false,

  // 实时流优化
  liveBufferLatencyChasing: true,
  liveBufferLatencyMaxLatency: 3,
  liveBufferLatencyMinRemain: 0.5,

  // 其他配置
  reuseRedirectedURL: false,
  referrerPolicy: 'no-referrer-when-downgrade'
};

/**
 * 高质量配置（适用于高清视频流）
 */
export const highQualityFlvOptions = {
  ...defaultFlvOptions,
  stashInitialSize: 256, // 增加初始缓存
  autoCleanupMaxBackwardDuration: 60, // 保留更多历史数据
  autoCleanupMinBackwardDuration: 20,
  liveBufferLatencyMaxLatency: 5, // 允许更高延迟以保证质量
  liveBufferLatencyMinRemain: 1.0
};

/**
 * 低延迟配置（适用于实时监控）
 */
export const lowLatencyFlvOptions = {
  ...defaultFlvOptions,
  stashInitialSize: 64, // 减少初始缓存
  autoCleanupMaxBackwardDuration: 15, // 减少历史数据保留
  autoCleanupMinBackwardDuration: 5,
  liveBufferLatencyMaxLatency: 1, // 最低延迟
  liveBufferLatencyMinRemain: 0.2,
  enableStashBuffer: false // 禁用缓存以降低延迟
};

/**
 * 移动端优化配置
 */
export const mobileFlvOptions = {
  ...defaultFlvOptions,
  enableWorker: false, // 移动端禁用Worker
  stashInitialSize: 64, // 减少内存使用
  autoCleanupMaxBackwardDuration: 20,
  autoCleanupMinBackwardDuration: 8,
  liveBufferLatencyMaxLatency: 2,
  liveBufferLatencyMinRemain: 0.3
};

/**
 * 错误类型映射
 */
export const flvErrorMessages = {
  // 网络错误
  'NetworkError': {
    'NetworkTimeout': '网络超时，请检查网络连接',
    'NetworkUnrecoverableEarlyEof': '网络连接中断',
    'NetworkException': '网络异常',
    'NetworkStatusCodeInvalid': '网络状态码无效',
    'NetworkNoSource': '无可用的视频源'
  },
  
  // 媒体错误
  'MediaError': {
    'MediaFormatError': '媒体格式错误',
    'MediaFormatUnsupported': '不支持的媒体格式',
    'MediaCodecUnsupported': '不支持的编解码器'
  },
  
  // 其他错误
  'OtherError': {
    'LoaderUnknownError': '加载器未知错误'
  }
};

/**
 * 重试配置
 */
export const retryConfig = {
  maxRetryCount: 3, // 最大重试次数
  retryDelay: 2000, // 重试延迟（毫秒）
  retryDelayMultiplier: 1.5, // 重试延迟倍数
  maxRetryDelay: 10000 // 最大重试延迟
};

/**
 * 连接超时配置
 */
export const timeoutConfig = {
  connectionTimeout: 10000, // 连接超时（毫秒）
  loadTimeout: 15000, // 加载超时（毫秒）
  playTimeout: 5000 // 播放超时（毫秒）
};

/**
 * 根据设备类型获取优化配置
 */
export const getOptimizedFlvOptions = (deviceType = 'desktop', quality = 'normal') => {
  let baseOptions = defaultFlvOptions;
  
  // 根据设备类型选择基础配置
  switch (deviceType) {
    case 'mobile':
      baseOptions = mobileFlvOptions;
      break;
    case 'tablet':
      baseOptions = mobileFlvOptions;
      break;
    default:
      baseOptions = defaultFlvOptions;
  }
  
  // 根据质量要求调整配置
  switch (quality) {
    case 'high':
      return { ...baseOptions, ...highQualityFlvOptions };
    case 'low-latency':
      return { ...baseOptions, ...lowLatencyFlvOptions };
    default:
      return baseOptions;
  }
};

/**
 * 检测设备类型
 */
export const detectDeviceType = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  
  if (/mobile|android|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent)) {
    return 'mobile';
  } else if (/tablet|ipad/i.test(userAgent)) {
    return 'tablet';
  } else {
    return 'desktop';
  }
};

/**
 * 获取错误消息
 */
export const getFlvErrorMessage = (errorType, errorDetail) => {
  const errorTypeMap = flvErrorMessages[errorType];
  if (errorTypeMap && errorTypeMap[errorDetail]) {
    return errorTypeMap[errorDetail];
  }
  return `未知错误: ${errorType} - ${errorDetail}`;
};

/**
 * 计算重试延迟
 */
export const calculateRetryDelay = (retryCount) => {
  const delay = retryConfig.retryDelay * Math.pow(retryConfig.retryDelayMultiplier, retryCount - 1);
  return Math.min(delay, retryConfig.maxRetryDelay);
};

/**
 * FLV.js CDN地址列表
 */
export const flvjsCdnUrls = [
  'https://cdn.jsdelivr.net/npm/flv.js@latest/dist/flv.min.js',
  'https://unpkg.com/flv.js@latest/dist/flv.min.js',
  'https://cdnjs.cloudflare.com/ajax/libs/flv.js/1.6.2/flv.min.js'
];

/**
 * 动态加载FLV.js
 */
export const loadFlvJs = async (cdnIndex = 0) => {
  if (window.flvjs) {
    return window.flvjs;
  }
  
  if (cdnIndex >= flvjsCdnUrls.length) {
    throw new Error('所有CDN地址都加载失败');
  }
  
  try {
    const script = document.createElement('script');
    script.src = flvjsCdnUrls[cdnIndex];
    
    await new Promise((resolve, reject) => {
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
    
    if (window.flvjs) {
      console.log(`FLV.js 从 CDN ${cdnIndex + 1} 加载成功`);
      return window.flvjs;
    } else {
      throw new Error('FLV.js 加载后未找到全局对象');
    }
  } catch (error) {
    console.warn(`CDN ${cdnIndex + 1} 加载失败，尝试下一个:`, error);
    return loadFlvJs(cdnIndex + 1);
  }
};

/**
 * 检查浏览器兼容性
 */
export const checkBrowserCompatibility = () => {
  const compatibility = {
    flvSupported: false,
    mseSupported: false,
    h264Supported: false,
    recommendations: []
  };
  
  // 检查MSE支持
  if (window.MediaSource || window.WebKitMediaSource) {
    compatibility.mseSupported = true;
  } else {
    compatibility.recommendations.push('浏览器不支持Media Source Extensions');
  }
  
  // 检查H.264支持
  const video = document.createElement('video');
  if (video.canPlayType('video/mp4; codecs="avc1.42E01E"')) {
    compatibility.h264Supported = true;
  } else {
    compatibility.recommendations.push('浏览器不支持H.264编解码器');
  }
  
  // 检查FLV支持（需要先加载flv.js）
  if (window.flvjs && window.flvjs.isSupported()) {
    compatibility.flvSupported = true;
  }
  
  // 生成建议
  if (!compatibility.mseSupported || !compatibility.h264Supported) {
    compatibility.recommendations.push('建议使用Chrome、Firefox或Edge浏览器');
  }
  
  return compatibility;
};

export default {
  defaultFlvConfig,
  defaultFlvOptions,
  highQualityFlvOptions,
  lowLatencyFlvOptions,
  mobileFlvOptions,
  flvErrorMessages,
  retryConfig,
  timeoutConfig,
  getOptimizedFlvOptions,
  detectDeviceType,
  getFlvErrorMessage,
  calculateRetryDelay,
  loadFlvJs,
  checkBrowserCompatibility
};
