<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>巡更记录视频监控功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.12);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            background: #10b981;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
        .code-block {
            background: #f1f5f9;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .api-info {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .success {
            background: #d1fae5;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎥 巡更记录视频监控功能</h1>
            <p>为巡更记录列表添加视频监控列，点击后弹窗查看该巡更点的视频监控</p>
        </div>
        
        <div class="content">
            <h2>✨ 功能特性</h2>
            <ul class="feature-list">
                <li>
                    <span class="icon">✓</span>
                    在巡更记录列表中新增"视频监控"列
                </li>
                <li>
                    <span class="icon">✓</span>
                    点击视频监控按钮弹窗查看该巡更点的视频监控
                </li>
                <li>
                    <span class="icon">✓</span>
                    通过巡更点ID查找相关视频监控设备
                </li>
                <li>
                    <span class="icon">✓</span>
                    支持多个视频监控的网格布局显示
                </li>
                <li>
                    <span class="icon">✓</span>
                    科技感UI设计，与现有系统风格保持一致
                </li>
                <li>
                    <span class="icon">✓</span>
                    完善的错误处理和加载状态提示
                </li>
                <li>
                    <span class="icon">🆕</span>
                    优化监控按钮和图标居中显示
                </li>
                <li>
                    <span class="icon">🆕</span>
                    空闲位置时播放器组件铺满整个区域
                </li>
                <li>
                    <span class="icon">🆕</span>
                    无视频时隐藏布局切换按钮
                </li>
            </ul>

            <h2>🔧 技术实现</h2>
            
            <h3>1. API接口扩展</h3>
            <div class="api-info">
                <strong>新增API:</strong> <code>/cardVideo/cardVideo/list</code><br>
                <strong>参数:</strong> <code>{ cardId: string }</code><br>
                <strong>功能:</strong> 根据巡更点ID获取关联的视频监控设备列表
            </div>

            <div class="code-block">
// Plan.api.ts 中新增的API方法
export const getCardVideoList = (params) => 
  defHttp.get({ url: Api.getCardVideoList, params }, { isTransformResponse: false });
            </div>

            <h3>2. 表格列扩展（已优化居中显示）</h3>
            <div class="code-block">
&lt;th scope="col" class="px-6 py-3 text-center text-xs font-medium text-cyan-700 uppercase tracking-wider"&gt;
  视频监控
&lt;/th&gt;

&lt;td class="px-6 py-4 whitespace-nowrap"&gt;
  &lt;div class="flex items-center justify-center"&gt;
    &lt;a-button type="link" size="small" @click="showCardVideoMonitor(record)"
              class="text-cyan-600 hover:text-cyan-800 transition-colors inline-flex items-center justify-center"&gt;
      &lt;Icon icon="mdi:video-wireless" class="mr-1" size="16"&gt;&lt;/Icon&gt;
      监控
    &lt;/a-button&gt;
  &lt;/div&gt;
&lt;/td&gt;
            </div>

            <h3>3. 视频监控弹窗组件（已优化空状态显示）</h3>
            <div class="code-block">
// 新建 CardVideoMonitorModal.vue 组件
- 支持1x1、2x2、3x3网格布局
- 集成VideoMonitorPlayerModal播放器
- 完善的加载状态和错误处理
- 科技感UI设计
- 🆕 空闲位置时播放器组件铺满整个区域
- 🆕 无视频时隐藏布局切换按钮
- 🆕 优化空状态显示效果
            </div>

            <h3>4. 核心方法实现</h3>
            <div class="code-block">
// 显示巡更点视频监控
const showCardVideoMonitor = (record) => {
  currentPatrolCard.value = record;
  cardVideoMonitorVisible.value = true;
  loadCardVideoList(record);
};

// 加载视频监控列表
const loadCardVideoList = async (record) => {
  const cardId = record.cardId || record.id;
  const response = await getCardVideoList({ cardId });
  // 处理响应数据...
};
            </div>

            <h2>🎯 使用流程</h2>
            <ol>
                <li><strong>查看巡更记录:</strong> 在巡更计划详情中查看巡更记录列表</li>
                <li><strong>点击监控按钮:</strong> 在"视频监控"列中点击"监控"按钮</li>
                <li><strong>弹窗显示:</strong> 系统自动根据巡更点ID查询关联的视频监控</li>
                <li><strong>视频播放:</strong> 在弹窗中查看该巡更点的实时或录像视频</li>
                <li><strong>布局切换:</strong> 支持1x1、2x2、3x3等不同网格布局</li>
            </ol>

            <h2>🔧 最新优化</h2>

            <h3>1. 监控按钮居中优化</h3>
            <div class="code-block">
// 优化前
&lt;td class="px-6 py-4 whitespace-nowrap text-center"&gt;
  &lt;a-button class="flex items-center justify-center"&gt;...&lt;/a-button&gt;
&lt;/td&gt;

// 优化后 - 使用flex容器确保完美居中
&lt;td class="px-6 py-4 whitespace-nowrap"&gt;
  &lt;div class="flex items-center justify-center"&gt;
    &lt;a-button class="inline-flex items-center justify-center"&gt;...&lt;/a-button&gt;
  &lt;/div&gt;
&lt;/td&gt;
            </div>

            <h3>2. 空闲位置铺满优化</h3>
            <div class="code-block">
// 计算显示列表时的优化
const displayVideoList = computed(() => {
  const list = [...props.videoList];

  // 如果没有视频，只显示一个空位置铺满整个区域
  if (list.length === 0) {
    return [{}] as VideoInfo[];
  }

  // 正常情况下填充空位置
  while (list.length < totalSlots) {
    list.push({} as VideoInfo);
  }

  return list.slice(0, totalSlots);
});
            </div>

            <h3>3. CSS样式优化</h3>
            <div class="code-block">
// 新增空状态专用样式
.video-grid.grid-empty {
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
  min-height: 500px;
}

.video-grid.grid-empty .empty-video-slot {
  min-height: 450px;

  .empty-content {
    padding: 40px;
  }
}
            </div>

            <div class="success">
                <h3>🎉 功能已完成并优化</h3>
                <p>巡更记录视频监控功能已成功集成到系统中，并完成了UI优化，提供了更好的用户体验和视觉效果。</p>
            </div>

            <h2>📋 文件修改清单</h2>
            <ul>
                <li><code>src/views/plan/Plan.api.ts</code> - 新增视频监控API接口</li>
                <li><code>src/views/plan/components/PlanDetailModal.vue</code> - 主要功能实现</li>
                <li><code>src/views/plan/components/CardVideoMonitorModal.vue</code> - 新建视频监控组件</li>
            </ul>
        </div>
    </div>
</body>
</html>
