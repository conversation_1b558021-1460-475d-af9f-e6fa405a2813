import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '姓名',
    align: "center",
    dataIndex: 'name'
  },
  {
    title: '照片',
    align: "center",
    dataIndex: 'image',
    customRender: render.renderImage,
  },
  {
    title: '警号',
    align: "center",
    dataIndex: 'num'
  },
  {
    title: '部门',
    align: "center",
    dataIndex: 'departName'
  },
  {
    title: '用户',
    align: "center",
    dataIndex: 'userName'
  },
];

// 高级查询数据
export const superQuerySchema = {
  name: {title: '姓名',order: 0,view: 'text', type: 'string',},
  image: {title: '照片',order: 1,view: 'image', type: 'string',},
  num: {title: '警号',order: 2,view: 'text', type: 'string',},
  departId: {title: '部门',order: 3,view: 'sel_depart', type: 'string',},
  userId: {title: '用户id',order: 4,view: 'sel_user', type: 'string',},
};
