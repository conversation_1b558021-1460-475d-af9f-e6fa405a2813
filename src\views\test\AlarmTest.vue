<template>
  <div class="alarm-test-container">
    <div class="test-header">
      <h2>报警弹窗测试页面</h2>
      <p>用于测试websocket接收到alarm命令时的报警灯闪烁弹窗效果</p>
    </div>

    <div class="test-controls">
      <a-space size="large">
        <a-button type="primary" @click="triggerAlarm" size="large">
          <Icon icon="ant-design:warning-filled" />
          触发报警测试
        </a-button>
        
        <a-button @click="triggerCustomAlarm" size="large">
          <Icon icon="ant-design:bell-filled" />
          自定义报警测试
        </a-button>
        
        <a-button @click="showModal = true" size="large">
          <Icon icon="ant-design:eye-filled" />
          直接显示弹窗
        </a-button>

        <a-button @click="triggerBatchAlarm" size="large" type="dashed">
          <Icon icon="ant-design:thunderbolt-filled" />
          批量报警测试
        </a-button>
      </a-space>
    </div>

    <div class="test-info">
      <a-card title="测试说明" size="small">
        <ul>
          <li>点击"触发报警测试"按钮模拟websocket接收到alarm命令</li>
          <li>点击"自定义报警测试"按钮可以自定义报警信息</li>
          <li>点击"直接显示弹窗"按钮直接显示报警弹窗</li>
          <li>点击"批量报警测试"按钮连续发送多个报警（测试性能）</li>
          <li>报警弹窗会显示闪烁的红色报警灯效果</li>
          <li>支持语音播报功能（如果浏览器支持）</li>
          <li>可在浏览器控制台使用 <code>window.alarmTest</code> 进行高级测试</li>
        </ul>
      </a-card>
    </div>

    <!-- 报警弹窗 -->
    <AlarmLightModal 
      v-model:open="showModal" 
      :alarm-data="testAlarmData" 
      @confirm="handleConfirm"
      @close="handleClose"
    />

    <!-- 自定义报警信息弹窗 -->
    <a-modal
      v-model:open="customModalVisible"
      title="自定义报警信息"
      @ok="triggerCustomAlarmConfirm"
      @cancel="customModalVisible = false"
    >
      <a-form layout="vertical">
        <a-form-item label="报警标题">
          <a-input v-model:value="customAlarm.msgTxt" placeholder="请输入报警标题" />
        </a-form-item>
        <a-form-item label="报警描述">
          <a-textarea v-model:value="customAlarm.msgDesc" placeholder="请输入报警描述" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { Card, Button, Space, Modal, Form, FormItem, Input, Textarea } from 'ant-design-vue';
import { Icon } from '/@/components/Icon';
import AlarmLightModal from '/@/components/AlarmLightModal/index.vue';
import { voice } from '/@/utils/voice';
import { sendRandomAlarm, createCustomAlarm, setupGlobalAlarmTest, sendBatchAlarms } from '/@/utils/alarmTest';

// 响应式数据
const showModal = ref(false);
const customModalVisible = ref(false);
const testAlarmData = ref({});

const customAlarm = ref({
  msgTxt: '紧急报警测试',
  msgDesc: '这是一个测试报警信息，用于验证报警弹窗功能是否正常工作。'
});

// 触发标准报警测试
const triggerAlarm = () => {
  sendRandomAlarm((alarmData) => {
    console.log('模拟websocket接收到alarm命令:', alarmData);
    testAlarmData.value = alarmData;
    showModal.value = true;
  });
};

// 触发自定义报警
const triggerCustomAlarm = () => {
  customModalVisible.value = true;
};

// 确认自定义报警
const triggerCustomAlarmConfirm = () => {
  createCustomAlarm(
    customAlarm.value.msgTxt || '自定义报警',
    customAlarm.value.msgDesc || '自定义报警描述',
    (alarmData) => {
      console.log('触发自定义报警:', alarmData);
      testAlarmData.value = alarmData;
      showModal.value = true;
    }
  );
  customModalVisible.value = false;
};

// 处理报警确认
const handleConfirm = (data: any) => {
  console.log('报警已确认处理:', data);
};

// 处理报警关闭
const handleClose = () => {
  console.log('报警弹窗已关闭');
};

// 批量报警测试
const triggerBatchAlarm = () => {
  sendBatchAlarms(3, 1500, (alarmData) => {
    console.log('批量报警:', alarmData);
    testAlarmData.value = alarmData;
    showModal.value = true;
  });
};

// 组件挂载时设置全局测试工具
onMounted(() => {
  setupGlobalAlarmTest();
});
</script>

<style lang="less" scoped>
.alarm-test-container {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 32px;
  
  h2 {
    color: #1890ff;
    margin-bottom: 8px;
  }
  
  p {
    color: #666;
    font-size: 14px;
  }
}

.test-controls {
  text-align: center;
  margin-bottom: 32px;
  
  .ant-btn {
    height: 48px;
    padding: 0 24px;
    font-size: 16px;
    border-radius: 8px;
    
    .anticon {
      margin-right: 8px;
    }
  }
}

.test-info {
  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        line-height: 1.6;
        color: #666;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
