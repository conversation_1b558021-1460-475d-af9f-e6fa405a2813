import { defHttp } from '/@/utils/http/axios';

enum Api {
  getPolicePatrolData = '/policePlan/policePlan/getPolicePlanData',
  getPoliceInfo = '/patrol/police/info',
  getPatrolPlans = '/patrol/police/plans',
  getCheckpoints = '/patrol/police/checkpoints',
  updateCheckpointStatus = '/patrol/police/checkpoint/status',
}

/**
 * 获取民警巡更大屏数据
 * @param params
 */
export const getPolicePatrolData = (params?: any) => {
  return defHttp.get({ 
    url: Api.getPolicePatrolData, 
    params: params || {} 
  },{ isTransformResponse: false });
};

/**
 * 获取民警信息
 * @param params
 */
export const getPoliceInfo = (params?: any) => {
  return defHttp.get({ 
    url: Api.getPoliceInfo, 
    params: params || {} 
  });
};

/**
 * 获取巡更计划列表
 * @param params
 */
export const getPatrolPlans = (params?: any) => {
  return defHttp.get({ 
    url: Api.getPatrolPlans, 
    params: params || {} 
  });
};

/**
 * 获取打卡点信息
 * @param params
 */
export const getCheckpoints = (params?: any) => {
  return defHttp.get({ 
    url: Api.getCheckpoints, 
    params: params || {} 
  });
};

/**
 * 更新打卡点状态
 * @param params
 */
export const updateCheckpointStatus = (params: any) => {
  return defHttp.post({ 
    url: Api.updateCheckpointStatus, 
    params 
  });
};

// 民警信息接口
export interface PoliceInfo {
  id: string | number;
  name: string;
  badge: string; // 警号
  avatar?: string;
  department: string;
  position: string;
  status: 'on-duty' | 'offline';
  phone?: string;
  imageError?: boolean; // 图片加载错误标识
}

// 打卡点接口
export interface Checkpoint {
  id: string | number;
  name?: string;
  cardName?: string; // 卡片名称（新字段）
  sectionName?: string; // 分监区名称
  departName?: string; // 部门名称（新字段）
  status: 'pending' | 'completed' | 'missed' | 0 | 1 | 2; // 支持数字状态：0待巡，1正常，2漏检
  checkTime?: string;
  patrolTime?: string; // 巡更时间（新字段）
  location?: {
    x: number;
    y: number;
  };
  description?: string;
  planCards?: PlanCard[]; // 巡更卡片列表
}

// 巡更卡片接口
export interface PlanCard {
  id: string | number;
  name?: string;
  cardName?: string; // 卡片名称（新字段）
  status: 0 | 1 | 2; // 0待巡，1正常，2漏检
  checkTime?: string;
  patrolTime?: string; // 巡更时间（新字段）
  location?: {
    x: number;
    y: number;
  };
}

// 巡更计划接口
export interface PatrolPlan {
  id: string | number;
  name: string;
  routeName: string; // 路线名称
  startTime: string;
  endTime: string;
  patrolOfficer: PoliceInfo;
  duration?: string; // 巡更时长（兼容旧字段）
  patrolDuration?: string; // 巡更时长（新字段）
  checkpoints: Checkpoint[];
  status: 'pending' | 'in-progress' | 'completed' | 'missed' | 0 | 1 | 2; // 支持数字状态：0待巡，1进行中，2已完成
  progress?: number; // 完成进度百分比
}

// 大屏数据接口
export interface PolicePatrolDashboardData {
  title: string;
  lineName?: string; // 监区名称
  policeList: PoliceInfo[];
  patrolPlans: PatrolPlan[];
  statistics: {
    totalPlans: number;
    completedPlans: number;
    inProgressPlans: number;
    missedPlans: number;
    totalCheckpoints: number;
    completedCheckpoints: number;
    missedCheckpoints: number;
  };
}

/**
 * 生成模拟数据
 */
export const generateMockPolicePatrolData = (): PolicePatrolDashboardData => {
  // 模拟民警信息
  const mockPolice: PoliceInfo[] = [
    {
      id: 1,
      name: '张警官',
      badge: 'P001',
      avatar: '/api/sys/common/static/temp/police1.jpg',
      department: '第一监区',
      position: '监区民警',
      status: 'on-duty',
      phone: '13800138001'
    },
    {
      id: 2,
      name: '李警官',
      badge: 'P002',
      avatar: '/api/sys/common/static/temp/police2.jpg',
      department: '第二监区',
      position: '监区民警',
      status: 'on-duty',
      phone: '13800138002'
    },
    {
      id: 3,
      name: '王警官',
      badge: 'P003',
      avatar: '/api/sys/common/static/temp/police3.jpg',
      department: '第三监区',
      position: '监区民警',
      status: 'offline',
      phone: '13800138003'
    },
    {
      id: 4,
      name: '赵警官',
      badge: 'P004',
      avatar: '/api/sys/common/static/temp/police4.jpg',
      department: '第四监区',
      position: '监区民警',
      status: 'on-duty',
      phone: '13800138004'
    },
    {
      id: 5,
      name: '陈警官',
      badge: 'P005',
      avatar: '/api/sys/common/static/temp/police5.jpg',
      department: '第五监区',
      position: '监区民警',
      status: 'on-duty',
      phone: '13800138005'
    },
    {
      id: 6,
      name: '刘警官',
      badge: 'P006',
      avatar: '/api/sys/common/static/temp/police6.jpg',
      department: '第六监区',
      position: '监区民警',
      status: 'offline',
      phone: '13800138006'
    },
    {
      id: 7,
      name: '杨警官',
      badge: 'P007',
      avatar: '/api/sys/common/static/temp/police7.jpg',
      department: '第七监区',
      position: '监区民警',
      status: 'on-duty',
      phone: '13800138007'
    },
    {
      id: 8,
      name: '周警官',
      badge: 'P008',
      avatar: '/api/sys/common/static/temp/police8.jpg',
      department: '第八监区',
      position: '监区民警',
      status: 'on-duty',
      phone: '13800138008'
    }
  ];

  // 生成打卡点数据
  const generateCheckpoints = (sectionName: string, planId: number): Checkpoint[] => {
    const checkpointNames = [`${sectionName}入口`, `${sectionName}出口`];

    return checkpointNames.map((name, index) => ({
      id: `${planId}-${index}`,
      name,
      sectionName,
      status: index === 0 ? 'completed' : (planId % 3 === 0 ? 'missed' : 'pending'),
      checkTime: index === 0 ? `${String(8 + Math.floor(planId / 2)).padStart(2, '0')}:${String((index * 15) % 60).padStart(2, '0')}` : undefined,
      location: { x: index * 100, y: 50 },
      description: `${name}巡更打卡点`
    }));
  };

  // 生成巡更计划
  const mockPlans: PatrolPlan[] = [];
  const sections = ['第一分监区', '第二分监区', '第三分监区', '第四分监区', '第五分监区'];
  const routes = ['A路线', 'B路线', 'C路线'];
  const statuses: ('pending' | 'in-progress' | 'completed' | 'missed')[] = ['pending', 'in-progress', 'completed', 'missed'];

  for (let i = 0; i < 8; i++) {
    const section = sections[i % sections.length];
    const route = routes[i % routes.length];
    const status = statuses[i % statuses.length];
    const officer = mockPolice[i % mockPolice.length];

    const startHour = 8 + Math.floor(i / 2);
    const endHour = startHour + 2;

    mockPlans.push({
      id: i + 1,
      name: `${section}巡更计划`,
      routeName: `${section}-${route}`,
      startTime: `${String(startHour).padStart(2, '0')}:00`,
      endTime: `${String(endHour).padStart(2, '0')}:00`,
      patrolOfficer: officer,
      duration: '2小时',
      checkpoints: generateCheckpoints(section, i + 1),
      status,
      progress: status === 'completed' ? 100 : (status === 'in-progress' ? 45 + (i * 10) % 40 : 0)
    });
  }

  // 计算统计数据
  const totalPlans = mockPlans.length;
  const completedPlans = mockPlans.filter(p => p.status === 'completed').length;
  const inProgressPlans = mockPlans.filter(p => p.status === 'in-progress').length;
  const missedPlans = mockPlans.filter(p => p.status === 'missed').length;

  const allCheckpoints = mockPlans.flatMap(p => p.checkpoints);
  const totalCheckpoints = allCheckpoints.length;
  const completedCheckpoints = allCheckpoints.filter(c => c.status === 'completed').length;
  const missedCheckpoints = allCheckpoints.filter(c => c.status === 'missed').length;

  return {
    title: '监区民警巡更数据大屏',
    lineName: sections[0], // 示例中使用第一个分监区作为监区名称
    policeList: mockPolice,
    patrolPlans: mockPlans,
    statistics: {
      totalPlans,
      completedPlans,
      inProgressPlans,
      missedPlans,
      totalCheckpoints,
      completedCheckpoints,
      missedCheckpoints
    }
  };
};
