<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>巡更记录视频监控功能优化对比</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.12);
        }
        .comparison-section {
            margin-bottom: 40px;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.12);
        }
        .section-header {
            background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
            color: white;
            padding: 20px;
            font-size: 1.2rem;
            font-weight: 600;
        }
        .comparison-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0;
        }
        .before, .after {
            padding: 30px;
        }
        .before {
            background: #fef3c7;
            border-right: 2px solid #f59e0b;
        }
        .after {
            background: #d1fae5;
        }
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .demo-table th, .demo-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #e2e8f0;
        }
        .demo-table th {
            background: #f1f5f9;
            font-weight: 600;
            color: #374151;
        }
        .monitor-btn-old {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            margin: 0 auto;
        }
        .monitor-btn-new {
            background: #06b6d4;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        .monitor-btn-container {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
        }
        .video-demo {
            width: 100%;
            height: 200px;
            border: 2px dashed #cbd5e1;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        }
        .video-demo-small {
            height: 120px;
            font-size: 14px;
        }
        .video-demo-large {
            height: 300px;
            font-size: 18px;
            font-weight: 600;
        }
        .icon {
            width: 16px;
            height: 16px;
            margin-right: 6px;
            background: currentColor;
            border-radius: 2px;
        }
        .tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 10px;
        }
        .tag-before {
            background: #fbbf24;
            color: #92400e;
        }
        .tag-after {
            background: #10b981;
            color: #065f46;
        }
        .improvement-list {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }
        .improvement-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
        }
        .improvement-list li::before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            margin-right: 10px;
            width: 20px;
            height: 20px;
            background: #d1fae5;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 巡更记录视频监控功能优化对比</h1>
            <p>展示优化前后的视觉效果和用户体验改进</p>
        </div>

        <!-- 监控按钮居中优化 -->
        <div class="comparison-section">
            <div class="section-header">
                1. 监控按钮居中显示优化
            </div>
            <div class="comparison-content">
                <div class="before">
                    <div class="tag tag-before">优化前</div>
                    <h3>问题：按钮对齐不完美</h3>
                    <table class="demo-table">
                        <thead>
                            <tr>
                                <th>巡更点</th>
                                <th>状态</th>
                                <th>视频监控</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>活动室</td>
                                <td>正常</td>
                                <td>
                                    <button class="monitor-btn-old">
                                        <span class="icon"></span>
                                        监控
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <p><strong>代码：</strong></p>
                    <code style="background: #f3f4f6; padding: 10px; display: block; border-radius: 4px; font-size: 12px;">
                        &lt;td class="text-center"&gt;<br>
                        &nbsp;&nbsp;&lt;a-button class="flex items-center"&gt;<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;监控<br>
                        &nbsp;&nbsp;&lt;/a-button&gt;<br>
                        &lt;/td&gt;
                    </code>
                </div>
                <div class="after">
                    <div class="tag tag-after">优化后</div>
                    <h3>解决：完美居中对齐</h3>
                    <table class="demo-table">
                        <thead>
                            <tr>
                                <th>巡更点</th>
                                <th>状态</th>
                                <th>视频监控</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>活动室</td>
                                <td>正常</td>
                                <td>
                                    <div class="monitor-btn-container">
                                        <button class="monitor-btn-new">
                                            <span class="icon"></span>
                                            监控
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <p><strong>代码：</strong></p>
                    <code style="background: #f3f4f6; padding: 10px; display: block; border-radius: 4px; font-size: 12px;">
                        &lt;td&gt;<br>
                        &nbsp;&nbsp;&lt;div class="flex items-center justify-center"&gt;<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&lt;a-button class="inline-flex items-center"&gt;<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;监控<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&lt;/a-button&gt;<br>
                        &nbsp;&nbsp;&lt;/div&gt;<br>
                        &lt;/td&gt;
                    </code>
                </div>
            </div>
        </div>

        <!-- 空状态显示优化 -->
        <div class="comparison-section">
            <div class="section-header">
                2. 空闲位置铺满优化
            </div>
            <div class="comparison-content">
                <div class="before">
                    <div class="tag tag-before">优化前</div>
                    <h3>问题：空状态显示区域较小</h3>
                    <div class="video-demo video-demo-small">
                        <div style="text-align: center;">
                            <div style="font-size: 24px; margin-bottom: 8px;">📹</div>
                            <div>空闲位置</div>
                        </div>
                    </div>
                    <ul class="improvement-list" style="list-style: disc; margin-left: 20px;">
                        <li style="color: #dc2626;">❌ 空状态显示不够醒目</li>
                        <li style="color: #dc2626;">❌ 用户可能忽略空状态信息</li>
                        <li style="color: #dc2626;">❌ 视觉效果不佳</li>
                    </ul>
                </div>
                <div class="after">
                    <div class="tag tag-after">优化后</div>
                    <h3>解决：铺满整个播放器区域</h3>
                    <div class="video-demo video-demo-large">
                        <div style="text-align: center;">
                            <div style="font-size: 48px; margin-bottom: 16px;">📹</div>
                            <div>该巡更点暂无视频监控</div>
                        </div>
                    </div>
                    <ul class="improvement-list">
                        <li>空状态显示更加醒目</li>
                        <li>自动隐藏布局切换按钮</li>
                        <li>提升用户体验</li>
                        <li>视觉效果更佳</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 总结 -->
        <div class="comparison-section">
            <div class="section-header">
                🎉 优化总结
            </div>
            <div style="padding: 30px;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px;">
                    <div>
                        <h3 style="color: #3b82f6;">🎯 用户体验提升</h3>
                        <ul class="improvement-list">
                            <li>监控按钮完美居中对齐</li>
                            <li>空状态显示更加醒目</li>
                            <li>界面布局更加合理</li>
                            <li>操作反馈更加直观</li>
                        </ul>
                    </div>
                    <div>
                        <h3 style="color: #10b981;">🔧 技术实现优化</h3>
                        <ul class="improvement-list">
                            <li>使用Flexbox确保完美居中</li>
                            <li>智能检测视频列表状态</li>
                            <li>动态调整布局和样式</li>
                            <li>优化CSS样式结构</li>
                        </ul>
                    </div>
                    <div>
                        <h3 style="color: #f59e0b;">📱 响应式设计</h3>
                        <ul class="improvement-list">
                            <li>适配不同屏幕尺寸</li>
                            <li>保持一致的视觉效果</li>
                            <li>优化移动端体验</li>
                            <li>提升可访问性</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
