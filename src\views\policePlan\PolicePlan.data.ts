import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { h } from 'vue';

//列表数据
export const columns: BasicColumn[] = [
  {
    title: '计划名称',
    align: "center",
    dataIndex: 'name',
    width: 200,
    ellipsis: true,
    customRender: ({ text }) => {
      return h('div', {
        style: {
          wordBreak: 'break-word',
          whiteSpace: 'normal',
          lineHeight: '1.4',
          padding: '4px 0',
          maxHeight: '60px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          display: '-webkit-box',
          WebkitLineClamp: 3,
          WebkitBoxOrient: 'vertical'
        },
        title: text || ''
      }, text || '');
    }
  },
  {
    title: '路线',
    align: "center",
    dataIndex: 'lineName',
    width: 120,
    ellipsis: true
  },
  {
    title: '开始时间',
    align: "center",
    dataIndex: 'startTime',
    width: 150,
    ellipsis: true
  },
  {
    title: '结束时间',
    align: "center",
    dataIndex: 'endTime',
    width: 150,
    ellipsis: true
  },
  {
    title: '状态',
    align: "center",
    dataIndex: 'status',
    width: 100,
    ellipsis: true,
    customRender: ({ text }) => {
      const status = parseInt(text);
      let statusText = '';
      let statusColor = '';
      let bgColor = '';

      switch (status) {
        case 0:
          statusText = '待巡';
          statusColor = '#faad14';
          bgColor = '#fff7e6';
          break;
        case 1:
          statusText = '计划进行中';
          statusColor = '#1890ff';
          bgColor = '#e6f7ff';
          break;
        case 2:
          statusText = '已完成';
          statusColor = '#52c41a';
          bgColor = '#f6ffed';
          break;
        default:
          statusText = '未知';
          statusColor = '#999';
          bgColor = '#f5f5f5';
      }

      return h('div', {
        style: {
          color: statusColor,
          backgroundColor: bgColor,
          padding: '4px 8px',
          borderRadius: '4px',
          border: `1px solid ${statusColor}`,
          fontSize: '12px',
          fontWeight: '500'
        }
      }, statusText);
    }
  },
  // {
  //   title: '创建人',
  //   align: "center",
  //   dataIndex: 'createUserId',
  //   width: 120,
  //   ellipsis: true
  // },
  // {
  //   title: '区间',
  //   align: "center",
  //   dataIndex: 'intervalDate',
  //   width: 150,
  //   ellipsis: true
  // },
  {
    title: '巡更民警',
    align: "center",
    dataIndex: 'patrolUserId',
    width: 150,
    ellipsis: true,
    customRender: ({ record }) => {
      const patrolUserName = record.patrolUserName;
      const patrolUserAvatar = record.patrolUserAvatar;

      if (!patrolUserName) {
        return h('div', { style: { color: '#999' } }, '未分配');
      }

      return h('div', {
        style: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '8px'
        }
      }, [
        // 头像
        patrolUserAvatar ?
          h('img', {
            src: patrolUserAvatar,
            style: {
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              objectFit: 'cover',
              border: '1px solid #d9d9d9'
            },
            onError: (e) => {
              // 头像加载失败时显示默认头像
              const target = e.target as HTMLImageElement;
              if (target) {
                target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9IiNmNWY1ZjUiLz4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxMiIgcj0iNSIgZmlsbD0iIzk5OSIvPgo8cGF0aCBkPSJNNiAyNmMwLTUuNSA0LjUtMTAgMTAtMTBzMTAgNC41IDEwIDEwIiBmaWxsPSIjOTk5Ii8+Cjwvc3ZnPgo=';
              }
            }
          }) :
          h('div', {
            style: {
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              backgroundColor: '#f0f0f0',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '14px',
              color: '#999',
              border: '1px solid #d9d9d9'
            }
          }, patrolUserName.charAt(0)),
        // 姓名
        h('span', {
          style: {
            fontSize: '14px',
            color: '#333',
            fontWeight: '500'
          }
        }, patrolUserName)
      ]);
    }
  },
  {
    title: '巡更统计',
    align: "center",
    dataIndex: 'patrolStatistics',
    width: 200,
    ellipsis: true,
    customRender: ({ record }) => {
      
      return h('div', {
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '2px',
          fontSize: '12px',
          lineHeight: '1.2'
        }
      }, [
        h('div', { style: { color: '#1890ff' } }, `巡更点数: ${record.totalPoints || 0}`),
        h('div', { style: { color: '#52c41a' } }, `正常数量: ${record.normalPoints || 0}`),
        h('div', { style: { color: '#faad14' } }, `待巡数量: ${record.pendingPoints || 0}`),
        h('div', { style: { color: '#ff4d4f' } }, `漏巡数量: ${record.missedPoints || 0}`)
      ]);
    }
  },

];

// 高级查询数据
export const superQuerySchema = {
  name: {title: '计划名称',order: 0,view: 'text', type: 'string',},
  lineId: {title: '路线',order: 1,view: 'text', type: 'string',},
  startTime: {title: '开始时间',order: 2,view: 'datetime', type: 'string',},
  endTime: {title: '结束时间',order: 3,view: 'datetime', type: 'string',},
  status: {title: '状态',order: 4,view: 'text', type: 'string',},
  createUserId: {title: '创建人',order: 5,view: 'text', type: 'string',},
  intervalDate: {title: '区间',order: 6,view: 'text', type: 'string',},
  patrolUserId: {title: '巡更民警',order: 7,view: 'text', type: 'string',},
  masterPlanId: {title: '巡更总计划',order: 8,view: 'text', type: 'string',},
};
