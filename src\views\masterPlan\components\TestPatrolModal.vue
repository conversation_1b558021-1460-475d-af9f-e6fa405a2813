<template>
  <a-modal 
    title="巡更记录测试" 
    :width="600" 
    v-model:open="visible" 
    @cancel="handleCancel"
  >
    <div class="p-4">
      <h3>🔍 巡更记录测试弹窗</h3>
      <p>这是一个简单的测试弹窗</p>
      <p>记录ID: {{ recordData.id }}</p>
      <p>记录名称: {{ recordData.name }}</p>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const visible = ref(false);
const recordData = ref<any>({});

const showModal = (record?: any) => {
  console.log('TestPatrolModal showModal called with:', record);
  if (record) {
    recordData.value = record;
  }
  visible.value = true;
};

const handleCancel = () => {
  visible.value = false;
  recordData.value = {};
};

defineExpose({
  showModal
});
</script>
