/**
 * 报警测试工具函数
 * 用于模拟websocket发送alarm命令
 */

import { voice } from '/@/utils/voice';

// 报警数据接口
export interface AlarmData {
  cmd: string;
  msgTxt: string;
  msgDesc?: string;
  timestamp?: Date;
  [key: string]: any;
}

// 预定义的测试报警数据
export const TEST_ALARM_DATA: AlarmData[] = [
  {
    cmd: 'alarm',
    msgTxt: '紧急报警：监区A区域异常',
    msgDesc: '检测到监区A区域有异常活动，请立即处理！',
    timestamp: new Date()
  },
  {
    cmd: 'alarm',
    msgTxt: '设备故障报警',
    msgDesc: '巡更设备001出现故障，无法正常工作，请及时维修。',
    timestamp: new Date()
  },
  {
    cmd: 'alarm',
    msgTxt: '人员脱岗报警',
    msgDesc: '巡更人员张三在预定时间内未到达指定巡更点，请确认情况。',
    timestamp: new Date()
  },
  {
    cmd: 'alarm',
    msgTxt: '系统异常报警',
    msgDesc: '巡更系统检测到数据异常，可能存在安全风险。',
    timestamp: new Date()
  },
  {
    cmd: 'alarm',
    msgTxt: '超时报警',
    msgDesc: '巡更任务执行超时，当前任务已超过预定完成时间30分钟。',
    timestamp: new Date()
  }
];

/**
 * 模拟发送报警消息
 * @param alarmData 报警数据
 * @param callback 回调函数，用于处理报警消息
 */
export function simulateAlarmMessage(
  alarmData: AlarmData, 
  callback?: (data: AlarmData) => void
) {
  console.log('模拟发送报警消息:', alarmData);
  
  // 播放语音提示
  if (alarmData.msgTxt) {
    voice(alarmData.msgTxt);
  }
  
  // 执行回调函数
  if (callback) {
    callback(alarmData);
  }
}

/**
 * 随机发送测试报警
 * @param callback 回调函数
 */
export function sendRandomAlarm(callback?: (data: AlarmData) => void) {
  const randomIndex = Math.floor(Math.random() * TEST_ALARM_DATA.length);
  const alarmData = {
    ...TEST_ALARM_DATA[randomIndex],
    timestamp: new Date()
  };
  
  simulateAlarmMessage(alarmData, callback);
}

/**
 * 创建自定义报警数据
 * @param msgTxt 报警标题
 * @param msgDesc 报警描述
 * @param callback 回调函数
 */
export function createCustomAlarm(
  msgTxt: string, 
  msgDesc?: string, 
  callback?: (data: AlarmData) => void
) {
  const alarmData: AlarmData = {
    cmd: 'alarm',
    msgTxt,
    msgDesc,
    timestamp: new Date()
  };
  
  simulateAlarmMessage(alarmData, callback);
}

/**
 * 批量发送测试报警（用于压力测试）
 * @param count 发送数量
 * @param interval 发送间隔（毫秒）
 * @param callback 回调函数
 */
export function sendBatchAlarms(
  count: number = 5, 
  interval: number = 2000, 
  callback?: (data: AlarmData) => void
) {
  let sentCount = 0;
  
  const timer = setInterval(() => {
    if (sentCount >= count) {
      clearInterval(timer);
      console.log(`批量报警测试完成，共发送 ${count} 条报警`);
      return;
    }
    
    sendRandomAlarm(callback);
    sentCount++;
  }, interval);
  
  console.log(`开始批量报警测试，将发送 ${count} 条报警，间隔 ${interval}ms`);
}

/**
 * 在控制台中提供全局测试函数
 */
export function setupGlobalAlarmTest() {
  // 将测试函数挂载到window对象上，方便在控制台中调用
  if (typeof window !== 'undefined') {
    (window as any).alarmTest = {
      sendRandom: sendRandomAlarm,
      sendCustom: createCustomAlarm,
      sendBatch: sendBatchAlarms,
      testData: TEST_ALARM_DATA
    };
    
    console.log('报警测试工具已加载，可在控制台使用以下命令：');
    console.log('- window.alarmTest.sendRandom() // 发送随机报警');
    console.log('- window.alarmTest.sendCustom("标题", "描述") // 发送自定义报警');
    console.log('- window.alarmTest.sendBatch(5, 2000) // 批量发送报警');
    console.log('- window.alarmTest.testData // 查看测试数据');
  }
}

// 导出默认对象
export default {
  simulateAlarmMessage,
  sendRandomAlarm,
  createCustomAlarm,
  sendBatchAlarms,
  setupGlobalAlarmTest,
  TEST_ALARM_DATA
};
