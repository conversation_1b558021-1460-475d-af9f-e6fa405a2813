<template>
  <div>
    <!--引用表格-->
   <BasicTable @register="registerTable" >
     <!--插槽:table标题-->
      <template #tableTitle>
          <a-button type="primary" v-auth="'policeRecord:patrol_police_record:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
          <a-button  type="primary" v-auth="'policeRecord:patrol_police_record:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
          <j-upload-button type="primary" v-auth="'policeRecord:patrol_police_record:importExcel'" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="batchHandleDelete">
                    <Icon icon="ant-design:delete-outlined"></Icon>
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button v-auth="'policeRecord:patrol_police_record:deleteBatch'">批量操作
                <Icon icon="mdi:chevron-down"></Icon>
              </a-button>
        </a-dropdown>
      
      </template>
       <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
      <!-- 视频监控截图 -->
      <template #screenshots="{ record }">
        <div class="screenshots-container">
          <div v-if="record.screenshots && record.screenshots.length > 0" class="screenshots-grid">
            <div
              v-for="(screenshot, index) in record.screenshots.slice(0, 4)"
              :key="index"
              class="screenshot-item"
              @click="previewImages(record.screenshots, index)"
            >
              <img
                :src="getFileAccessHttpUrl(screenshot.screenshotPath || screenshot)"
                :alt="`截图${index + 1}`"
                class="screenshot-image"
              />
              <!-- 显示更多图片的指示器 -->
              <div v-if="index === 3 && record.screenshots.length > 4" class="more-indicator">
                +{{ record.screenshots.length - 4 }}
              </div>
            </div>
          </div>
          <div v-else class="no-screenshots">
            <Icon icon="mdi:image-off" size="24" class="text-gray-400" />
            <span class="text-gray-400 text-sm">暂无截图</span>
          </div>
        </div>
      </template>
      <!--字段回显插槽-->
      <template v-slot:bodyCell="{ column, record, index, text }">
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <PoliceRecordModal @register="registerModal" @success="handleSuccess"></PoliceRecordModal>
    <PoliceRecordDetailModal ref="detailModal"></PoliceRecordDetailModal>
  </div>
</template>

<script lang="ts" name="policeRecord-policeRecord" setup>
  import {ref, reactive, computed, unref} from 'vue';
  import {BasicTable, useTable, TableAction} from '/@/components/Table';
  import {useModal} from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage'
  import PoliceRecordModal from './components/PoliceRecordModal.vue'
  import {columns, searchFormSchema, superQuerySchema} from './PoliceRecord.data';
  import {list, deleteOne, batchDelete, getImportUrl,getExportUrl} from './PoliceRecord.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import { useUserStore } from '/@/store/modules/user';
  import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';
  import { createImgPreview } from '/@/components/Preview/index';
  import PoliceRecordDetailModal from './components/PoliceRecordDetailModal.vue';
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  const detailModal = ref();
  //注册model
  const [registerModal, {openModal}] = useModal();
  //注册table数据
  const { prefixCls,tableContext,onExportXls,onImportXls } = useListPage({
      tableProps:{
           title: '民警巡更打卡表',
           api: list,
           columns,
           canResize:false,
           formConfig: {
              //labelWidth: 120,
              schemas: searchFormSchema,
              autoSubmitOnEnter:true,
              showAdvancedButton:true,
              fieldMapToNumber: [
              ],
              fieldMapToTime: [
                 ['recordTime', ['recordTime_begin', 'recordTime_end'], 'YYYY-MM-DD HH:mm:ss'],
              ],
            },
           actionColumn: {
               width: 120,
               fixed:'right'
            },
            beforeFetch: (params) => {
              return Object.assign(params, queryParam);
            },
      },
       exportConfig: {
            name:"民警巡更打卡表",
            url: getExportUrl,
            params: queryParam,
          },
          importConfig: {
            url: getImportUrl,
            success: handleSuccess
          },
  })

  const [registerTable, {reload},{ rowSelection, selectedRowKeys }] = tableContext

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }

   /**
   * 预览多张图片
   */
   function previewImages(images, startIndex = 0) {
    if (!images || images.length === 0) return;

    const imageUrls = images.map(img => {
      // 处理不同类型的图片对象
      if (typeof img === 'string') {
        return getFileAccessHttpUrl(img);
      } else if (img.url) {
        return getFileAccessHttpUrl(img.url);
      } else if (img.screenshotPath) {
        return getFileAccessHttpUrl(img.screenshotPath);
      } else {
        return getFileAccessHttpUrl(img);
      }
    });

    createImgPreview({
      imageList: imageUrls,
      index: startIndex || 0
    });
  }
   /**
    * 新增事件
    */
  function handleAdd() {
     openModal(true, {
       isUpdate: false,
       showFooter: true,
     });
  }
   /**
    * 编辑事件
    */
  function handleEdit(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: true,
     });
   }
   /**
    * 详情
   */
  function handleDetail(record: Recordable) {
    detailModal.value.showModal(record)
   }
   /**
    * 删除事件
    */
  async function handleDelete(record) {
     await deleteOne({id: record.id}, handleSuccess);
   }
   /**
    * 批量删除事件
    */
  async function batchHandleDelete() {
     await batchDelete({ids: selectedRowKeys.value}, handleSuccess);
   }
   /**
    * 成功回调
    */
  function handleSuccess() {
      (selectedRowKeys.value = []) && reload();
   }
   /**
      * 操作栏
      */
  function getTableAction(record){
       return [
       {
           label: '详情',
           onClick: handleDetail.bind(null, record),
         },
         {
           label: '编辑',
           onClick: handleEdit.bind(null, record),
           auth: 'policeRecord:patrol_police_record:edit'
         }
       ]
   }
     /**
        * 下拉操作栏
        */
  function getDropDownAction(record){
       return [
         {
           label: '详情',
           onClick: handleDetail.bind(null, record),
         }, {
           label: '删除',
           popConfirm: {
             title: '是否确认删除',
             confirm: handleDelete.bind(null, record),
             placement: 'topLeft',
           },
           auth: 'policeRecord:patrol_police_record:delete'
         }
       ]
   }



</script>

<style lang="less" scoped>
  :deep(.ant-picker),:deep(.ant-input-number){
    width: 100%;
  }
  
  /* 截图容器样式 */
  .screenshots-container {
    padding: 8px;
  }

  .screenshots-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 4px;
    max-width: 180px;
  }

  .screenshot-item {
    position: relative;
    width: 80px;
    height: 60px;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #e8e8e8;
  }

  .screenshot-item:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #1677ff;
  }

  .screenshot-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .more-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
  }

  .no-screenshots {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #999;
  }

  .no-screenshots .anticon {
    margin-bottom: 8px;
  }
</style>