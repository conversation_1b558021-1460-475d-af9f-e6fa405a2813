import { defHttp } from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  getPersonnelCards = '/card/card/personnel-cards',
  updateBinding = '/card/card/update-binding',
  getPatrolUsers = '/patrolUser/patrolUser/list',
}

/**
 * 获取当前用户部门管理的人员卡信息
 */
export const getPersonnelCards = () => defHttp.get({ url: Api.getPersonnelCards },{ isTransformResponse: false });

/**
 * 更新人员卡绑定
 * @param cardId 卡片ID
 * @param patrolUserId 巡更人员ID
 */
export const updateCardBinding = (id: string, patrolUserId?: string) => 
  defHttp.put({ 
    url: Api.updateBinding, 
    params: { id, patrolUserId } 
  },{ isTransformResponse: false });

/**
 * 获取巡更人员列表
 * @param params 查询参数
 */
export const getPatrolUsers = (params) => defHttp.get({ url: Api.getPatrolUsers, params },{ isTransformResponse: false });
