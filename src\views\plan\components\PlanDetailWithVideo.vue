<template>
  <div class="plan-detail-with-video">
    <a-tabs v-model:activeKey="activeTab" type="card" size="large">
      <!-- 计划信息标签页 -->
      <a-tab-pane key="info" tab="计划信息">
        <template #tab>
          <FileTextOutlined />
          计划信息
        </template>
        
        <div class="plan-info-content">
          <a-card title="基本信息" class="info-card">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="计划名称">{{ planDetail?.name }}</a-descriptions-item>
              <a-descriptions-item label="路线名称">{{ planDetail?.lineName }}</a-descriptions-item>
              <a-descriptions-item label="开始时间">{{ planDetail?.startTime }}</a-descriptions-item>
              <a-descriptions-item label="结束时间">{{ planDetail?.endTime }}</a-descriptions-item>
              <a-descriptions-item label="计划状态">
                <a-tag :color="getStatusColor(planDetail?.status)">
                  {{ getStatusText(planDetail?.status) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="巡更模式">{{ planDetail?.mode }}</a-descriptions-item>
              <a-descriptions-item label="创建时间">{{ planDetail?.createTime }}</a-descriptions-item>
              <a-descriptions-item label="创建人">{{ planDetail?.createUserName }}</a-descriptions-item>
            </a-descriptions>
          </a-card>
          
          <a-card title="巡更点信息" class="info-card">
            <a-table
              :columns="cardColumns"
              :data-source="planDetail?.cardList || []"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'status'">
                  <a-tag :color="getCardStatusColor(record.status)">
                    {{ getCardStatusText(record.status) }}
                  </a-tag>
                </template>
              </template>
            </a-table>
          </a-card>
          
          <a-card title="巡更记录" class="info-card">
            <a-table
              :columns="recordColumns"
              :data-source="planDetail?.recordList || []"
              :pagination="{ pageSize: 10 }"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'status'">
                  <a-tag :color="getRecordStatusColor(record.status)">
                    {{ getRecordStatusText(record.status) }}
                  </a-tag>
                </template>
              </template>
            </a-table>
          </a-card>
        </div>
      </a-tab-pane>
      
      <!-- 视频监控标签页 -->
      <a-tab-pane key="video" tab="视频监控">
        <template #tab>
          <VideoCameraOutlined />
          视频监控 ({{ videoList.length }})
        </template>
        
        <PlanVideoMonitor
          :plan-info="planInfo"
          :video-list="videoList"
        />
      </a-tab-pane>
      
      <!-- 统计分析标签页 -->
      <a-tab-pane key="statistics" tab="统计分析">
        <template #tab>
          <BarChartOutlined />
          统计分析
        </template>
        
        <div class="statistics-content">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-card>
                <a-statistic
                  title="总巡更点"
                  :value="statistics.totalPoints"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card>
                <a-statistic
                  title="已巡更点"
                  :value="statistics.completedPoints"
                  :value-style="{ color: '#52c41a' }"
                />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card>
                <a-statistic
                  title="漏巡点"
                  :value="statistics.missedPoints"
                  :value-style="{ color: '#ff4d4f' }"
                />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card>
                <a-statistic
                  title="完成率"
                  :value="statistics.completionRate"
                  suffix="%"
                  :value-style="{ color: '#722ed1' }"
                />
              </a-card>
            </a-col>
          </a-row>
          
          <!-- 这里可以添加图表组件 -->
          <a-card title="巡更进度图表" style="margin-top: 16px">
            <div class="chart-placeholder">
              <p>图表组件占位符</p>
              <p>可以集成 ECharts 或其他图表库显示巡更进度、时间分布等数据</p>
            </div>
          </a-card>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  FileTextOutlined,
  VideoCameraOutlined,
  BarChartOutlined
} from '@ant-design/icons-vue';
import PlanVideoMonitor from './PlanVideoMonitor.vue';

interface PlanDetail {
  id: string;
  name: string;
  lineName: string;
  startTime: string;
  endTime: string;
  status: string;
  mode: string;
  createTime: string;
  createUserName: string;
  cardList: any[];
  recordList: any[];
  videoList: any[];
}

interface Props {
  planId: string;
}

const props = defineProps<Props>();

// 响应式数据
const activeTab = ref('info');
const planDetail = ref<PlanDetail | null>(null);
const loading = ref(false);

// 计算属性
const planInfo = computed(() => {
  if (!planDetail.value) return null;
  return {
    id: planDetail.value.id,
    name: planDetail.value.name
  };
});

const videoList = computed(() => {
  return planDetail.value?.videoList || [];
});

const statistics = computed(() => {
  if (!planDetail.value) {
    return {
      totalPoints: 0,
      completedPoints: 0,
      missedPoints: 0,
      completionRate: 0
    };
  }
  
  const total = planDetail.value.cardList?.length || 0;
  const completed = planDetail.value.recordList?.filter(r => r.status === '1').length || 0;
  const missed = planDetail.value.recordList?.filter(r => r.status === '2').length || 0;
  const rate = total > 0 ? Math.round((completed / total) * 100) : 0;
  
  return {
    totalPoints: total,
    completedPoints: completed,
    missedPoints: missed,
    completionRate: rate
  };
});

// 表格列定义
const cardColumns = [
  { title: '卡号', dataIndex: 'cardNo', key: 'cardNo' },
  { title: '位置', dataIndex: 'location', key: 'location' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '备注', dataIndex: 'remark', key: 'remark' }
];

const recordColumns = [
  { title: '卡号', dataIndex: 'cardNo', key: 'cardNo' },
  { title: '打卡时间', dataIndex: 'recordTime', key: 'recordTime' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '巡更人员', dataIndex: 'patrolUser', key: 'patrolUser' }
];

// 组件挂载
onMounted(() => {
  loadPlanDetail();
});

/**
 * 加载计划详情
 */
async function loadPlanDetail() {
  if (!props.planId) {
    message.error('计划ID不能为空');
    return;
  }
  
  loading.value = true;
  
  try {
    // 这里应该调用实际的API
    // const response = await getPlanDetail(props.planId);
    // planDetail.value = response.data;
    
    // 模拟数据
    planDetail.value = {
      id: props.planId,
      name: '夜间巡更计划',
      lineName: '东区巡更路线',
      startTime: '2025-07-14 22:00:00',
      endTime: '2025-07-15 06:00:00',
      status: '1',
      mode: '定时巡更',
      createTime: '2025-07-14 18:00:00',
      createUserName: '管理员',
      cardList: [
        { cardNo: 'C001', location: '东门', status: '1', remark: '正常' },
        { cardNo: 'C002', location: '停车场', status: '0', remark: '待巡' },
        { cardNo: 'C003', location: '办公楼', status: '2', remark: '漏巡' }
      ],
      recordList: [
        { cardNo: 'C001', recordTime: '2025-07-14 22:30:00', status: '1', patrolUser: '张三' },
        { cardNo: 'C003', recordTime: '2025-07-14 23:00:00', status: '2', patrolUser: '张三' }
      ],
      videoList: [
        {
          id: 'video_001',
          name: '东门监控',
          videoUrl: 'rtsp://admin:password@192.168.1.100:554/stream1',
          streamId: 'plan_123_video_001',
          websocketUrl: '/websocket/video/123',
          hlsUrl: '/jeecgboot/api/video/hls/plan_123_video_001/index.m3u8',
          streamType: 'preview'
        },
        {
          id: 'video_002',
          name: '停车场监控',
          videoUrl: 'rtsp://admin:password@192.168.1.101:554/stream1',
          streamId: 'plan_123_video_002',
          websocketUrl: '/websocket/video/123',
          hlsUrl: '/jeecgboot/api/video/hls/plan_123_video_002/index.m3u8',
          streamType: 'playback'
        },
        {
          id: 'video_003',
          name: '办公楼监控',
          videoUrl: 'rtsp://admin:password@192.168.1.102:554/stream1',
          streamId: 'plan_123_video_003',
          websocketUrl: '/websocket/video/123',
          hlsUrl: '/jeecgboot/api/video/hls/plan_123_video_003/index.m3u8',
          streamType: 'preview'
        }
      ]
    };
    
  } catch (error) {
    console.error('加载计划详情失败:', error);
    message.error('加载计划详情失败');
  } finally {
    loading.value = false;
  }
}

/**
 * 获取状态颜色
 */
function getStatusColor(status: string) {
  switch (status) {
    case '0': return 'default';
    case '1': return 'green';
    case '2': return 'blue';
    default: return 'default';
  }
}

/**
 * 获取状态文本
 */
function getStatusText(status: string) {
  switch (status) {
    case '0': return '未开始';
    case '1': return '进行中';
    case '2': return '已完成';
    default: return '未知';
  }
}

/**
 * 获取卡片状态颜色
 */
function getCardStatusColor(status: string) {
  switch (status) {
    case '0': return 'default';
    case '1': return 'green';
    case '2': return 'red';
    default: return 'default';
  }
}

/**
 * 获取卡片状态文本
 */
function getCardStatusText(status: string) {
  switch (status) {
    case '0': return '待巡';
    case '1': return '已巡';
    case '2': return '漏巡';
    default: return '未知';
  }
}

/**
 * 获取记录状态颜色
 */
function getRecordStatusColor(status: string) {
  switch (status) {
    case '1': return 'green';
    case '2': return 'red';
    default: return 'default';
  }
}

/**
 * 获取记录状态文本
 */
function getRecordStatusText(status: string) {
  switch (status) {
    case '1': return '正常';
    case '2': return '异常';
    default: return '未知';
  }
}
</script>

<style scoped>
.plan-detail-with-video {
  width: 100%;
  height: 100%;
  padding: 16px;
}

.plan-info-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-card {
  margin-bottom: 16px;
}

.statistics-content {
  padding: 16px;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .plan-detail-with-video {
    padding: 8px;
  }
  
  .statistics-content .ant-col {
    margin-bottom: 16px;
  }
}
</style>
