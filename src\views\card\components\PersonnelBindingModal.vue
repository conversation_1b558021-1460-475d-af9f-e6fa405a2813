<template>
  <a-modal
    v-model:visible="visible"
    :title="modalTitle"
    width="600px"
    :confirm-loading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
  >
    <div class="binding-modal-content">
      <!-- 当前卡片信息 -->
      <div class="card-info-section">
        <h4 class="section-title">
          <Icon icon="ant-design:credit-card-outlined" />
          卡片信息
        </h4>
        <div class="card-info-display">
          <div class="info-row">
            <span class="label">卡片名称:</span>
            <span class="value">{{ currentCard?.name }}</span>
          </div>
          <div class="info-row">
            <span class="label">卡号:</span>
            <span class="value">{{ currentCard?.num }}</span>
          </div>
          <div class="info-row">
            <span class="label">所属路线:</span>
            <span class="value">{{ currentCard?.lineName }}</span>
          </div>
        </div>
      </div>

      <!-- 当前绑定信息 -->
      <div v-if="currentCard?.patrolUserId" class="current-binding-section">
        <h4 class="section-title">
          <Icon icon="ant-design:user-outlined" />
          当前绑定人员
        </h4>
        <div class="current-user-display">
          <div class="user-avatar">
            <img 
              v-if="currentCard?.patrolUserImage" 
              :src="getFileAccessHttpUrl(currentCard.patrolUserImage)" 
              :alt="currentCard.patrolUserName"
              @error="handleImageError"
            />
            <Icon v-else icon="ant-design:user-outlined" />
          </div>
          <div class="user-details">
            <div class="user-name">{{ currentCard?.patrolUserName }}</div>
            <div class="user-status">已绑定</div>
          </div>
        </div>
      </div>

      <!-- 选择新的绑定人员 -->
      <div class="select-user-section">
        <h4 class="section-title">
          <Icon icon="ant-design:team-outlined" />
          {{ currentCard?.patrolUserId ? '更换绑定人员' : '选择绑定人员' }}
        </h4>
        
        <!-- 搜索框 -->
        <div class="search-section">
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索人员姓名或编号"
            @search="searchUsers"
            @change="onSearchChange"
          />
        </div>

        <!-- 人员列表 -->
        <div class="users-list">
          <a-spin :spinning="usersLoading">
            <div v-if="filteredUsers.length === 0 && !usersLoading" class="empty-users">
              <Icon icon="ant-design:user-delete-outlined" class="empty-icon" />
              <p>暂无可选择的人员</p>
            </div>
            
            <div v-else class="users-grid">
              <div
                v-for="user in filteredUsers"
                :key="user.id"
                class="user-item"
                :class="{ 'selected': selectedUserId === user.id }"
                @click="selectUser(user)"
              >
                <div class="user-avatar">
                  <img 
                    v-if="user.image" 
                    :src="getFileAccessHttpUrl(user.image)" 
                    :alt="user.name"
                    @error="handleImageError"
                  />
                  <Icon v-else icon="ant-design:user-outlined" />
                </div>
                <div class="user-info">
                  <div class="user-name">{{ user.name }}</div>
                  <div class="user-id">编号: {{ user.num || user.id }}</div>
                </div>
                <div class="selection-indicator">
                  <Icon 
                    v-if="selectedUserId === user.id" 
                    icon="ant-design:check-circle-filled" 
                    class="selected-icon"
                  />
                </div>
              </div>
            </div>
          </a-spin>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import { Icon } from '/@/components/Icon';
import { getPatrolUsers, updateCardBinding } from '../PersonnelCard.api';
import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';

// 类型定义
interface PatrolUser {
  id: string;
  name: string;
  num?: string;
  image?: string;
}

interface CardInfo {
  id: string;
  name: string;
  num: string;
  lineName: string;
  patrolUserId?: string;
  patrolUserName?: string;
  patrolUserImage?: string;
}

// Props and Emits
const emit = defineEmits(['success']);

// 响应式数据
const visible = ref(false);
const confirmLoading = ref(false);
const usersLoading = ref(false);
const currentCard = ref<CardInfo | null>(null);
const selectedUserId = ref('');
const searchKeyword = ref('');
const patrolUsers = ref<PatrolUser[]>([]);

// 计算属性
const modalTitle = computed(() => {
  return currentCard.value?.patrolUserId ? '更换绑定人员' : '绑定人员';
});

const filteredUsers = computed(() => {
  if (!searchKeyword.value) {
    return patrolUsers.value;
  }
  const keyword = searchKeyword.value.toLowerCase();
  return patrolUsers.value.filter(user => 
    user.name?.toLowerCase().includes(keyword) ||
    user.num?.toLowerCase().includes(keyword) ||
    user.id?.toLowerCase().includes(keyword)
  );
});

// 方法
const open = (card: CardInfo) => {
  currentCard.value = { ...card };
  selectedUserId.value = card.patrolUserId || '';
  searchKeyword.value = '';
  visible.value = true;
  loadPatrolUsers();
};

const loadPatrolUsers = async () => {
  try {
    usersLoading.value = true;
    const result = await getPatrolUsers({ pageNo: 1, pageSize: 5000 });
    if (result.success) {
      patrolUsers.value = result.result?.records || [];
    } else {
      message.error('获取人员列表失败');
    }
  } catch (error) {
    console.error('加载人员列表失败:', error);
    message.error('加载人员列表失败');
  } finally {
    usersLoading.value = false;
  }
};

const selectUser = (user: PatrolUser) => {
  selectedUserId.value = user.id;
};

const searchUsers = () => {
  // 搜索逻辑已在计算属性中处理
};

const onSearchChange = () => {
  // 实时搜索
};

const handleOk = async () => {
  if (!selectedUserId.value) {
    message.warning('请选择要绑定的人员');
    return;
  }

  if (!currentCard.value) {
    message.error('卡片信息不存在');
    return;
  }

  try {
    confirmLoading.value = true;
    const result = await updateCardBinding(currentCard.value.id, selectedUserId.value);

    if (result.success) {
      message.success('绑定成功');
      const selectedUser = patrolUsers.value.find(u => u.id === selectedUserId.value);
      emit('success', currentCard.value.id, selectedUser);
      handleCancel();
    } else {
      message.error(result.message || '绑定失败');
    }
  } catch (error) {
    console.error('绑定失败:', error);
    message.error('绑定失败');
  } finally {
    confirmLoading.value = false;
  }
};

const handleCancel = () => {
  visible.value = false;
  currentCard.value = null;
  selectedUserId.value = '';
  searchKeyword.value = '';
};

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  if (target) {
    target.style.display = 'none';
  }
};

// 暴露方法
defineExpose({
  open
});
</script>

<style lang="less" scoped>
.binding-modal-content {
  padding: 0 8px;
  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;

    .anticon {
      margin-right: 8px;
      color: #667eea;
    }
  }

  .card-info-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;

    .card-info-display {
      .info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #666;
          font-size: 14px;
          min-width: 80px;
        }

        .value {
          color: #1a1a1a;
          font-weight: 500;
          font-size: 14px;
          text-align: right;
        }
      }
    }
  }

  .current-binding-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #e6f7ff;
    border-radius: 8px;
    border: 1px solid #91d5ff;

    .current-user-display {
      display: flex;
      align-items: center;

      .user-avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        overflow: hidden;
        border: 2px solid #1890ff;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .anticon {
          font-size: 24px;
          color: #1890ff;
        }
      }

      .user-details {
        .user-name {
          font-size: 16px;
          font-weight: 600;
          color: #1a1a1a;
          margin-bottom: 4px;
        }

        .user-status {
          font-size: 12px;
          color: #52c41a;
          background: #f6ffed;
          padding: 2px 8px;
          border-radius: 4px;
          display: inline-block;
        }
      }
    }
  }

  .select-user-section {
    .search-section {
      margin-bottom: 16px;
    }

    .users-list {
      max-height: 280px;
      overflow-y: auto;

      .empty-users {
        text-align: center;
        padding: 32px 20px;
        color: #999;

        .empty-icon {
          font-size: 48px;
          margin-bottom: 12px;
        }

        p {
          margin: 0;
          font-size: 14px;
        }
      }

      .users-grid {
        display: grid;
        gap: 8px;

        .user-item {
          display: flex;
          align-items: center;
          padding: 12px;
          border: 2px solid #f0f0f0;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: #d9d9d9;
            background: #fafafa;
          }

          &.selected {
            border-color: #1890ff;
            background: #e6f7ff;
          }

          .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            .anticon {
              font-size: 20px;
              color: #999;
            }
          }

          .user-info {
            flex: 1;

            .user-name {
              font-size: 14px;
              font-weight: 500;
              color: #1a1a1a;
              margin-bottom: 4px;
            }

            .user-id {
              font-size: 12px;
              color: #666;
            }
          }

          .selection-indicator {
            .selected-icon {
              font-size: 20px;
              color: #1890ff;
            }
          }
        }
      }
    }
  }
}

// 滚动条样式
.users-list::-webkit-scrollbar {
  width: 6px;
}

.users-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.users-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;

  &:hover {
    background: #a8a8a8;
  }
}
</style>

