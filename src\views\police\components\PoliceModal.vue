<template>
  <!-- 编辑/新增模态框 -->
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <PoliceForm ref="registerForm" @ok="submitCallback" :formDisabled="disableSubmit" :formBpm="false"></PoliceForm>
  </j-modal>
</template>

<script lang="ts" setup>
  import { ref, nextTick, defineExpose } from 'vue';
  import PoliceForm from './PoliceForm.vue'
  import JModal from '/@/components/Modal/src/JModal/JModal.vue';

  const title = ref<string>('');
  const width = ref<number>(800);
  const visible = ref<boolean>(false);
  const disableSubmit = ref<boolean>(false);
  const registerForm = ref();
  const emit = defineEmits(['register', 'success']);

  /**
   * 新增
   */
  function add() {
    title.value = '新增';
    visible.value = true;
    nextTick(() => {
      registerForm.value.add();
    });
  }

  /**
   * 编辑
   * @param record
   */
  function edit(record) {
    title.value = '编辑';
    nextTick(() => {
      registerForm.value.edit(record);
    });
    visible.value = true;
  }
  
  /**
   * 确定按钮点击事件
   */
  function handleOk() {
    registerForm.value.submitForm();
  }

  /**
   * form保存回调事件
   */
  function submitCallback() {
    handleCancel();
    emit('success');
  }

  /**
   * 取消按钮回调事件
   */
  function handleCancel() {
    visible.value = false;
  }

  defineExpose({
    add,
    edit,
    disableSubmit,
  });
</script>

<style lang="less">
  /**隐藏样式-modal确定按钮 */
  .jee-hidden {
    display: none !important;
  }
</style>

<style lang="less" scoped>
/* 科技感模态框 */
.tech-modal {
  :deep(.ant-modal-content) {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    border: 1px solid rgba(59, 130, 246, 0.15);
    box-shadow:
      0 25px 50px rgba(59, 130, 246, 0.1),
      0 10px 30px rgba(6, 182, 212, 0.05);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 16px;
    overflow: hidden;
  }

  :deep(.ant-modal-header) {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 16px 16px 0 0;
  }

  :deep(.ant-modal-title) {
    color: #1e293b;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
  }

  :deep(.ant-modal-body) {
    padding: 0;
  }
}
</style>
