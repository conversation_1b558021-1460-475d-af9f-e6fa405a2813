<template>
  <div class="p-4">
    <a-card title="报警详情功能测试" class="mb-4">
      <p>这是一个测试页面，用于演示报警详情弹窗功能。</p>
      <a-space>
        <a-button type="primary" @click="showDetailModal">
          查看报警详情
        </a-button>
        <a-button @click="showDetailModalWithData">
          查看带数据的报警详情
        </a-button>
      </a-space>
    </a-card>

    <!-- 模拟报警列表 -->
    <a-card title="模拟报警列表">
      <a-table 
        :columns="columns" 
        :data-source="mockData" 
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === '0' ? 'red' : 'green'">
              {{ record.status === '0' ? '未处理' : '已处理' }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-button type="link" size="small" @click="showDetailModal(record)">
              查看详情
            </a-button>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 报警详情弹窗 -->
    <AlarmDetailModal ref="detailModalRef" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import AlarmDetailModal from './components/AlarmDetailModal.vue';

const detailModalRef = ref();

// 表格列定义
const columns = [
  {
    title: '设备编号',
    dataIndex: 'deviceNum',
    key: 'deviceNum',
  },
  {
    title: '设备名称',
    dataIndex: 'deviceName',
    key: 'deviceName',
  },
  {
    title: '报警时间',
    dataIndex: 'alarmTime',
    key: 'alarmTime',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '操作',
    key: 'action',
  },
];

// 模拟数据
const mockData = [
  {
    id: '1',
    deviceNum: 'DEV001',
    deviceName: '监控设备1',
    lineName: '巡更路线A',
    alarmTime: '2024-01-15 14:30:25',
    status: '0',
    handleUserName: '',
    handleTime: '',
    opinion: '',
  },
  {
    id: '2',
    deviceNum: 'DEV002',
    deviceName: '监控设备2',
    lineName: '巡更路线B',
    alarmTime: '2024-01-15 13:20:15',
    status: '1',
    handleUserName: '张三',
    handleTime: '2024-01-15 13:25:30',
    opinion: '已确认，设备正常',
  },
  {
    id: '3',
    deviceNum: 'DEV003',
    deviceName: '监控设备3',
    lineName: '巡更路线C',
    alarmTime: '2024-01-15 12:10:45',
    status: '0',
    handleUserName: '',
    handleTime: '',
    opinion: '',
  },
];

// 显示详情弹窗
function showDetailModal(record?: any) {
  const testRecord = record || mockData[0];
  detailModalRef.value?.showModal(testRecord);
}

// 显示带完整数据的详情弹窗
function showDetailModalWithData() {
  const fullRecord = {
    id: '999',
    deviceNum: 'DEV999',
    deviceName: '测试监控设备',
    lineName: '测试巡更路线',
    alarmTime: '2024-01-15 15:45:30',
    status: '0',
    handleUserName: '',
    handleTime: '',
    opinion: '',
  };
  detailModalRef.value?.showModal(fullRecord);
}
</script>

<style scoped>
.p-4 {
  padding: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
