<template>
  <a-modal
    v-model:open="visible"
    :title="null"
    :footer="null"
    :closable="false"
    :mask-closable="false"
    :width="800"
    :centered="true"
    :z-index="9999"
    class="alarm-video-monitor-modal"
    :body-style="{ padding: 0 }"
  >
    <div class="alarm-video-container">
      <!-- 报警信息头部 -->
      <div class="alarm-header">
        <div class="alarm-title">
          <div class="alarm-icon-wrapper">
            <Icon icon="ant-design:warning-filled" class="alarm-icon" />
            <div class="alarm-pulse"></div>
          </div>
          <div class="alarm-text">
            <h3>紧急报警 - 视频监控</h3>
            <p class="alarm-message">{{ alarmData?.msgTxt || '系统检测到异常情况，正在调取相关监控视频' }}</p>
          </div>
        </div>
        <div class="alarm-time">
          {{ formatTime(alarmData?.timestamp || new Date()) }}
        </div>
      </div>

      <!-- 视频监控区域 -->
      <div class="video-monitor-section">
        <!-- 视频网格 - 固定2x2布局显示4个视频框 -->
        <div class="video-grid">
          <div
            v-for="(video, index) in displayVideoList"
            :key="video.id || index"
            class="video-grid-item"
          >
            <VideoMonitorPlayerModal
              v-if="video.id"
              :ref="el => setVideoPlayerRef(el, index)"
              :plan-id="'alarm-monitor'"
              :video-info="video"
              :auto-start="true"
              :show-controls="true"
              @error="onVideoError(index, $event)"
            />
            <div v-else class="empty-video-slot">
              <div class="empty-content">
                <Icon icon="mdi:video-off" :size="videoList.length === 0 ? 80 : 48" class="text-gray-400 mb-2"></Icon>
                <p class="text-gray-500 text-lg" v-if="videoList.length === 0">暂无相关监控视频</p>
                <p class="text-gray-500" v-else>空闲位置</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="alarm-actions">
        <a-button
          type="default"
          size="large"
          @click="handleClose"
          class="close-btn"
        >
          <Icon icon="ant-design:close-outlined" />
          关闭
        </a-button>
        <a-button
          type="primary"
          danger
          size="large"
          @click="handleConfirm"
          class="confirm-btn"
          :loading="confirmLoading"
        >
          <Icon icon="ant-design:check-circle-outlined" />
          确认处理
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onUnmounted } from 'vue';
import { Icon } from '/@/components/Icon';
import VideoMonitorPlayerModal from '/@/views/plan/components/VideoMonitorPlayerModal.vue';

interface VideoInfo {
  id: string;
  name: string;
  videoUrl: string;
  streamId: string;
  websocketUrl: string;
  cameraIndexCode?: string;
  streamType?: string;
  rtspUrl?: string;
}

interface AlarmData {
  msgTxt?: string;
  msgDesc?: string;
  timestamp?: Date;
  location?: string;
  deviceId?: string;
  videoList?: VideoInfo[];
}

interface Props {
  open: boolean;
  alarmData?: AlarmData;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:open', 'confirm', 'close']);

// 响应式数据
const visible = ref(false);
const videoList = ref<VideoInfo[]>([]);
const confirmLoading = ref(false); // 确认按钮加载状态

// 视频播放器引用
const videoPlayerRefs = ref<any[]>([]);

// 计算属性 - 固定显示4个视频位置（2x2网格）
const displayVideoList = computed(() => {
  const list = [...videoList.value];

  // 如果没有视频，显示4个空位置
  if (list.length === 0) {
    return [{}, {}, {}, {}] as VideoInfo[];
  }

  // 固定显示4个位置，不足的用空位置填充
  while (list.length < 4) {
    list.push({} as VideoInfo);
  }

  // 只显示前4个视频
  return list.slice(0, 4);
});

// 设置视频播放器引用
const setVideoPlayerRef = (el: any, index: number) => {
  if (el) {
    videoPlayerRefs.value[index] = el;
  }
};

// 视频错误处理
const onVideoError = (index: number, error: any) => {
  console.error(`视频播放器 ${index} 错误:`, error);
};

// 格式化时间
const formatTime = (date: Date) => {
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 加载报警相关的视频监控
const loadAlarmVideoList = () => {
  if (!props.alarmData) return;

  // 使用传入的视频列表
  if (props.alarmData.videoList && props.alarmData.videoList.length > 0) {
    videoList.value = props.alarmData.videoList;
    console.log('使用传入的视频监控列表:', videoList.value);
  } else {
    videoList.value = [];
    console.log('没有可用的视频监控');
  }
};

// 处理确认
const handleConfirm = () => {
  confirmLoading.value = true;

  // 构建确认数据，包含固定的处理意见
  const confirmData = {
    ...props.alarmData,
    handleOpinion: '已确认处理', // 固定的处理意见
    handleTime: new Date(),
    handleStatus: 1 // 已处理状态
  };

  emit('confirm', confirmData);

  // 注意：不在这里关闭弹窗，让父组件在API调用成功后关闭
  // handleClose();
};

// 处理关闭
const handleClose = () => {
  // 停止所有视频播放
  videoPlayerRefs.value.forEach(player => {
    if (player && typeof player.stopVideo === 'function') {
      player.stopVideo();
    }
  });

  // 重置状态
  confirmLoading.value = false;

  emit('close');
  emit('update:open', false);
};

// 监听open属性变化
watch(() => props.open, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    loadAlarmVideoList();
    // 重置加载状态
    confirmLoading.value = false;
  }
});

// 监听visible变化
watch(visible, (newVal) => {
  if (!newVal) {
    emit('update:open', false);
  }
});

// 组件卸载时清理
onUnmounted(() => {
  videoPlayerRefs.value.forEach(player => {
    if (player && typeof player.cleanup === 'function') {
      player.cleanup();
    }
  });
});
</script>

<style lang="less" scoped>
.alarm-video-monitor-modal {
  :deep(.ant-modal-content) {
    border-radius: 16px;
    overflow: hidden;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  }
}

.alarm-video-container {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  color: #ffffff;
  min-height: 700px;
}

.alarm-header {
  padding: 24px;
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.alarm-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.alarm-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.alarm-icon {
  font-size: 32px;
  color: #ffffff;
  z-index: 2;
  position: relative;
}

.alarm-pulse {
  position: absolute;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.alarm-text {
  h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #ffffff;
  }
  
  .alarm-message {
    margin: 4px 0 0 0;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
  }
}

.alarm-time {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-family: 'Courier New', monospace;
}

.video-monitor-section {
  padding: 24px;
  flex: 1;
}

.video-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 12px;
  height: 500px;
}

.video-grid-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 212, 255, 0.3);
  transition: all 0.3s ease;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  &:hover {
    border-color: rgba(0, 212, 255, 0.6);
    box-shadow: 0 4px 20px rgba(0, 212, 255, 0.2);
  }

  // 确保视频播放器组件填充整个容器
  :deep(.video-monitor-player-modal) {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  :deep(.video-player-wrapper) {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 6px;
    overflow: hidden;
  }

  :deep(.video-header) {
    padding: 4px 6px;
    min-height: 28px;

    h5 {
      font-size: 11px;
      margin: 0;
    }

    .video-controls {
      gap: 2px;

      .ant-btn {
        padding: 0 4px;
        height: 20px;
        font-size: 12px;
        min-width: 20px;
      }
    }
  }

  :deep(.video-content) {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  :deep(.video-player-area) {
    flex: 1;
    min-height: 0;
    max-height: 200px;
  }

  :deep(.video-info-bar) {
    padding: 2px 6px;
    min-height: 24px;

    .ant-tag {
      font-size: 10px;
      padding: 0 4px;
      height: 16px;
      line-height: 14px;
    }
  }
}

.empty-video-slot {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  .empty-content {
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
  }
}



.alarm-actions {
  padding: 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.close-btn, .confirm-btn {
  min-width: 120px;
  height: 40px;
  border-radius: 8px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.close-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
  }
}

.confirm-btn {
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
  border: none;
  
  &:hover {
    background: linear-gradient(135deg, #ff3742 0%, #ff2f3a 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 71, 87, 0.4);
  }
}
</style>
