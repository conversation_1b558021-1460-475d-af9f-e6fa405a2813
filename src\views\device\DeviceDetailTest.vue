<template>
  <div class="device-detail-test">
    <div class="test-container">
      <h2>设备详情模态框测试</h2>
      <p>点击下面的按钮测试不同状态的设备详情展示效果：</p>
      
      <div class="test-buttons">
        <a-button type="primary" @click="showOnlineDevice" class="test-btn">
          <Icon icon="mdi:check-circle" />
          在线设备
        </a-button>
        
        <a-button @click="showOfflineDevice" class="test-btn">
          <Icon icon="mdi:close-circle" />
          离线设备
        </a-button>
        
        <a-button danger @click="showErrorDevice" class="test-btn">
          <Icon icon="mdi:alert-circle" />
          故障设备
        </a-button>
        
        <a-button type="dashed" @click="showUnknownDevice" class="test-btn">
          <Icon icon="mdi:help-circle" />
          未知状态
        </a-button>
      </div>
    </div>
    
    <!-- 设备详情模态框 -->
    <DeviceDetailModal ref="detailModal"></DeviceDetailModal>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Icon } from '/@/components/Icon';
import DeviceDetailModal from './components/DeviceDetailModal.vue';

const detailModal = ref();

// 模拟在线设备数据
function showOnlineDevice() {
  const mockDevice = {
    id: '1',
    num: 'DEV-001',
    name: '巡更点设备A',
    deviceTypeName: '巡更点设备',
    lineName: '一号巡更线路',
    sn: 'SN123456789',
    status: '1',
    statusName: '在线',
    deviceUserName: '巡更人员',
    createUserName: '管理员',
    createTime: '2024-01-15 10:30:00'
  };
  detailModal.value.showModal(mockDevice);
}

// 模拟离线设备数据
function showOfflineDevice() {
  const mockDevice = {
    id: '2',
    num: 'DEV-002',
    name: '监控摄像头B',
    deviceTypeName: '监控设备',
    lineName: '二号巡更线路',
    sn: 'SN987654321',
    status: '0',
    statusName: '离线',
    deviceUserName: '监控中心',
    createUserName: '技术员',
    createTime: '2024-01-10 14:20:00'
  };
  detailModal.value.showModal(mockDevice);
}

// 模拟故障设备数据
function showErrorDevice() {
  const mockDevice = {
    id: '3',
    num: 'DEV-003',
    name: '报警器设备C',
    deviceTypeName: '报警设备',
    lineName: '三号巡更线路',
    sn: 'SN456789123',
    status: '2',
    statusName: '故障',
    deviceUserName: '安保部门',
    createUserName: '维护员',
    createTime: '2024-01-08 09:15:00'
  };
  detailModal.value.showModal(mockDevice);
}

// 模拟未知状态设备数据
function showUnknownDevice() {
  const mockDevice = {
    id: '4',
    num: 'DEV-004',
    name: '传感器设备D',
    deviceTypeName: '传感器',
    lineName: '四号巡更线路',
    sn: 'SN789123456',
    status: '3',
    statusName: '未知',
    deviceUserName: '环境监测',
    createUserName: '系统',
    createTime: '2024-01-05 16:45:00'
  };
  detailModal.value.showModal(mockDevice);
}
</script>

<style scoped lang="less">
.device-detail-test {
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
}

.test-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 32px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  h2 {
    text-align: center;
    color: #1e293b;
    margin-bottom: 16px;
    background: linear-gradient(135deg, #1e293b, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  p {
    text-align: center;
    color: #64748b;
    margin-bottom: 32px;
  }
}

.test-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.test-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  height: auto;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

@media (max-width: 768px) {
  .test-container {
    margin: 16px;
    padding: 24px;
  }

  .test-buttons {
    flex-direction: column;
    align-items: center;
  }

  .test-btn {
    width: 200px;
    justify-content: center;
  }
}
</style>
