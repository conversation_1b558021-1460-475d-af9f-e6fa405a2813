<template>
  <div class="tech-container">
   
    
    <!-- 民警头像和基本信息卡片 -->
    <div class="tech-card mb-6">
      <div class="flex items-center space-x-6">
        <!-- 头像区域 -->
        <div class="tech-avatar-container">
          <div class="tech-avatar">
            <img
              v-if="policeDetail.image"
              :src="getImageUrl(policeDetail.image)"
              :alt="policeDetail.name"
              class="w-24 h-24 rounded-full object-cover border-4 border-blue-200/50"
            />
            <div
              v-else
              class="w-24 h-24 rounded-full bg-gradient-to-br from-blue-400 to-cyan-500 flex items-center justify-center text-white text-2xl font-bold"
            >
              {{ getNameInitial(policeDetail.name) }}
            </div>
          </div>
          <!-- 在线状态指示器 -->
          <div class="status-indicator online"></div>
        </div>
        
        <!-- 基本信息 -->
        <div class="flex-1">
          <div class="flex items-center space-x-4 mb-3">
            <h2 class="text-2xl font-bold text-gray-800">{{ policeDetail.name || '未知' }}</h2>
            <div class="tech-badge">
              <Icon icon="ant-design:safety-certificate-outlined" class="mr-1" />
              在职
            </div>
          </div>
          <div class="grid grid-cols-2 gap-4">
            <div class="info-item">
              <span class="label">警号</span>
              <span class="value">{{ policeDetail.num || '未设置' }}</span>
            </div>
            <div class="info-item">
              <span class="label">部门</span>
              <span class="value">{{ policeDetail.departName || '未分配' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细信息卡片 -->
    <div class="tech-card mb-6">
      <div class="card-header">
        <Icon icon="ant-design:info-circle-outlined" class="text-blue-500 mr-2" />
        <h3 class="text-lg font-semibold text-gray-800">详细信息</h3>
      </div>
      <div class="card-content">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="info-group">
            <div class="info-row">
              <span class="info-label">姓名</span>
              <span class="info-value">{{ policeDetail.name || '未填写' }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">警号</span>
              <span class="info-value">{{ policeDetail.num || '未填写' }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">部门</span>
              <span class="info-value">{{ policeDetail.departName || '未分配' }}</span>
            </div>
          </div>
          <div class="info-group">
            <div class="info-row">
              <span class="info-label">关联用户</span>
              <span class="info-value">{{ policeDetail.userName || '未关联' }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">创建时间</span>
              <span class="info-value">{{ formatDate(policeDetail.createTime) || '未知' }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">更新时间</span>
              <span class="info-value">{{ formatDate(policeDetail.updateTime) || '未知' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <div class="tech-card">
      <div class="card-header">
        <Icon icon="ant-design:bar-chart-outlined" class="text-green-500 mr-2" />
        <h3 class="text-lg font-semibold text-gray-800">工作统计</h3>
      </div>
      <div class="card-content">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="stat-item">
            <div class="stat-icon bg-blue-100">
              <Icon icon="ant-design:calendar-outlined" class="text-blue-600" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ policeDetail.totalPlans }}</div>
              <div class="stat-label">总巡逻次数</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon bg-green-100">
              <Icon icon="ant-design:check-circle-outlined" class="text-green-600" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ policeDetail.completedPlans }}</div>
              <div class="stat-label">完成巡逻</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon bg-orange-100">
              <Icon icon="ant-design:clock-circle-outlined" class="text-orange-600" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ policeDetail.pendingPlans }}</div>
              <div class="stat-label">待执行巡逻</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon bg-purple-100">
              <Icon icon="ant-design:trophy-outlined" class="text-purple-600" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ policeDetail.completionRate }}%</div>
              <div class="stat-label">完成率</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, watch } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';
  import { detail } from '../Police.api';
  import { defHttp } from '/@/utils/http/axios';
  import dayjs from 'dayjs';

  interface PoliceData {
    id?: string;
    name?: string;
    image?: string;
    num?: string;
    departId?: string;
    userId?: string;
    createTime?: string;
    updateTime?: string;
  }

  const props = defineProps<{
    policeData: PoliceData;
  }>();

  // 详细的民警数据
  const policeDetail = ref<PoliceData>({});

  const departmentName = ref<string>('');
  const stats = ref({
    totalPatrols: 0,
    completedPatrols: 0,
    pendingPatrols: 0,
    completionRate: 0
  });

  // 获取图片URL
  const getImageUrl = (imagePath: string) => {
    if (!imagePath) return '';
    return getFileAccessHttpUrl(imagePath);
  };

  // 获取姓名首字母
  const getNameInitial = (name: string) => {
    if (!name) return '?';
    return name.charAt(0).toUpperCase();
  };

  // 格式化日期
  const formatDate = (dateStr: string) => {
    if (!dateStr) return '';
    return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss');
  };

  // 获取民警详情数据
  const fetchPoliceDetail = async () => {
    if (!props.policeData.id) return;
    try {
      const response = await detail({ id: props.policeData.id });
      if (response.success && response.result) {
        policeDetail.value = response.result;
        
      }
    } catch (error) {
      console.error('获取民警详情失败:', error);
     
    }
  };

  onMounted(() => {
    fetchPoliceDetail();
  });
</script>

<style scoped lang="less">
/* 科技感容器 */
.tech-container {
  position: relative;
  padding: 24px;
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.03) 0%, transparent 50%);
}

/* 科技网格背景 */
.tech-grid-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  z-index: 0;
}

/* 科技卡片 */
.tech-card {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 16px;
  padding: 24px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow:
    0 4px 20px rgba(59, 130, 246, 0.08),
    0 2px 10px rgba(6, 182, 212, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  z-index: 1;
}

.tech-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 30px rgba(59, 130, 246, 0.12),
    0 4px 20px rgba(6, 182, 212, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border-color: rgba(59, 130, 246, 0.2);
}

/* 头像容器 */
.tech-avatar-container {
  position: relative;
  display: inline-block;
}

.tech-avatar {
  position: relative;
  overflow: hidden;
}

/* 状态指示器 */
.status-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-indicator.online {
  background: linear-gradient(135deg, #10b981, #059669);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* 科技徽章 */
.tech-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1));
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-radius: 20px;
  color: #059669;
  font-size: 12px;
  font-weight: 500;
}

/* 信息项 */
.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.value {
  font-size: 14px;
  color: #1e293b;
  font-weight: 600;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.card-content {
  margin-top: 16px;
}

/* 信息组 */
.info-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
  min-width: 80px;
}

.info-value {
  font-size: 14px;
  color: #1e293b;
  font-weight: 600;
  text-align: right;
}

/* 统计项 */
.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.6), rgba(248, 250, 252, 0.4));
  border: 1px solid rgba(226, 232, 240, 0.3);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
  margin-top: 4px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tech-container {
    padding: 16px;
  }

  .tech-card {
    padding: 16px;
  }

  .flex.items-center.space-x-6 {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .grid.grid-cols-2.gap-4 {
    grid-template-columns: 1fr;
  }

  .grid.grid-cols-2.md\\:grid-cols-4.gap-4 {
    grid-template-columns: 1fr 1fr;
  }
}
</style>
