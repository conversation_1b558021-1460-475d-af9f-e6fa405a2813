<template>
  <a-modal
    :title="title"
    :width="width"
    v-model:visible="open"
    :footer="null"
    @cancel="handleCancel"
    class="tech-modal"
  >
    <div class="tech-container">
     

      <!-- 巡更记录信息卡片 -->
      <div class="tech-card main-info-card">
        <!-- 科技感装饰线条 -->
        <div class="decoration-line top-line"></div>
        <div class="decoration-line bottom-line"></div>

        <!-- 状态指示器 -->
        <div class="status-indicator" :class="getStatusIndicatorClass(info.status)"></div>

        <div class="card-header">
          <div class="header-left">
            <h2 class="card-title">
              <Icon icon="mdi:clipboard-text" class="title-icon" />
              民警巡更记录详情
            </h2>
            <div class="status-badge" :class="getStatusBadgeClass(info.status)">
              <span class="status-dot" :class="getStatusDotClass(info.status)"></span>
              {{ info.statusName }}
            </div>
          </div>
          <div class="header-right">
            <div class="time-display">
              <Icon icon="mdi:clock-outline" class="time-icon" />
              <span class="time-text">{{ info.recordTime }}</span>
            </div>
          </div>
        </div>

        <!-- 信息网格 -->
        <div class="info-grid">
          <div class="info-item">
            <div class="info-icon blue-gradient">
              <Icon icon="mdi:devices" />
            </div>
            <div class="info-content">
              <span class="info-label">设备号</span>
              <span class="info-value">{{ info.deviceNum }}</span>
            </div>
          </div>

          <div class="info-item">
            <div class="info-icon green-gradient">
              <Icon icon="mdi:desktop-classic" />
            </div>
            <div class="info-content">
              <span class="info-label">设备名称</span>
              <span class="info-value">{{ info.deviceName }}</span>
            </div>
          </div>

          <div class="info-item">
            <div class="info-icon purple-gradient">
              <Icon icon="mdi:map-marker" />
            </div>
            <div class="info-content">
              <span class="info-label">线路名称</span>
              <span class="info-value">{{ info.lineName }}</span>
            </div>
          </div>

          <div class="info-item">
            <div class="info-icon orange-gradient">
              <Icon icon="mdi:card-account-details" />
            </div>
            <div class="info-content">
              <span class="info-label">地址卡号</span>
              <span class="info-value">{{ info.cardNum }}</span>
            </div>
          </div>

          <div class="info-item">
            <div class="info-icon cyan-gradient">
              <Icon icon="mdi:map-marker-radius" />
            </div>
            <div class="info-content">
              <span class="info-label">地址名称</span>
              <span class="info-value">{{ info.cardName }}</span>
            </div>
          </div>

          <div class="info-item">
            <div class="info-icon indigo-gradient">
              <Icon icon="mdi:account" />
            </div>
            <div class="info-content">
              <span class="info-label">民警</span>
              <span class="info-value">{{ info.deviceUserName }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 视频监控截图卡片 -->
      <div class="tech-card screenshots-card">
        <!-- 科技感装饰线条 -->
        <div class="decoration-line top-line-alt"></div>
        <div class="decoration-line bottom-line-alt"></div>

        <div class="card-header">
          <h3 class="card-title">
            <Icon icon="mdi:video-wireless" class="title-icon" />
            视频监控截图
          </h3>
          <div class="screenshot-count" v-if="info.screenshots && info.screenshots.length > 0">
            共 {{ info.screenshots.length }} 张
          </div>
        </div>

        <!-- 截图网格 -->
        <div v-if="info.screenshots && info.screenshots.length > 0" class="screenshots-gallery">
          <div class="screenshots-grid">
            <div
              v-for="(screenshot, index) in info.screenshots"
              :key="index"
              class="screenshot-item"
              @click="previewImages(info.screenshots, index)"
            >
              <img
                :src="getFileAccessHttpUrl(screenshot.screenshotPath || screenshot)"
                :alt="`截图${index + 1}`"
                class="screenshot-image"
              />
              <div class="screenshot-overlay">
                <Icon icon="mdi:eye" size="24" class="overlay-icon" />
              </div>
              <div class="screenshot-number">{{ index + 1 }}</div>
            </div>
          </div>
        </div>

        <!-- 无截图状态 -->
        <div v-else class="no-screenshots">
          <div class="no-screenshots-icon">
            <Icon icon="mdi:video-off" size="64" />
          </div>
          <p class="no-screenshots-text">暂无视频监控截图</p>
          <p class="no-screenshots-desc">系统未检测到相关的监控截图数据</p>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, defineExpose, reactive } from "vue"
import { detailUrl } from "../PoliceRecord.api";
import { createImgPreview } from '/@/components/Preview/index';
import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';
import { Icon } from '/@/components/Icon';
import JModal from "@/components/Modal/src/JModal/JModal.vue";


const width = ref<number>(1200);
const title = ref<string>('详情');
const open = ref<boolean>(false)
const info = ref<any>([]);


function showModal(record) {

  open.value = true
  detail(record.id)

}


function detail(id) {
  detailUrl({id: id})
    .then((res) => {
      if (res.success) {
        info.value = res.result
        // 模拟截图数据（实际项目中从后端获取）
        if (!info.value.screenshots) {
          info.value.screenshots = [
            'https://via.placeholder.com/400x300/4CAF50/white?text=截图1',
            'https://via.placeholder.com/400x300/2196F3/white?text=截图2',
            'https://via.placeholder.com/400x300/FF9800/white?text=截图3'
          ];
        }
      } else {
        console.log(res.message)
      }
    }).finally(() => {

  });
}

// 获取状态颜色
function getStatusColor(status) {
  switch(status) {
    case '0':
    case 0:
      return 'orange';
    case '1':
    case 1:
      return 'green';
    case '2':
    case 2:
      return 'red';
    default:
      return 'gray';
  }
}

// 获取状态指示器样式
function getStatusIndicatorClass(status) {
  switch(status) {
    case '0':
    case 0:
      return 'status-warning';
    case '1':
    case 1:
      return 'status-success';
    case '2':
    case 2:
      return 'status-error';
    default:
      return 'status-default';
  }
}

// 获取状态徽章样式
function getStatusBadgeClass(status) {
  switch(status) {
    case '0':
    case 0:
      return 'badge-warning';
    case '1':
    case 1:
      return 'badge-success';
    case '2':
    case 2:
      return 'badge-error';
    default:
      return 'badge-default';
  }
}

// 获取状态点样式
function getStatusDotClass(status) {
  switch(status) {
    case '0':
    case 0:
      return 'dot-warning';
    case '1':
    case 1:
      return 'dot-success';
    case '2':
    case 2:
      return 'dot-error';
    default:
      return 'dot-default';
  }
}

/**
 * 预览多张图片
 */
function previewImages(images, startIndex = 0) {
  if (!images || images.length === 0) return;

  const imageUrls = images.map(img => {
    // 处理不同类型的图片对象
    if (typeof img === 'string') {
      return img.startsWith('http') ? img : getFileAccessHttpUrl(img);
    } else if (img.url) {
      return getFileAccessHttpUrl(img.url);
    } else if (img.screenshotPath) {
      return getFileAccessHttpUrl(img.screenshotPath);
    } else {
      return getFileAccessHttpUrl(img);
    }
  });

  createImgPreview({
    imageList: imageUrls,
    index: startIndex || 0
  });
}



/**
 * 取消按钮回调时间
 */
function handleCancel() {
  open.value = false;
}

defineExpose({
  showModal,
  detail
});
</script>

<style scoped lang="less">
/* 科技感模态框 */
.tech-modal {
  :deep(.ant-modal-content) {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    border: 1px solid rgba(59, 130, 246, 0.15);
    box-shadow:
      0 25px 50px rgba(59, 130, 246, 0.1),
      0 10px 30px rgba(6, 182, 212, 0.05);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 16px;
    overflow: hidden;
  }

  :deep(.ant-modal-header) {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 16px 16px 0 0;
  }

  :deep(.ant-modal-title) {
    color: #1e293b;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
  }

  :deep(.ant-modal-body) {
    padding: 0;
  }
}

/* 科技感容器 */
.tech-container {
  position: relative;
  padding: 24px;
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.03) 0%, transparent 50%);
}

/* 科技网格背景 */
.tech-grid-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  z-index: 0;
}

/* 科技卡片 */
.tech-card {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow:
    0 4px 20px rgba(59, 130, 246, 0.08),
    0 2px 10px rgba(6, 182, 212, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  z-index: 1;
}

.tech-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 30px rgba(59, 130, 246, 0.12),
    0 4px 20px rgba(6, 182, 212, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border-color: rgba(59, 130, 246, 0.2);
}

/* 装饰线条 */
.decoration-line {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.6), transparent);
  animation: pulse-line 3s ease-in-out infinite;
}

.top-line {
  top: 0;
  left: 20%;
  right: 20%;
}

.bottom-line {
  bottom: 0;
  left: 30%;
  right: 30%;
}

.top-line-alt {
  top: 0;
  left: 25%;
  right: 25%;
  background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.6), transparent);
}

.bottom-line-alt {
  bottom: 0;
  left: 35%;
  right: 35%;
  background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.6), transparent);
}

/* 状态指示器 */
.status-indicator {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse-glow 2s ease-in-out infinite;
}

.status-success { background: #10b981; }
.status-warning { background: #f59e0b; }
.status-error { background: #ef4444; }
.status-default { background: #6b7280; }

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.header-right {
  display: flex;
  align-items: center;
}

.card-title {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  text-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
}

.title-icon {
  margin-right: 8px;
  color: #3b82f6;
}

/* 状态徽章 */
.status-badge {
  width: 100px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid;
}

.badge-success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(52, 211, 153, 0.1));
  color: #047857;
  border-color: rgba(16, 185, 129, 0.3);
}

.badge-warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(251, 191, 36, 0.1));
  color: #92400e;
  border-color: rgba(245, 158, 11, 0.3);
}

.badge-error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(248, 113, 113, 0.1));
  color: #991b1b;
  border-color: rgba(239, 68, 68, 0.3);
}

.badge-default {
  background: linear-gradient(135deg, rgba(107, 114, 128, 0.1), rgba(156, 163, 175, 0.1));
  color: #374151;
  border-color: rgba(107, 114, 128, 0.3);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
  animation: pulse-glow 2s ease-in-out infinite;
}

.dot-success { background: #10b981; }
.dot-warning { background: #f59e0b; }
.dot-error { background: #ef4444; }
.dot-default { background: #6b7280; }

/* 时间显示 */
.time-display {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(6, 182, 212, 0.1));
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  color: #1e40af;
  font-weight: 500;
}

.time-icon {
  margin-right: 6px;
  color: #3b82f6;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.6), rgba(248, 250, 252, 0.4));
  border: 1px solid rgba(59, 130, 246, 0.08);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.info-item:hover {
  transform: translateY(-1px);
  border-color: rgba(59, 130, 246, 0.15);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.info-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.blue-gradient { background: linear-gradient(135deg, #3b82f6, #1d4ed8); }
.green-gradient { background: linear-gradient(135deg, #10b981, #047857); }
.purple-gradient { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
.orange-gradient { background: linear-gradient(135deg, #f59e0b, #d97706); }
.cyan-gradient { background: linear-gradient(135deg, #06b6d4, #0891b2); }
.indigo-gradient { background: linear-gradient(135deg, #6366f1, #4f46e5); }

.info-content {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 4px;
  font-weight: 500;
}

.info-value {
  font-size: 16px;
  color: #1e293b;
  font-weight: 600;
}

/* 截图相关样式 */
.screenshot-count {
  padding: 4px 12px;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(59, 130, 246, 0.1));
  border: 1px solid rgba(6, 182, 212, 0.2);
  border-radius: 12px;
  color: #0891b2;
  font-size: 12px;
  font-weight: 600;
}

.screenshots-gallery {
  margin-top: 16px;
}

.screenshots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.screenshot-item {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(59, 130, 246, 0.1);
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.screenshot-item:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 24px rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
}

.screenshot-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
  display: block;
}

.screenshot-overlay {
  position: absolute;
  inset: 0;
  background: rgba(59, 130, 246, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.screenshot-item:hover .screenshot-overlay {
  opacity: 1;
}

.overlay-icon {
  color: white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.screenshot-number {
  position: absolute;
  top: 8px;
  left: 8px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 无截图状态 */
.no-screenshots {
  text-align: center;
  padding: 60px 20px;
  color: #64748b;
}

.no-screenshots-icon {
  margin-bottom: 16px;
  color: #94a3b8;
}

.no-screenshots-text {
  font-size: 18px;
  font-weight: 600;
  color: #475569;
  margin-bottom: 8px;
}

.no-screenshots-desc {
  font-size: 14px;
  color: #94a3b8;
  margin: 0;
}

/* 动画 */
@keyframes pulse-line {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 currentColor;
    opacity: 1;
  }
  50% {
    box-shadow: 0 0 0 4px transparent;
    opacity: 0.8;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tech-container {
    padding: 16px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .screenshots-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .screenshots-grid {
    grid-template-columns: 1fr;
  }

  .info-item {
    flex-direction: column;
    text-align: center;
  }

  .info-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }
}
</style>
