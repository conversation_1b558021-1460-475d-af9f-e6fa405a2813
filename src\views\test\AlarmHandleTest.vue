<template>
  <div class="alarm-handle-test">
    <div class="test-header">
      <h1>报警处理功能测试</h1>
      <p>测试报警视频监控弹窗的处理意见输入和API调用功能</p>
    </div>

    <div class="test-controls">
      <a-space size="large">
        <a-button type="primary" size="large" @click="showTestAlarm">
          触发测试报警
        </a-button>
        <a-button type="default" size="large" @click="clearLogs">
          清空日志
        </a-button>
      </a-space>
    </div>

    <div class="test-info">
      <div class="info-card">
        <h3>🔧 新增功能</h3>
        <ul>
          <li><strong>API集成</strong> - 集成了 Alarm.api 的 handleUrl 接口</li>
          <li><strong>固定处理意见</strong> - 默认使用"已确认处理"</li>
          <li><strong>加载状态</strong> - 确认按钮显示加载状态</li>
          <li><strong>错误处理</strong> - 完善的错误提示和处理</li>
          <li><strong>简化界面</strong> - 删除处理意见输入框，简化操作</li>
        </ul>
      </div>

      <div class="info-card">
        <h3>📋 API参数</h3>
        <ul>
          <li><strong>接口地址</strong> - /alarm/alarm/handle</li>
          <li><strong>请求方法</strong> - POST</li>
          <li><strong>id</strong> - 报警ID</li>
          <li><strong>handleOpinion</strong> - 处理意见</li>
          <li><strong>handleStatus</strong> - 处理状态(1-已处理)</li>
          <li><strong>handleTime</strong> - 处理时间</li>
          <li><strong>handleUser</strong> - 处理人</li>
        </ul>
      </div>
    </div>

    <div class="test-logs">
      <h3>📝 操作日志</h3>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
        <div v-if="logs.length === 0" class="no-logs">暂无日志</div>
      </div>
    </div>

    <!-- 报警视频监控模态框 -->
    <AlarmVideoMonitorModal
      v-model:open="alarmModalVisible"
      :alarm-data="testAlarmData"
      @confirm="handleAlarmConfirm"
      @close="handleAlarmClose"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import AlarmVideoMonitorModal from '/@/components/AlarmVideoMonitorModal/index.vue';
import { handleUrl } from '/@/views/alarm/Alarm.api';
import { useMessage } from '/@/hooks/web/useMessage';
import { useUserStore } from '/@/store/modules/user';

const { createMessage } = useMessage();
const userStore = useUserStore();

// 响应式数据
const alarmModalVisible = ref(false);
const logs = ref<Array<{time: string, message: string, type: string}>>([]);

// 测试报警数据
const testAlarmData = ref({
  id: 'test_alarm_001',
  alarmId: 'test_alarm_001',
  msgTxt: '测试报警 - 点击确认处理按钮',
  msgDesc: '这是一个用于测试处理功能的模拟报警，将使用默认处理意见',
  timestamp: new Date(),
  location: '测试区域',
  deviceId: 'TEST-CAM-001',
  videoList: [
    {
      id: 'test-video-1',
      name: '测试摄像头-01',
      videoUrl: 'http://test.example.com/video1.mp4',
      streamId: 'test-stream-001',
      websocketUrl: 'ws://test.example.com/stream1',
      cameraIndexCode: 'TEST-CAM-001',
      streamType: 'preview',
      rtspUrl: 'rtsp://test.example.com/stream1'
    },
    {
      id: 'test-video-2',
      name: '测试摄像头-02',
      videoUrl: 'http://test.example.com/video2.mp4',
      streamId: 'test-stream-002',
      websocketUrl: 'ws://test.example.com/stream2',
      cameraIndexCode: 'TEST-CAM-002',
      streamType: 'preview',
      rtspUrl: 'rtsp://test.example.com/stream2'
    }
  ]
});

// 添加日志
const addLog = (message: string, type: string = 'info') => {
  const time = new Date().toLocaleTimeString();
  logs.value.unshift({ time, message, type });
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50);
  }
};

// 显示测试报警
const showTestAlarm = () => {
  addLog('触发测试报警', 'info');
  alarmModalVisible.value = true;
};

// 清空日志
const clearLogs = () => {
  logs.value = [];
  addLog('日志已清空', 'info');
};

// 处理报警确认
const handleAlarmConfirm = async (data: any) => {
  addLog(`开始处理报警，ID: ${data.id}`, 'info');
  addLog(`处理意见: ${data.handleOpinion || '已确认处理'}`, 'info');
  
  try {
    // 构建处理参数
    const handleParams = {
      id: data.id || data.alarmId,
      handleOpinion: data.handleOpinion || '已确认处理',
      handleStatus: 1,
      handleTime: new Date().toISOString(),
      handleUser: userStore.getUserInfo?.realname || '测试用户'
    };

    addLog(`调用API参数: ${JSON.stringify(handleParams)}`, 'info');
    
    // 调用API处理报警
    const result = await handleUrl(handleParams);
    
    if (result.success) {
      addLog('报警处理成功', 'success');
      createMessage.success('报警处理成功');
      alarmModalVisible.value = false;
    } else {
      addLog(`报警处理失败: ${result.message}`, 'error');
      createMessage.warning(result.message || '报警处理失败');
    }
    
  } catch (error: any) {
    addLog(`API调用失败: ${error.message || error}`, 'error');
    createMessage.error('报警处理失败，请稍后重试');
  }
};

// 处理报警关闭
const handleAlarmClose = () => {
  addLog('报警弹窗已关闭', 'info');
};
</script>

<style lang="less" scoped>
.alarm-handle-test {
  padding: 32px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  min-height: 100vh;
  color: #ffffff;
}

.test-header {
  text-align: center;
  margin-bottom: 32px;
  
  h1 {
    color: #00d4ff;
    font-size: 28px;
    margin-bottom: 12px;
    font-weight: 600;
  }
  
  p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    margin: 0;
  }
}

.test-controls {
  text-align: center;
  margin-bottom: 32px;
}

.test-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.info-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 24px;
  border: 1px solid rgba(0, 212, 255, 0.3);
  
  h3 {
    color: #00d4ff;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
  }
  
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    
    li {
      padding: 6px 0;
      padding-left: 16px;
      position: relative;
      color: rgba(255, 255, 255, 0.9);
      font-size: 13px;
      line-height: 1.4;
      
      &:before {
        content: '•';
        position: absolute;
        left: 0;
        color: #00d4ff;
      }
      
      strong {
        color: #ffffff;
      }
    }
  }
}

.test-logs {
  max-width: 1200px;
  margin: 0 auto;
  
  h3 {
    color: #00d4ff;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 600;
  }
}

.log-container {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid rgba(0, 212, 255, 0.2);
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-family: 'Courier New', monospace;
  font-size: 12px;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.info {
    color: rgba(255, 255, 255, 0.8);
  }
  
  &.success {
    color: #52c41a;
  }
  
  &.error {
    color: #ff4d4f;
  }
}

.log-time {
  color: #00d4ff;
  min-width: 80px;
}

.log-message {
  flex: 1;
  word-break: break-all;
}

.no-logs {
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
  padding: 20px;
}

@media (max-width: 768px) {
  .test-info {
    grid-template-columns: 1fr;
  }
}
</style>
