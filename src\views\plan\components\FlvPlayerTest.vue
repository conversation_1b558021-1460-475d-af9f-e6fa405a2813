<template>
  <div class="flv-player-test">
    <div class="test-header">
      <h2>FLV视频播放测试</h2>
      <p>测试JavaCV优化后的FLV视频流播放功能</p>
    </div>

    <div class="test-controls">
      <a-space>
        <a-input 
          v-model:value="testFlvUrl" 
          placeholder="输入FLV视频流URL" 
          style="width: 400px"
        />
        <a-button type="primary" @click="startTest" :loading="testing">
          开始测试
        </a-button>
        <a-button @click="stopTest" :disabled="!playing">
          停止测试
        </a-button>
        <a-button @click="clearLog">
          清空日志
        </a-button>
      </a-space>
    </div>

    <div class="test-content">
      <div class="video-section">
        <h3>视频播放区域</h3>
        <div class="video-container">
          <video
            ref="videoElement"
            class="test-video"
            controls
            muted
            @loadstart="onVideoEvent('loadstart')"
            @loadeddata="onVideoEvent('loadeddata')"
            @loadedmetadata="onVideoEvent('loadedmetadata')"
            @canplay="onVideoEvent('canplay')"
            @play="onVideoEvent('play')"
            @pause="onVideoEvent('pause')"
            @error="onVideoEvent('error')"
            @waiting="onVideoEvent('waiting')"
            @stalled="onVideoEvent('stalled')"
          >
            您的浏览器不支持视频播放
          </video>
          
          <div class="video-info">
            <a-descriptions size="small" :column="2">
              <a-descriptions-item label="播放状态">
                <a-tag :color="playing ? 'green' : 'red'">
                  {{ playing ? '播放中' : '已停止' }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="FLV支持">
                <a-tag :color="flvSupported ? 'green' : 'red'">
                  {{ flvSupported ? '支持' : '不支持' }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="视频尺寸">
                {{ videoWidth }}x{{ videoHeight }}
              </a-descriptions-item>
              <a-descriptions-item label="播放时长">
                {{ playDuration }}
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </div>
      </div>

      <div class="log-section">
        <h3>测试日志</h3>
        <div class="log-container">
          <div 
            v-for="(log, index) in logs" 
            :key="index" 
            class="log-item"
            :class="log.type"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';

// 动态加载flv.js
let flvjs: any = null;

const loadFlvJs = async () => {
  if (flvjs) return true;
  
  try {
    // 尝试从CDN加载flv.js
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/flv.js@latest/dist/flv.min.js';
    
    await new Promise((resolve, reject) => {
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
    
    flvjs = (window as any).flvjs;
    addLog('info', 'flv.js 加载成功');
    return true;
  } catch (error) {
    addLog('error', 'flv.js 加载失败: ' + error);
    return false;
  }
};

// 响应式数据
const videoElement = ref<HTMLVideoElement>();
const testFlvUrl = ref('http://localhost:8080/patrol-api/video/flv/plan_123_video_456');
const testing = ref(false);
const playing = ref(false);
const logs = ref<Array<{time: string, message: string, type: string}>>([]);
const videoWidth = ref(0);
const videoHeight = ref(0);
const playDuration = ref('00:00:00');

let flvPlayer: any = null;
let durationTimer: NodeJS.Timeout | null = null;
let playStartTime = 0;

// 计算属性
const flvSupported = computed(() => {
  return flvjs && flvjs.isSupported();
});

// 组件挂载
onMounted(async () => {
  addLog('info', '组件初始化');
  await loadFlvJs();
});

// 组件卸载
onUnmounted(() => {
  stopTest();
});

// 添加日志
function addLog(type: string, message: string) {
  const now = new Date();
  const time = now.toLocaleTimeString();
  logs.value.push({ time, message, type });
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value.shift();
  }
  
  console.log(`[${type.toUpperCase()}] ${message}`);
}

// 清空日志
function clearLog() {
  logs.value = [];
}

// 视频事件处理
function onVideoEvent(eventType: string) {
  addLog('info', `视频事件: ${eventType}`);
  
  if (videoElement.value) {
    const video = videoElement.value;
    videoWidth.value = video.videoWidth || 0;
    videoHeight.value = video.videoHeight || 0;
    
    if (eventType === 'play') {
      playing.value = true;
      startDurationTimer();
    } else if (eventType === 'pause' || eventType === 'error') {
      playing.value = false;
      stopDurationTimer();
    }
  }
}

// 开始测试
async function startTest() {
  if (!testFlvUrl.value) {
    addLog('error', '请输入FLV视频流URL');
    return;
  }
  
  if (!flvSupported.value) {
    addLog('error', '浏览器不支持FLV播放');
    return;
  }
  
  testing.value = true;
  addLog('info', '开始FLV播放测试');
  addLog('info', `测试URL: ${testFlvUrl.value}`);
  
  try {
    // 清理之前的播放器
    if (flvPlayer) {
      flvPlayer.destroy();
      flvPlayer = null;
    }
    
    // 创建FLV播放器
    addLog('info', '创建FLV播放器');
    flvPlayer = flvjs.createPlayer({
      type: 'flv',
      url: testFlvUrl.value,
      isLive: true,
      hasAudio: true,
      hasVideo: true,
      cors: true,
      withCredentials: false
    }, {
      enableWorker: false,
      enableStashBuffer: true,
      stashInitialSize: 128,
      autoCleanupSourceBuffer: true,
      autoCleanupMaxBackwardDuration: 30,
      autoCleanupMinBackwardDuration: 10,
      fixAudioTimestampGap: true,
      lazyLoad: false,
      liveBufferLatencyChasing: true,
      liveBufferLatencyMaxLatency: 3,
      liveBufferLatencyMinRemain: 0.5
    });
    
    // 添加事件监听
    flvPlayer.on(flvjs.Events.ERROR, (errorType: string, errorDetail: string, errorInfo: any) => {
      addLog('error', `FLV播放错误: ${errorType} - ${errorDetail}`);
      testing.value = false;
      playing.value = false;
    });
    
    flvPlayer.on(flvjs.Events.LOADING_COMPLETE, () => {
      addLog('info', 'FLV加载完成');
    });
    
    flvPlayer.on(flvjs.Events.MEDIA_INFO, (mediaInfo: any) => {
      addLog('info', `FLV媒体信息: ${JSON.stringify(mediaInfo)}`);
    });
    
    // 绑定到视频元素
    if (!videoElement.value) {
      throw new Error('视频元素未找到');
    }
    
    addLog('info', '绑定FLV播放器到视频元素');
    flvPlayer.attachMediaElement(videoElement.value);
    
    // 加载视频
    addLog('info', '开始加载FLV视频流');
    flvPlayer.load();
    
    // 等待一小段时间
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 开始播放
    addLog('info', '开始播放FLV视频');
    await videoElement.value.play();
    
    addLog('info', 'FLV视频播放成功');
    testing.value = false;
    
  } catch (error: any) {
    addLog('error', `FLV播放失败: ${error?.message || error}`);
    testing.value = false;
    playing.value = false;
  }
}

// 停止测试
function stopTest() {
  addLog('info', '停止FLV播放测试');
  
  if (flvPlayer) {
    flvPlayer.destroy();
    flvPlayer = null;
  }
  
  if (videoElement.value) {
    videoElement.value.pause();
    videoElement.value.src = '';
  }
  
  playing.value = false;
  testing.value = false;
  stopDurationTimer();
}

// 开始计时
function startDurationTimer() {
  playStartTime = Date.now();
  durationTimer = setInterval(() => {
    const elapsed = Date.now() - playStartTime;
    const seconds = Math.floor(elapsed / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    playDuration.value = `${hours.toString().padStart(2, '0')}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
  }, 1000);
}

// 停止计时
function stopDurationTimer() {
  if (durationTimer) {
    clearInterval(durationTimer);
    durationTimer = null;
  }
  playDuration.value = '00:00:00';
}
</script>

<style scoped>
.flv-player-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 20px;
}

.test-controls {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 8px;
}

.test-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.video-section, .log-section {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 15px;
}

.video-container {
  text-align: center;
}

.test-video {
  width: 100%;
  max-width: 500px;
  height: auto;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.video-info {
  margin-top: 15px;
  text-align: left;
}

.log-container {
  height: 400px;
  overflow-y: auto;
  background: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 10px;
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  margin-bottom: 5px;
  padding: 2px 5px;
  border-radius: 3px;
}

.log-item.info {
  background: #e6f7ff;
  color: #1890ff;
}

.log-item.error {
  background: #fff2f0;
  color: #ff4d4f;
}

.log-item.warn {
  background: #fffbe6;
  color: #faad14;
}

.log-time {
  font-weight: bold;
  margin-right: 10px;
}

.log-message {
  word-break: break-all;
}
</style>
