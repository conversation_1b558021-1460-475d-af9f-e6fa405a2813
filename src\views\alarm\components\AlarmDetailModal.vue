<template>
  <j-modal
    title="设备报警详情"
    :width="1200"
    v-model:open="open"
    :footer="null"
    @cancel="handleCancel"
    :closable="true"
    :mask-closable="true"
    class="tech-alarm-modal"
    :body-style="{ padding: 0 }"
  >
    <div class="alarm-detail-container">
      <!-- 报警头部信息 -->
      <div class="alarm-header">
        <div class="alarm-status-indicator">
          <div class="status-icon" :class="getStatusClass(info.status)">
            <Icon icon="ant-design:warning-outlined" />
          </div>
          <div class="status-info">
            <h3>{{ info.deviceName || '设备报警' }}</h3>
            <span class="status-text">{{ getStatusText(info.status) }}</span>
          </div>
        </div>
        <div class="alarm-time">
          <Icon icon="ant-design:clock-circle-outlined" />
          <span>{{ info.alarmTime }}</span>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="alarm-content">
        <!-- 基本信息 -->
        <div class="info-section">
          <div class="section-title">
            <Icon icon="ant-design:info-circle-outlined" />
            <span>基本信息</span>
          </div>
          <div class="info-grid">
            <div class="info-item">
              <label>设备编号</label>
              <span>{{ info.deviceNum || '-' }}</span>
            </div>
            <div class="info-item">
              <label>设备名称</label>
              <span>{{ info.deviceName || '-' }}</span>
            </div>
            <div class="info-item">
              <label>所属路线</label>
              <span>{{ info.lineName || '-' }}</span>
            </div>
            <div class="info-item">
              <label>报警时间</label>
              <span>{{ info.alarmTime }}</span>
            </div>
            <div class="info-item">
              <label>处理人</label>
              <span>{{ info.handleUserName || '-' }}</span>
            </div>
            <div class="info-item">
              <label>处理时间</label>
              <span >{{ info.handleTime }}</span>
            </div>
            <div class="info-item full-width">
              <label>处理意见</label>
              <span>{{ info.opinion || '-' }}</span>
            </div>
          </div>
        </div>

        <!-- 监控截图区域 -->
        <div class="screenshot-section">
          <div class="section-title">
            <div class="title-content">
              <Icon icon="ant-design:picture-outlined" />
              <span>监控截图</span>
            </div>
            <div class="count-badge">{{ screenshots.length }}</div>
            <!-- <button class="refresh-btn" @click="refreshScreenshots">
              <Icon icon="ant-design:reload-outlined" />
            </button> -->
          </div>
          <div class="screenshot-content">
            <div class="screenshot-grid" v-if="screenshots.length > 0">
              <div
                v-for="(screenshot, index) in screenshots"
                :key="index"
                class="screenshot-item"
                @click="previewImage(screenshot.screenshotPath)"
              >
                <img :src="getFileAccessHttpUrl(screenshot.screenshotPath)" :alt="screenshot.name" />
                <div class="screenshot-overlay">
                  <Icon icon="ant-design:eye-outlined" />
                </div>
                <div class="screenshot-info">
                  <span class="name">{{ screenshot.videoName }}</span>
                  <span class="time">{{ screenshot.createTime }}</span>
                </div>
              </div>
            </div>
            <div v-else class="empty-state">
              <Icon icon="ant-design:picture-outlined" />
              <p>暂无监控截图</p>
            </div>
          </div>
        </div>

        <!-- 视频监控区域 -->
        <div class="video-section">
          <div class="section-title">
            <div class="title-content">
              <Icon icon="ant-design:video-camera-outlined" />
              <span>视频监控</span>
            </div>
            <div class="count-badge">{{ videoList.length }}</div>
            <!-- <button class="refresh-btn" @click="refreshVideoList">
              <Icon icon="ant-design:reload-outlined" />
            </button> -->
          </div>
          <div class="video-content">
            <div class="video-grid" v-if="videoList.length > 0">
              <div
                v-for="(video, index) in videoList"
                :key="video.id"
                class="video-item"
              >
                <div class="video-header">
                  <h4>{{ video.name }}</h4>
                  <span class="video-status" :class="getVideoStatusClass(video.status)">
                    {{ getVideoStatusText(video.status) }}
                  </span>
                </div>
                <div class="video-player-wrapper">
                  <!-- WebRTC视频播放器 -->
                  <div class="webrtc-player">
                    <video
                      :ref="el => setVideoRef(el, index)"
                      :id="`rtspVideo_${index}`"
                      class="video-player"
                      controls
                      autoplay
                      muted
                      width="100%"
                      height="240px"
                      @play="onVideoPlay(index)"
                      @pause="onVideoPause(index)"
                      @error="onVideoError(index)"
                      @loadstart="onVideoLoadStart(index)"
                      @loadeddata="onVideoLoaded(index)"
                    >
                      您的浏览器不支持WebRTC视频播放
                    </video>

                    <!-- 视频覆盖层 -->
                    <div class="video-overlay" v-if="!video.isPlaying && !video.starting">
                      <div class="play-button" @click="startVideo(index)">
                        <Icon icon="ant-design:play-circle-outlined" />
                        <span class="play-text">点击播放</span>
                      </div>
                    </div>

                    <!-- 加载状态 -->
                    <div v-if="video.starting" class="video-loading">
                      <div class="loading-spinner"></div>
                      <p>正在连接视频流...</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="empty-state">
              <Icon icon="ant-design:video-camera-outlined" />
              <p>暂无视频监控</p>
            </div>
          </div>
        </div>
      </div>
    </div>


  </j-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";
import JModal from "@/components/Modal/src/JModal/JModal.vue";
import { detailUrl } from "../Alarm.api";
import { Icon } from '/@/components/Icon';
import { createImgPreview } from '/@/components/Preview/index';
import { message } from 'ant-design-vue';
import { getFileAccessHttpUrl } from "/@/utils/common/compUtils";


const open = ref<boolean>(false);
const info = ref<any>({});
const screenshots = ref<any[]>([]);
const videoList = ref<any[]>([]);
const videoRefs = ref<any[]>([]);
const webRtcServers = ref<any[]>([]);

const emit = defineEmits(['register', 'success']);

// 获取状态样式类
function getStatusClass(status: string) {
  return status === '0' ? 'status-pending' : status === '1' ? 'status-handled' : 'status-unknown';
}

// 获取状态文本
function getStatusText(status: string) {
  return status === '0' ? '未处理' : status === '1' ? '已处理' : '未知';
}

// 获取视频状态样式类
function getVideoStatusClass(status: string) {
  return status === 'online' ? 'status-online' : status === 'offline' ? 'status-offline' : 'status-connecting';
}

// 获取视频状态文本
function getVideoStatusText(status: string) {
  return status === 'online' ? '在线' : status === 'offline' ? '离线' : '连接中';
}

// 格式化时间
function formatTime(time: string) {
  if (!time) return '-';
  return new Date(time).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

// 显示模态框
function showModal(record: any) {
  open.value = true;
  detail(record.id);
  // loadScreenshots(record.id);
  // loadVideoList(record.id);
}

// 获取详情
function detail(id: string | number) {
  detailUrl({ id: id })
    .then((res: any) => {
      if (res.success) {
        info.value = res.result;
        screenshots.value = res.result.screenshots;
        videoList.value = res.result.lineVideoList;
      } else {
        message.error(res.message || '获取详情失败');
      }
    })
    .catch((error) => {
      console.error('获取详情失败:', error);
      message.error('获取详情失败');
    });
}

// 加载监控截图
function loadScreenshots(alarmId: string | number) {
  // 模拟数据，实际应该调用API
  screenshots.value = [
    {
      id: '1',
      name: '监控点1',
      url: '/api/screenshots/alarm_' + alarmId + '_1.jpg',
      captureTime: new Date().toISOString()
    },
    {
      id: '2', 
      name: '监控点2',
      url: '/api/screenshots/alarm_' + alarmId + '_2.jpg',
      captureTime: new Date().toISOString()
    }
  ];
}

// 加载视频列表
function loadVideoList(alarmId: string | number) {
  // 模拟数据，实际应该调用API
  videoList.value = [
    {
      id: 'video_1',
      name: '主监控点',
      videoUrl: 'rtsp://admin:admin123@192.168.1.100:554/stream1',
      rtspUrl: 'rtsp://admin:admin123@192.168.1.100:554/stream1',
      streamId: 'stream_1',
      status: 'online',
      isPlaying: false,
      starting: false
    },
    {
      id: 'video_2',
      name: '辅助监控点',
      videoUrl: 'rtsp://admin:admin123@192.168.1.101:554/stream1',
      rtspUrl: 'rtsp://admin:admin123@192.168.1.101:554/stream1',
      streamId: 'stream_2',
      status: 'online',
      isPlaying: false,
      starting: false
    }
  ];
}

// 预览图片
function previewImage(url: string) {
  createImgPreview({
    imageList: [getFileAccessHttpUrl(url)],
    index: 0
  });
}

// 刷新截图
function refreshScreenshots() {
  loadScreenshots(info.value.id);
  message.success('截图已刷新');
}

// 刷新视频列表
function refreshVideoList() {
  loadVideoList(info.value.id);
  message.success('视频列表已刷新');
}

// 设置视频引用
function setVideoRef(el: any, index: number) {
  if (el) {
    videoRefs.value[index] = el;
  }
}

// 开始播放视频
async function startVideo(index: number) {
  const video = videoList.value[index];
  if (!video || video.isPlaying) return;

  video.starting = true;

  try {
    // 检查WebRtcStreamer是否可用
    if (typeof (window as any).WebRtcStreamer === 'undefined') {
      throw new Error('WebRtcStreamer未加载，请确保webrtcstreamer.js已正确引入');
    }

    // 创建WebRTC服务器实例
    const WebRtcStreamerClass = (window as any).WebRtcStreamer;
    const videoElement = videoRefs.value[index];

    if (!videoElement) {
      throw new Error('视频元素未找到');
    }

    // 停止之前的连接
    if (webRtcServers.value[index]) {
      webRtcServers.value[index].disconnect();
    }

    // 创建新的WebRTC连接
    webRtcServers.value[index] = new WebRtcStreamerClass(videoElement.id, "http://10.0.122.239:8000");

    // 连接到视频流
    webRtcServers.value[index].connect(video.rtspUrl || video.videoUrl);

    video.isPlaying = true;
    video.starting = false;

    console.log('WebRTC连接成功，流地址:', video.rtspUrl || video.videoUrl);

  } catch (error: any) {
    console.error('WebRTC播放失败:', error);
    message.error('视频播放失败: ' + (error?.message || error));
    video.starting = false;
  }
}

// 视频播放事件
function onVideoPlay(index: number) {
  const video = videoList.value[index];
  if (video) {
    video.isPlaying = true;
    video.starting = false;
  }
}

// 视频暂停事件
function onVideoPause(index: number) {
  const video = videoList.value[index];
  if (video) {
    video.isPlaying = false;
  }
}

// 视频错误事件
function onVideoError(index: number) {
  const video = videoList.value[index];
  if (video) {
    video.isPlaying = false;
    video.starting = false;
  }
  message.error(`视频${index + 1}播放失败`);
}

// 视频加载开始
function onVideoLoadStart(index: number) {
  const video = videoList.value[index];
  if (video) {
    video.starting = true;
  }
}

// 视频加载完成
function onVideoLoaded(index: number) {
  const video = videoList.value[index];
  if (video) {
    video.starting = false;
  }
}

// 关闭模态框
function handleCancel() {
  open.value = false;
  info.value = {};
  screenshots.value = [];

  // 停止所有视频播放
  videoList.value.forEach((video, index) => {
    if (webRtcServers.value[index]) {
      try {
        webRtcServers.value[index].disconnect();
      } catch (error) {
        console.error('断开WebRTC连接失败:', error);
      }
    }
    video.isPlaying = false;
    video.starting = false;
  });

  videoList.value = [];
  videoRefs.value = [];
  webRtcServers.value = [];
}

// 暴露方法
defineExpose({
  showModal
});
</script>

<style lang="less" scoped>
// 科技风格模态框
.tech-alarm-modal {
  :deep(.ant-modal-content) {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    border: 1px solid rgba(59, 130, 246, 0.15);
    box-shadow:
      0 25px 50px rgba(59, 130, 246, 0.1),
      0 10px 30px rgba(6, 182, 212, 0.05);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    overflow: hidden;
  }

  :deep(.ant-modal-header) {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px 16px 0 0;
  }

  :deep(.ant-modal-body) {
    padding: 0;
  }
}

.alarm-detail-container {
  color: #1f2937;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

// 报警头部
.alarm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  position: relative;

  .alarm-status-indicator {
    display: flex;
    align-items: center;
    gap: 16px;

    .status-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      position: relative;

      &.status-pending {
        background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);
        color: #d97706;
        box-shadow: 0 4px 12px rgba(251, 191, 36, 0.3);
      }

      &.status-handled {
        background: linear-gradient(135deg, #d1fae5 0%, #10b981 100%);
        color: #047857;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
      }

      &.status-unknown {
        background: linear-gradient(135deg, #f3f4f6 0%, #9ca3af 100%);
        color: #6b7280;
        box-shadow: 0 4px 12px rgba(156, 163, 175, 0.3);
      }
    }

    .status-info {
      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
        line-height: 1.2;
      }

      .status-text {
        font-size: 14px;
        color: #6b7280;
        font-weight: 500;
      }
    }
  }

  .alarm-time {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 8px;
    color: #3b82f6;
    font-size: 14px;
    font-weight: 500;
  }
}

// 内容区域
.alarm-content {
  padding: 24px 32px 32px;
}

// 信息部分
.info-section {
  margin-bottom: 32px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 50%,
    rgba(255, 255, 255, 0.95) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  overflow: hidden;
  backdrop-filter: blur(20px);
  box-shadow:
    0 8px 32px rgba(59, 130, 246, 0.08),
    inset 0 1px 0 rgba(59, 130, 246, 0.05);
  position: relative;

  // 科技感光效
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(59, 130, 246, 0.5) 50%,
      transparent 100%);
    animation: pulse-glow 3s ease-in-out infinite;
  }

  // 数据流动效果
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(99, 102, 241, 0.6) 50%,
      transparent 100%);
    animation: data-flow 4s linear infinite;
    animation-delay: 1s;
  }

  .section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 18px 24px;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(248, 250, 252, 0.8) 100%);
    border-bottom: 1px solid rgba(59, 130, 246, 0.15);
    font-size: 15px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0;
    position: relative;

    .anticon {
      color: #3b82f6;
      font-size: 16px;
    }

    span {
      color: #374151;
      letter-spacing: 0.5px;
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1px;
    padding: 0;
    background: rgba(59, 130, 246, 0.08);

    .info-item {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 20px 24px;
      background: rgba(255, 255, 255, 0.9);
      transition: all 0.3s ease;
      position: relative;

      &:hover {
        background: rgba(59, 130, 246, 0.05);

        &::before {
          opacity: 1;
        }
      }

      // 科技感光效线条
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 2px;
        background: linear-gradient(180deg,
          transparent 0%,
          rgba(59, 130, 246, 0.8) 50%,
          transparent 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &.full-width {
        grid-column: 1 / -1;
        border-top: 1px solid rgba(59, 130, 246, 0.15);
        background: rgba(248, 250, 252, 0.8);

        &::before {
          display: none;
        }
      }

      label {
        font-size: 11px;
        font-weight: 600;
        color: #6b7280;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin: 0;
        opacity: 0.9;
      }

      span {
        font-size: 15px;
        font-weight: 500;
        color: #1f2937;
        line-height: 1.4;

        &.time-value {
          color: #3b82f6;
          font-family: 'JetBrains Mono', 'SF Mono', Monaco, monospace;
          font-size: 14px;
          font-weight: 500;
          letter-spacing: 0.5px;
        }
      }
    }
  }
}

// 截图和视频部分
.screenshot-section,
.video-section {
  margin-bottom: 32px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(59, 130, 246, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  }

  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);

    .title-content {
      display: flex;
      align-items: center;
      gap: 8px;
      text-align: left;

      .anticon {
        color: #3b82f6;
        font-size: 18px;
      }

      span {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }
    }

    .count-badge {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      color: white;
      font-size: 12px;
      font-weight: 600;
      padding: 2px 8px;
      border-radius: 12px;
      min-width: 20px;
      text-align: center;
      margin-left: 8px;
    }

    .refresh-btn {
      background: none;
      border: none;
      color: #6b7280;
      cursor: pointer;
      padding: 8px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(59, 130, 246, 0.1);
        color: #3b82f6;
      }

      .anticon {
        font-size: 16px;
      }
    }
  }

  .screenshot-content,
  .video-content {
    padding: 24px;
  }
}

// 截图网格
.screenshot-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 16px;

  .screenshot-item {
    position: relative;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(59, 130, 246, 0.1);
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(59, 130, 246, 0.3);
      transform: translateY(-4px);
      box-shadow: 0 8px 20px rgba(59, 130, 246, 0.2);

      .screenshot-overlay {
        opacity: 1;
      }
    }

    img {
      width: 100%;
      height: 120px;
      object-fit: cover;
    }

    .screenshot-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 50px;
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(99, 102, 241, 0.8) 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: all 0.3s ease;

      .anticon {
        font-size: 28px;
        color: white;
      }
    }

    .screenshot-info {
      padding: 12px;
      background: rgba(255, 255, 255, 0.95);

      .name {
        display: block;
        font-size: 13px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 4px;
      }

      .time {
        font-size: 11px;
        color: #6b7280;
        font-family: 'JetBrains Mono', 'SF Mono', Monaco, monospace;
      }
    }
  }
}

// 视频网格
.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 500px));
  gap: 20px;
  justify-content: start;

  .video-item {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(59, 130, 246, 0.1);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    width: 100%;
    max-width: 500px;

    &:hover {
      border-color: rgba(59, 130, 246, 0.3);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    }

    .video-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: rgba(248, 250, 252, 0.8);
      border-bottom: 1px solid rgba(59, 130, 246, 0.1);

      h4 {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
        color: #1f2937;
      }

      .video-status {
        font-size: 12px;
        font-weight: 500;
        padding: 2px 8px;
        border-radius: 6px;

        &.status-online {
          background: rgba(16, 185, 129, 0.2);
          color: #047857;
        }

        &.status-offline {
          background: rgba(239, 68, 68, 0.2);
          color: #dc2626;
        }

        &.status-connecting {
          background: rgba(245, 158, 11, 0.2);
          color: #d97706;
        }
      }
    }

    .video-player-wrapper {
      position: relative;
      height: 240px;
      background: #000;

      .webrtc-player {
        position: relative;
        width: 100%;
        height: 100%;

        .video-player {
          width: 100%;
          height: 100%;
          background: #000;
        }

        .video-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.7);
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(0, 0, 0, 0.8);
          }

          .play-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            color: white;

            .anticon {
              font-size: 48px;
              transition: all 0.3s ease;
            }

            .play-text {
              font-size: 14px;
              font-weight: 500;
            }

            &:hover .anticon {
              transform: scale(1.1);
            }
          }
        }

        .video-loading {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background: rgba(0, 0, 0, 0.8);
          color: white;

          .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
          }

          p {
            margin: 0;
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #9ca3af;

  .anticon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
  }

  p {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 科技感脉冲动画
@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.4;
    transform: scaleX(1);
  }
  50% {
    opacity: 0.8;
    transform: scaleX(1.02);
  }
}

// 数据流动画
@keyframes data-flow {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .info-section .info-grid {
    grid-template-columns: 1fr;
    gap: 1px;
  }

  .video-grid {
    grid-template-columns: repeat(auto-fit, minmax(400px, 450px));
    justify-content: center;

    .video-item {
      max-width: 450px;
    }
  }
}

@media (max-width: 900px) {
  .info-section .info-grid {
    grid-template-columns: 1fr;
    gap: 1px;
  }

  .video-grid {
    grid-template-columns: 1fr;
    justify-content: center;

    .video-item {
      max-width: 500px;
      margin: 0 auto;
    }
  }
}

@media (max-width: 768px) {
  .alarm-header {
    padding: 16px 20px;
    flex-direction: column;
    gap: 16px;
    text-align: center;

    .alarm-status-indicator {
      justify-content: center;
    }
  }

  .alarm-content {
    padding: 16px 20px 24px;
  }

  .screenshot-grid {
    grid-template-columns: 1fr;
  }

  .screenshot-section,
  .video-section {
    .section-title {
      padding: 16px 20px;
    }

    .screenshot-content,
    .video-content {
      padding: 16px;
    }
  }

  .video-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    justify-content: center;

    .video-item {
      max-width: 100%;
      margin: 0 auto;

      .video-player-wrapper {
        height: 200px;
      }
    }
  }
}
</style>
