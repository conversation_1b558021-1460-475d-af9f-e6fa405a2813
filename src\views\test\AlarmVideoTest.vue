<template>
  <div class="alarm-video-test">
    <div class="test-header">
      <h2>报警视频监控模态框测试</h2>
      <a-button type="primary" @click="showAlarmModal">
        显示报警视频监控
      </a-button>
    </div>

    <div class="test-content">
      <h3>测试说明</h3>
      <ul>
        <li>已删除"相关监控视频"标题</li>
        <li>已删除1x1、2x2网格选择按钮</li>
        <li>统一使用2x2网格布局，同时显示4个视频框</li>
        <li>优化了视频组件大小，框体更小更紧凑</li>
        <li>保留视频播放器控制按钮，便于操作</li>
        <li>模态框宽度1400px，高度适应4个视频框</li>
        <li>删除处理意见表单，默认为"已确认处理"</li>
      </ul>
    </div>

    <!-- 报警视频监控模态框 -->
    <AlarmVideoMonitorModal
      v-model:open="alarmModalVisible"
      :alarm-data="mockAlarmData"
      @confirm="handleAlarmConfirm"
      @close="handleAlarmClose"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import AlarmVideoMonitorModal from '/@/components/AlarmVideoMonitorModal/index.vue';

// 响应式数据
const alarmModalVisible = ref(false);

// 模拟报警数据
const mockAlarmData = ref({
  id: 'alarm_001', // 添加报警ID用于API调用
  alarmId: 'alarm_001', // 备用ID字段
  msgTxt: '监控区域检测到异常活动，请立即查看相关视频监控',
  msgDesc: '系统自动检测到可疑行为',
  timestamp: new Date(),
  location: '监控区域A-01',
  deviceId: 'CAM-001',
  videoList: [
    {
      id: 'video-1',
      name: '监控摄像头-01',
      videoUrl: 'http://example.com/video1.mp4',
      streamId: 'stream-001',
      websocketUrl: 'ws://example.com/stream1',
      cameraIndexCode: 'CAM-001',
      streamType: 'preview',
      rtspUrl: 'rtsp://example.com/stream1'
    },
    {
      id: 'video-2',
      name: '监控摄像头-02',
      videoUrl: 'http://example.com/video2.mp4',
      streamId: 'stream-002',
      websocketUrl: 'ws://example.com/stream2',
      cameraIndexCode: 'CAM-002',
      streamType: 'preview',
      rtspUrl: 'rtsp://example.com/stream2'
    },
    {
      id: 'video-3',
      name: '监控摄像头-03',
      videoUrl: 'http://example.com/video3.mp4',
      streamId: 'stream-003',
      websocketUrl: 'ws://example.com/stream3',
      cameraIndexCode: 'CAM-003',
      streamType: 'playback',
      rtspUrl: 'rtsp://example.com/stream3'
    },
    {
      id: 'video-4',
      name: '监控摄像头-04',
      videoUrl: 'http://example.com/video4.mp4',
      streamId: 'stream-004',
      websocketUrl: 'ws://example.com/stream4',
      cameraIndexCode: 'CAM-004',
      streamType: 'preview',
      rtspUrl: 'rtsp://example.com/stream4'
    }
  ]
});

// 显示报警模态框
const showAlarmModal = () => {
  alarmModalVisible.value = true;
};

// 处理报警确认
const handleAlarmConfirm = (alarmData: any) => {
  console.log('报警已确认处理:', alarmData);
  alarmModalVisible.value = false;
};

// 处理报警关闭
const handleAlarmClose = () => {
  console.log('报警模态框已关闭');
  alarmModalVisible.value = false;
};
</script>

<style lang="less" scoped>
.alarm-video-test {
  padding: 24px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  min-height: 100vh;
  color: #ffffff;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(0, 212, 255, 0.3);

  h2 {
    margin: 0;
    color: #00d4ff;
    font-size: 24px;
    font-weight: 600;
  }
}

.test-content {
  padding: 24px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(0, 212, 255, 0.3);

  h3 {
    color: #00d4ff;
    margin-bottom: 16px;
    font-size: 18px;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      padding: 8px 0;
      padding-left: 20px;
      position: relative;
      color: rgba(255, 255, 255, 0.9);
      font-size: 14px;

      &:before {
        content: '✓';
        position: absolute;
        left: 0;
        color: #00d4ff;
        font-weight: bold;
      }
    }
  }
}
</style>
