<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol" name="CardVideoForm">
          <a-row>
						<a-col :span="24">
							<a-form-item label="监控名称" v-bind="validateInfos.name" id="CardVideoForm-name" name="name">
								<a-input v-model:value="formData.name" placeholder="请输入监控名称"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="海康监控唯一标识" v-bind="validateInfos.cameraIndexCode" id="CardVideoForm-cameraIndexCode" name="cameraIndexCode">
								<a-input v-model:value="formData.cameraIndexCode" placeholder="请输入海康监控唯一标识"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="是否主监控" v-bind="validateInfos.isMain" id="CardVideoForm-isMain" name="isMain">
								<a-radio-group v-model:value="formData.isMain">
									<a-radio value="0">否</a-radio>
									<a-radio value="1">是</a-radio>
								</a-radio-group>
								<div class="form-tip" style="margin-top: 8px;">
									<a-icon type="info-circle" style="color: #1890ff; margin-right: 4px;" />
									<span style="color: #666; font-size: 12px;">提示：选择"是"后，在SOS报警时会将该监控展示在报警监控视频页面</span>
								</div>
							</a-form-item>
						</a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate } from '../CardVideo.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({})},
    formBpm: { type: Boolean, default: true }
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    cardId: '',
    name: '',
    cameraIndexCode: '',
    isMain: "0",
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({
    name: [{ required: true, message: '请输入监控名称!'},],
    cameraIndexCode: [{ required: true, message: '请输入海康监控唯一标识!'},],
    isMain: [{ required: true, message: '请选择是否主监控!'},],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(()=>{
    if(props.formBpm === true){
      if(props.formData.disabled === false){
        return false;
      }else{
        return true;
      }
    }
    return props.formDisabled;
  });

  
  /**
   * 新增
   */
  function add(record) {
    edit(record || {});
  }

  /**
   * 编辑
   */
  function edit(record) {
   
    nextTick(() => {
      resetFields();
      const tmpData: Record<string, any> = {};
      Object.keys(formData).forEach((key) => {
        if(record.hasOwnProperty(key)){
          tmpData[key] = record[key]
        }
      })

      // 确保 isMain 字段有正确的默认值
      if (!tmpData.hasOwnProperty('isMain')) {
        tmpData.isMain = "0";
      }

      //赋值
      Object.assign(formData, tmpData);
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    try {
      // 触发表单验证
      await validate();
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    }
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }


  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>

<style lang="less">
  /* 提高消息弹窗的 z-index，确保在模态框上方显示 */
  .ant-message {
    z-index: 1300 !important;
  }

  .ant-message .ant-message-notice {
    z-index: 1300 !important;
  }
</style>
