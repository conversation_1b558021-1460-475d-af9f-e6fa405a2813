<template>
  <div class="flv-video-test">
    <div class="test-header">
      <h2>FLV 视频播放测试</h2>
      <p>测试不同格式的视频播放功能</p>
    </div>

    <div class="test-controls">
      <a-space>
        <a-select v-model:value="selectedFormat" style="width: 120px" @change="onFormatChange">
          <a-select-option value="flv">FLV 格式</a-select-option>
          <a-select-option value="hls">HLS 格式</a-select-option>
          <a-select-option value="mp4">MP4 格式</a-select-option>
        </a-select>
        
        <a-input 
          v-model:value="customUrl" 
          placeholder="输入自定义视频URL" 
          style="width: 400px"
          @pressEnter="loadCustomVideo"
        />
        
        <a-button type="primary" @click="loadCustomVideo">
          加载视频
        </a-button>
        
        <a-button @click="loadTestVideo">
          加载测试视频
        </a-button>
      </a-space>
    </div>

    <div class="test-info">
      <a-descriptions title="当前视频信息" :column="2" size="small">
        <a-descriptions-item label="格式">{{ currentVideoInfo.format }}</a-descriptions-item>
        <a-descriptions-item label="URL">{{ currentVideoInfo.url }}</a-descriptions-item>
        <a-descriptions-item label="状态">{{ currentVideoInfo.status }}</a-descriptions-item>
        <a-descriptions-item label="支持情况">{{ currentVideoInfo.support }}</a-descriptions-item>
      </a-descriptions>
    </div>

    <div class="video-container">
      <VideoMonitorPlayerModal
        v-if="videoInfo"
        ref="videoPlayerRef"
        :plan-id="'test-plan'"
        :video-info="videoInfo"
        :auto-start="false"
        :show-controls="true"
        @error="onVideoError"
      />
      <div v-else class="no-video">
        <a-empty description="请选择或输入视频URL进行测试" />
      </div>
    </div>

    <div class="test-logs">
      <h3>测试日志</h3>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <a-button @click="clearLogs" size="small">清空日志</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import VideoMonitorPlayerModal from './VideoMonitorPlayerModal.vue';

// 响应式数据
const selectedFormat = ref('flv');
const customUrl = ref('');
const videoPlayerRef = ref();
const logs = ref<Array<{time: string, message: string, type: string}>>([]);

// 测试视频URL
const testUrls = {
  flv: 'http://example.com/test.flv',
  hls: 'http://example.com/test.m3u8', 
  mp4: 'http://example.com/test.mp4'
};

// 当前视频信息
const videoInfo = ref<any>(null);
const currentVideoInfo = reactive({
  format: '',
  url: '',
  status: '未加载',
  support: ''
});

// 添加日志
function addLog(message: string, type: string = 'info') {
  const now = new Date();
  const time = now.toLocaleTimeString();
  logs.value.unshift({ time, message, type });
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50);
  }
}

// 清空日志
function clearLogs() {
  logs.value = [];
}

// 格式变化处理
function onFormatChange() {
  customUrl.value = testUrls[selectedFormat.value] || '';
  addLog(`切换到 ${selectedFormat.value.toUpperCase()} 格式`, 'info');
}

// 加载自定义视频
function loadCustomVideo() {
  if (!customUrl.value.trim()) {
    addLog('请输入视频URL', 'error');
    return;
  }

  const url = customUrl.value.trim();
  const format = detectVideoFormat(url);
  
  videoInfo.value = {
    id: `test-video-${Date.now()}`,
    name: `测试视频 - ${format.toUpperCase()}`,
    videoUrl: url,
    streamId: `test-stream-${Date.now()}`,
    websocketUrl: `/websocket/video/test-stream-${Date.now()}`,
    hlsUrl: format === 'hls' ? url : `/hls/test-stream-${Date.now()}/index.m3u8`,
    streamType: 'preview'
  };

  // 更新当前视频信息
  currentVideoInfo.format = format;
  currentVideoInfo.url = url;
  currentVideoInfo.status = '加载中';
  currentVideoInfo.support = checkFormatSupport(format);

  addLog(`开始加载 ${format.toUpperCase()} 视频: ${url}`, 'info');
}

// 加载测试视频
function loadTestVideo() {
  customUrl.value = testUrls[selectedFormat.value] || '';
  loadCustomVideo();
}

// 检测视频格式
function detectVideoFormat(url: string): string {
  const lowerUrl = url.toLowerCase();
  if (lowerUrl.includes('.flv') || lowerUrl.includes('flv')) return 'flv';
  if (lowerUrl.includes('.m3u8') || lowerUrl.includes('hls')) return 'hls';
  if (lowerUrl.includes('.mp4')) return 'mp4';
  if (lowerUrl.includes('rtmp://')) return 'rtmp';
  if (lowerUrl.includes('rtsp://')) return 'rtsp';
  return 'unknown';
}

// 检查格式支持情况
function checkFormatSupport(format: string): string {
  switch (format) {
    case 'flv':
      return 'FLV.js 支持';
    case 'hls':
      return 'HLS.js 支持';
    case 'mp4':
      return '原生支持';
    case 'rtmp':
    case 'rtsp':
      return '需要转换';
    default:
      return '未知';
  }
}

// 视频错误处理
function onVideoError(error: any) {
  addLog(`视频播放错误: ${error}`, 'error');
  currentVideoInfo.status = '播放失败';
}

// 初始化
onFormatChange();
addLog('FLV 视频测试组件已加载', 'info');
</script>

<style scoped>
.flv-video-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 20px;
}

.test-header h2 {
  color: #1890ff;
  margin-bottom: 8px;
}

.test-controls {
  margin-bottom: 20px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 8px;
}

.test-info {
  margin-bottom: 20px;
}

.video-container {
  margin-bottom: 20px;
  min-height: 400px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
}

.no-video {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.test-logs {
  margin-top: 20px;
}

.test-logs h3 {
  margin-bottom: 12px;
  color: #333;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 8px;
  background: #fff;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  margin-bottom: 8px;
}

.log-item {
  padding: 2px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #666;
  margin-right: 8px;
}

.log-item.info .log-message {
  color: #1890ff;
}

.log-item.error .log-message {
  color: #ff4d4f;
}

.log-item.success .log-message {
  color: #52c41a;
}

.log-item.warning .log-message {
  color: #faad14;
}
</style>
