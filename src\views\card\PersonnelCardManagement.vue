<template>
  <div class="personnel-card-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <Icon icon="ant-design:team-outlined" class="title-icon" />
          人员卡管理
        </h1>
        <p class="page-subtitle">管理当前部门巡更路线下的人员卡信息</p>
      </div>
      <div class="header-actions">
        <a-button type="primary" @click="refreshData" :loading="loading">
          <Icon icon="ant-design:reload-outlined" />
          刷新数据
        </a-button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon total">
            <Icon icon="ant-design:credit-card-outlined" />
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ totalCards }}</div>
            <div class="stat-label">总卡片数</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon bound">
            <Icon icon="ant-design:user-outlined" />
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ boundCards }}</div>
            <div class="stat-label">已绑定</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon unbound">
            <Icon icon="ant-design:user-delete-outlined" />
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ unboundCards }}</div>
            <div class="stat-label">未绑定</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 卡片列表 -->
    <div class="cards-section">
      <a-spin :spinning="loading">
        <div v-if="personnelCards.length === 0 && !loading" class="empty-state">
          <Icon icon="ant-design:inbox-outlined" class="empty-icon" />
          <p class="empty-text">暂无人员卡数据</p>
          <p class="empty-desc">当前部门没有管理的路线或路线下没有人员卡</p>
        </div>
        
        <div v-else class="cards-grid">
          <div 
            v-for="card in personnelCards" 
            :key="card.id" 
            class="personnel-card"
            :class="{ 'bound': card.patrolUserId, 'unbound': !card.patrolUserId }"
          >
            <!-- 卡片头部 -->
            <div class="card-header">
              <div class="card-title">
                <Icon icon="ant-design:credit-card-outlined" class="card-icon" />
                <span class="card-name">{{ card.name }}</span>
              </div>
              <div class="card-status">
                <a-tag :color="card.patrolUserId ? 'success' : 'warning'">
                  {{ card.patrolUserId ? '已绑定' : '未绑定' }}
                </a-tag>
              </div>
            </div>

            <!-- 卡片内容 -->
            <div class="card-content">
              <div class="card-info">
                <div class="info-item">
                  <span class="info-label">卡号:</span>
                  <span class="info-value">{{ card.num }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">路线:</span>
                  <span class="info-value">{{ card.lineName }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">类型:</span>
                  <span class="info-value">{{ card.typeName }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">状态:</span>
                  <span class="info-value">{{ card.statusName }}</span>
                </div>
              </div>

              <!-- 绑定人员信息 -->
              <div class="binding-section">
                <div v-if="card.patrolUserId" class="bound-user">
                  <div class="user-avatar">
                    <img 
                      v-if="card.patrolUserImage" 
                      :src="getFileAccessHttpUrl(card.patrolUserImage)" 
                      :alt="card.patrolUserName"
                      @error="handleImageError"
                    />
                    <Icon v-else icon="ant-design:user-outlined" />
                  </div>
                  <div class="user-info">
                    <div class="user-name">{{ card.patrolUserName }}</div>
                    <div class="user-label">绑定人员</div>
                  </div>
                </div>
                <div v-else class="unbound-placeholder">
                  <Icon icon="ant-design:user-add-outlined" class="placeholder-icon" />
                  <span class="placeholder-text">未绑定人员</span>
                </div>
              </div>
            </div>

            <!-- 卡片操作 -->
            <div class="card-actions">
              <a-button 
                type="primary" 
                size="small" 
                @click="openBindingModal(card)"
                :loading="card.updating"
              >
                <Icon icon="ant-design:edit-outlined" />
                {{ card.patrolUserId ? '更换绑定' : '绑定人员' }}
              </a-button>
              <a-button 
                v-if="card.patrolUserId"
                type="default" 
                size="small" 
                @click="unbindUser(card)"
                :loading="card.updating"
               
              >
                <Icon icon="ant-design:disconnect-outlined" />
                解除绑定
              </a-button>
            </div>
          </div>
        </div>
      </a-spin>
    </div>

    <!-- 绑定人员弹窗 -->
    <PersonnelBindingModal 
      ref="bindingModalRef"
      @success="handleBindingSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { message } from 'ant-design-vue';
import { Icon } from '/@/components/Icon';
import { getPersonnelCards, updateCardBinding } from './PersonnelCard.api';
import PersonnelBindingModal from './components/PersonnelBindingModal.vue';
import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';

// 响应式数据
const loading = ref(false);
const personnelCards = ref([]);
const bindingModalRef = ref();

// 计算属性
const totalCards = computed(() => personnelCards.value.length);
const boundCards = computed(() => personnelCards.value.filter(card => card.patrolUserId).length);
const unboundCards = computed(() => personnelCards.value.filter(card => !card.patrolUserId).length);

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;
    const result = await getPersonnelCards();
    if (result.success) {
      personnelCards.value = result.result || [];
    } else {
      message.error(result.message || '获取人员卡数据失败');
    }
  } catch (error) {
    console.error('加载数据失败:', error);
    message.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// 刷新数据
const refreshData = () => {
  loadData();
};

// 打开绑定弹窗
const openBindingModal = (card) => {
  bindingModalRef.value.open(card);
};

// 解除绑定
const unbindUser = async (card) => {
  try {
    card.updating = true;
    const result = await updateCardBinding(card.id, '');
    if (result.success) {
      message.success('解除绑定成功');
      card.patrolUserId = null;
      card.patrolUserName = '';
      card.patrolUserImage = '';
    } else {
      message.error(result.message || '解除绑定失败');
    }
  } catch (error) {
    console.error('解除绑定失败:', error);
    message.error('解除绑定失败');
  } finally {
    card.updating = false;
  }
};

// 绑定成功回调
const handleBindingSuccess = (cardId, patrolUser) => {
  const card = personnelCards.value.find(c => c.id === cardId);
  if (card) {
    card.patrolUserId = patrolUser.id;
    card.patrolUserName = patrolUser.name;
    card.patrolUserImage = patrolUser.image;
  }
};

// 图片加载错误处理
const handleImageError = (event) => {
  event.target.style.display = 'none';
};

// 组件挂载
onMounted(() => {
  loadData();
});
</script>

<style lang="less" scoped>
.personnel-card-container {
  padding: 24px;
  background: #ffffff !important;
  min-height: 100vh;
  position: relative;
  color: #1f2937 !important;

  // 页面头部
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    padding: 24px;
    background: #ffffff;
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      .page-title {
        display: flex;
        align-items: center;
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 600;
        color: #1f2937 !important;

        .title-icon {
          margin-right: 12px;
          color: #3b82f6 !important;
        }
      }

      .page-subtitle {
        margin: 0;
        color: #6b7280 !important;
        font-size: 14px;
      }
    }

    .header-actions {
      .ant-btn {
        height: 40px;
        border-radius: 8px;
        font-weight: 500;
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        border: none;
        color: #ffffff;

        &:hover {
          background: linear-gradient(135deg, #2563eb, #1e40af);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }
      }
    }
  }

  // 统计信息
  .stats-section {
    margin-bottom: 32px;

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;

      .stat-card {
        display: flex;
        align-items: center;
        padding: 24px;
        background: #ffffff;
        border: 1px solid rgba(59, 130, 246, 0.2);
        border-radius: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(59, 130, 246, 0.2);
          border-color: rgba(59, 130, 246, 0.4);
        }

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          font-size: 24px;

          &.total {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
          }

          &.bound {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
          }

          &.unbound {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
          }
        }

        .stat-content {
          .stat-number {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937 !important;
            line-height: 1;
          }

          .stat-label {
            font-size: 14px;
            color: #6b7280 !important;
            margin-top: 4px;
          }
        }
      }
    }
  }

  // 卡片列表
  .cards-section {

    .empty-state {
      text-align: center;
      padding: 80px 20px;
      background: #ffffff;
      border: 1px solid rgba(59, 130, 246, 0.2);
      border-radius: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .empty-icon {
        font-size: 64px;
        color: #d1d5db !important;
        margin-bottom: 16px;
      }

      .empty-text {
        font-size: 18px;
        color: #1f2937 !important;
        margin: 0 0 8px 0;
      }

      .empty-desc {
        font-size: 14px;
        color: #6b7280 !important;
        margin: 0;
      }
    }

    .cards-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
      gap: 24px;

      .personnel-card {
        width: 100%; // 自适应宽度
        background: #ffffff;
        border: 1px solid rgba(59, 130, 246, 0.2);
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border-left: 4px solid transparent;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 4px 16px rgba(59, 130, 246, 0.2);
          border-color: rgba(59, 130, 246, 0.4);
        }

        &.bound {
          border-left-color: #10b981;
        }

        &.unbound {
          border-left-color: #f59e0b;
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;

          .card-title {
            display: flex;
            align-items: center;

            .card-icon {
              margin-right: 8px;
              color: #3b82f6 !important;
              font-size: 18px;
            }

            .card-name {
              font-size: 18px;
              font-weight: 600;
              color: #1f2937 !important;
            }
          }
        }

        .card-content {
          .card-info {
            margin-bottom: 20px;

            .info-item {
              display: flex;
              justify-content: space-between;
              margin-bottom: 8px;
              padding: 8px 0;
              border-bottom: 1px solid rgba(59, 130, 246, 0.1);

              &:last-child {
                border-bottom: none;
                margin-bottom: 0;
              }

              .info-label {
                color: #6b7280 !important;
                font-size: 14px;
              }

              .info-value {
                color: #1f2937 !important;
                font-weight: 500;
                font-size: 14px;
              }
            }
          }

          .binding-section {
            margin-bottom: 20px;
            padding: 16px;
            background: #f9fafb;
            border: 1px solid rgba(59, 130, 246, 0.1);
            border-radius: 12px;

            .bound-user {
              display: flex;
              align-items: center;

              .user-avatar {
                width: 48px;
                height: 48px;
                border-radius: 50%;
                background: rgba(59, 130, 246, 0.2);
                border: 2px solid rgba(59, 130, 246, 0.3);
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 12px;
                overflow: hidden;

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }

                .anticon {
                  font-size: 24px;
                  color: #3b82f6 !important;
                }
              }

              .user-info {
                .user-name {
                  font-size: 16px;
                  font-weight: 600;
                  color: #1f2937 !important;
                  margin-bottom: 4px;
                }

                .user-label {
                  font-size: 12px;
                  color: #10b981;
                  background: rgba(16, 185, 129, 0.2);
                  border: 1px solid rgba(16, 185, 129, 0.3);
                  padding: 2px 8px;
                  border-radius: 4px;
                  display: inline-block;
                }
              }
            }

            .unbound-placeholder {
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 16px;
              color: #9ca3af !important;

              .placeholder-icon {
                margin-right: 8px;
                font-size: 20px;
                color: #9ca3af !important;
              }

              .placeholder-text {
                font-size: 14px;
                color: #9ca3af !important;
              }
            }
          }

          .card-actions {
            display: flex !important;
            gap: 16px !important; // 增加按钮间距
            justify-content: center !important; // 按钮居中显示
            align-items: center !important;
            margin-top: 8px !important; // 增加顶部间距
            padding: 0 !important;

            .ant-btn {
              min-width: 120px !important; // 设置最小宽度，保持按钮大小一致
              height: 36px !important;
              border-radius: 8px !important;
              font-weight: 500 !important;
              display: flex !important;
              align-items: center !important;
              justify-content: center !important;
              transition: all 0.3s ease !important;
              flex: none !important; // 防止按钮被拉伸

              .anticon {
                margin-right: 4px !important;
              }

              // 主要按钮样式
              &.ant-btn-primary {
                background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
                border: none !important;
                color: #ffffff !important;
                box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3) !important;

                &:hover {
                  background: linear-gradient(135deg, #2563eb, #1e40af) !important;
                  transform: translateY(-1px) !important;
                  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;
                }
              }

              // 默认按钮样式
              &.ant-btn-default {
                background: rgba(239, 68, 68, 0.1) !important;
                border: 1px solid rgba(239, 68, 68, 0.3) !important;
                color: #ef4444 !important;

                &:hover {
                  background: rgba(239, 68, 68, 0.2) !important;
                  border-color: rgba(239, 68, 68, 0.5) !important;
                  transform: translateY(-1px) !important;
                  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3) !important;
                }
              }
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .stats-grid {
      grid-template-columns: 1fr;
    }

    .cards-grid {
      grid-template-columns: 1fr;

      .personnel-card {
        width: 100%;
      }
    }
  }

  // 中等屏幕适配
  @media (max-width: 1200px) and (min-width: 769px) {
    .cards-grid {
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    }
  }

  // 大屏幕优化
  @media (min-width: 1400px) {
    .cards-grid {
      grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    }
  }
}

// 全局样式覆盖
:deep(.ant-tag) {
  border-radius: 6px;
  font-weight: 500;

  &.ant-tag-success {
    background: rgba(16, 185, 129, 0.2);
    border-color: rgba(16, 185, 129, 0.4);
    color: #10b981;
  }

  &.ant-tag-warning {
    background: rgba(245, 158, 11, 0.2);
    border-color: rgba(245, 158, 11, 0.4);
    color: #f59e0b;
  }
}

// 卡片操作按钮样式强制覆盖
:deep(.personnel-card .card-actions) {
  display: flex !important;
  gap: 16px !important;
  justify-content: center !important;
  align-items: center !important;
  margin-top: 8px !important;
  padding: 0 !important;

  .ant-btn {
    min-width: 120px !important;
    height: 36px !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
    flex: none !important;

    &.ant-btn-primary {
      background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
      border: none !important;
      color: #ffffff !important;
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3) !important;

      .anticon {
        color: #ffffff !important;
      }

      &:hover, &:focus {
        background: linear-gradient(135deg, #2563eb, #1e40af) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;

        .anticon {
          color: #ffffff !important;
        }
      }
    }

    &.ant-btn-default {
      background: rgba(239, 68, 68, 0.1) !important;
      border: 1px solid rgba(239, 68, 68, 0.3) !important;
      color: #ef4444 !important;

      .anticon {
        color: #ef4444 !important;
      }

      &:hover, &:focus {
        background: rgba(239, 68, 68, 0.2) !important;
        border-color: rgba(239, 68, 68, 0.5) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3) !important;

        .anticon {
          color: #ef4444 !important;
        }
      }
    }
  }
}

:deep(.ant-spin-container) {
  min-height: 200px;
}

:deep(.ant-spin-dot) {
  .ant-spin-dot-item {
    background-color: #3b82f6;
  }
}

// 强制覆盖文字颜色，确保在白色背景下可见，但保持图标颜色
.personnel-card-container {
  h1, h2, h3, h4, h5, h6 {
    color: #1f2937 !important;
  }

  p, span:not(.anticon), div:not(.anticon) {
    color: #1f2937 !important;
  }

  // 图标颜色专门设置
  .title-icon {
    color: #3b82f6 !important;
  }

  .card-icon {
    color: #3b82f6 !important;
  }

  .empty-icon {
    color: #d1d5db !important;
  }

  .placeholder-icon {
    color: #9ca3af !important;
  }

  .stat-icon {
    &.total .anticon {
      color: white !important;
    }
    &.bound .anticon {
      color: white !important;
    }
    &.unbound .anticon {
      color: white !important;
    }
  }

  .user-avatar .anticon {
    color: #3b82f6 !important;
  }

  .ant-tag {
    color: inherit !important;
  }

  .ant-btn {
    &.ant-btn-primary {
      color: #ffffff !important;
      .anticon {
        color: #ffffff !important;
      }
    }
    &.ant-btn-default {
      color: #ef4444 !important;
      .anticon {
        color: #ef4444 !important;
      }
    }
  }
}
</style>
