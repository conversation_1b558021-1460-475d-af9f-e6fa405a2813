<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm"/>
  </BasicModal>
</template>

<script lang="ts" setup>
  import {ref, computed, unref} from 'vue';
  import {BasicModal, useModalInner} from '/@/components/Modal';
  import {BasicForm, useForm} from '/@/components/Form/index';
  import { formSchema } from '../MasterPlan.data';
  import { saveOrUpdate, checkMasterPlanName } from '../MasterPlan.api';
  import {useMessage} from "/@/hooks/web/useMessage";

  const emit = defineEmits(['success', 'register']);
  const isUpdate = ref(true);
  const {createMessage} = useMessage();

  //表单配置
  const [registerForm, {setProps, resetFields, setFieldsValue, validate}] = useForm({
    //labelWidth: 150,
    schemas: formSchema,
    showActionButtonGroup: false,
    size: 'default'
  });

  //表单赋值
  const [registerModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
    //重置表单
    await resetFields();
    setModalProps({confirmLoading: false,showCancelBtn:data?.showCancelBtn,showOkBtn:data?.showOkBtn});
    isUpdate.value = !!data?.isUpdate;
    if (unref(isUpdate)) {
      //表单赋值
      await setFieldsValue({
        ...data.record,
      });
    }
    // 隐藏底部时禁用整个表单
    setProps({ disabled: !data?.showOkBtn })
  });

  const getTitle = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));

  //表单提交
  async function handleSubmit(v) {
    try {
      const values = await validate();
      setModalProps({confirmLoading: true});
      
      // 验证总计划名称是否重复
      try {
        await checkMasterPlanName(values.masterPlanName, values.id);
      } catch (error) {
        createMessage.error('总计划名称已存在，请重新输入！');
        setModalProps({confirmLoading: false});
        return;
      }
      
      //提交表单
      await saveOrUpdate(values, unref(isUpdate));
      //关闭弹窗
      closeModal();
      //刷新列表
      emit('success');
    } finally {
      setModalProps({confirmLoading: false});
    }
  }
</script>
