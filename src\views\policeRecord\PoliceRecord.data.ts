import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '设备',
    align:"center",
    dataIndex: 'deviceName'
   },
   {
    title: '线路名称',
    align:"center",
    dataIndex: 'lineName'
   },
   {
    title: '地址',
    align:"center",
    dataIndex: 'cardName'
   },
   {
    title: '巡检时间',
    align:"center",
    dataIndex: 'recordTime'
   },
   {
    title: '状态',
    align: "center",
    dataIndex: 'status',
    customRender:({text}) => {
      const color = text == '0' ? 'orange' : text == '1' ? 'green' : text == '2' ? 'red' : 'gray';
      return render.renderTag( text == '0' ? '未按时' : text == '1' ? '正常'  : text == '2' ? '漏检' : '未知', color);
    },
  },
  //  {
  //   title: '使用人类型',
  //   align:"center",
  //   dataIndex: 'deviceUser'
  //  },
  
   {
    title: '民警',
    align:"center",
    dataIndex: 'patrolUserName'
   },
   {
    title: '视频监控截图',
    align: "center",
    dataIndex: 'screenshots',
    width: 200,
    slots: { customRender: 'screenshots' }
  },
  //  {
  //   title: '巡检计划',
  //   align:"center",
  //   dataIndex: 'planId'
  //  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
     {
      label: "巡检时间",
      field: "recordTime",
      component: 'RangePicker',
      componentProps: {
          valueType: 'Date',
          showTime:true
      },
      //colProps: {span: 6},
	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '设备',
    field: 'deviceId',
    component: 'Input',
  },
  {
    label: '线路名称',
    field: 'lineId',
    component: 'Input',
  },
  {
    label: '地址',
    field: 'cardId',
    component: 'Input',
  },
  {
    label: '巡检时间',
    field: 'recordTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '状态',
    field: 'status',
    component: 'Input',
  },
  {
    label: '使用人类型',
    field: 'deviceUser',
    component: 'Input',
  },
  {
    label: '记录编号',
    field: 'num',
    component: 'Input',
  },
  {
    label: '民警',
    field: 'patrolUserId',
    component: 'Input',
  },
  {
    label: '巡检计划',
    field: 'planId',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  deviceId: {title: '设备',order: 0,view: 'text', type: 'string',},
  lineId: {title: '线路名称',order: 1,view: 'text', type: 'string',},
  cardId: {title: '地址',order: 2,view: 'text', type: 'string',},
  recordTime: {title: '巡检时间',order: 3,view: 'datetime', type: 'string',},
  status: {title: '状态',order: 4,view: 'text', type: 'string',},
  deviceUser: {title: '使用人类型',order: 5,view: 'text', type: 'string',},
  num: {title: '记录编号',order: 6,view: 'text', type: 'string',},
  patrolUserId: {title: '民警',order: 7,view: 'text', type: 'string',},
  planId: {title: '巡检计划',order: 8,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}