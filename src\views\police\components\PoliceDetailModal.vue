<template>
  <!-- 科技感详情模态框 -->
  <j-modal
    :title="title"
    :width="900"
    :visible="visible"
    :footer="null"
    @cancel="handleCancel"
    class="tech-modal"
    :mask-closable="true"
    :keyboard="true"
    :centered="true"
  >
    <PoliceDetailView :policeData="currentRecord" />
  </j-modal>
</template>

<script lang="ts" setup>
  import { ref, defineExpose } from 'vue';
  import PoliceDetailView from './PoliceDetailView.vue';
  import JModal from '/@/components/Modal/src/JModal/JModal.vue';

  const title = ref<string>('民警详情');
  const visible = ref<boolean>(false);
  const currentRecord = ref<any>({});

  /**
   * 打开详情模态框
   * @param record 民警记录
   */
  function openDetail(record: any) {
    currentRecord.value = record;
    visible.value = true;
  }

  /**
   * 关闭模态框
   */
  function handleCancel() {
    visible.value = false;
    currentRecord.value = {};
  }

  // 暴露方法给父组件
  defineExpose({
    openDetail
  });
</script>

<style scoped lang="less">
/* 科技感模态框样式 */
:deep(.tech-modal) {
  .ant-modal-content {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(59, 130, 246, 0.2);
    box-shadow: 
      0 20px 40px rgba(59, 130, 246, 0.1),
      0 10px 20px rgba(6, 182, 212, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  }

  .ant-modal-header {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(6, 182, 212, 0.03) 100%);
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .ant-modal-title {
    color: #1e293b;
    font-weight: 600;
    font-size: 18px;
  }

  .ant-modal-close {
    color: #64748b;
    transition: all 0.3s ease;
  }

  .ant-modal-close:hover {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
  }

  .ant-modal-body {
    padding: 0;
    background: transparent;
  }
}

/* 科技感背景动画 */
:deep(.tech-modal .ant-modal-content::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}
</style>
