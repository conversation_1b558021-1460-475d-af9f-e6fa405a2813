import { defHttp } from '/@/utils/http/axios';

enum Api {
  getCommandCenterData = '/plan/plan/command-center',
  updatePatrolStatus = '/patrol/command-center/updateStatus',
}

/**
 * 获取指挥中心可视化数据
 * @param params
 */
export const getCommandCenterData = (params?: any) => {
  return defHttp.get({ 
    url: Api.getCommandCenterData, 
    params: params || {} 
  });
};

/**
 * 更新巡更状态
 * @param params
 */
export const updatePatrolStatus = (params: any) => {
  return defHttp.post({ 
    url: Api.updatePatrolStatus, 
    params 
  });
};

// 人员信息接口
export interface StaffInfo {
  id: string | number;
  name: string;
  cardId: string;
  avatar?: string;
  status: 'on-duty' | 'online' | 'offline';
  isOnDuty: boolean;
  online: boolean; // 新增：true=巡更中，false=离线
  department: string;
  position: string;
}

// 巡更点接口
export interface PatrolPoint {
  id: string | number;
  name: string;
  status: 'checked' | 'current' | 'pending' | 'missed';
  checkTime?: string;
  location?: {
    x: number;
    y: number;
  };
  description?: string;
}

// 巡更计划接口
export interface PatrolPlan {
  id: string | number;
  name?: string;
  sectionName: string;
  status: 'in-progress' | 'pending' | 'completed' | 'missed';
  startTime: string;
  endTime: string;
  estimatedDuration: string;
  currentDuration?: string;
  actualDuration?: string;
  staff: StaffInfo;
  points: PatrolPoint[];
  priority?: 'high' | 'medium' | 'low';
  notes?: string;
}

// 分监区接口
export interface SubSection {
  id: string | number;
  name: string;
  staff: StaffInfo[];
  patrolPlans: PatrolPlan[];
}

// 监区接口
export interface PrisonSection {
  id: string | number;
  name: string;
  subSections: SubSection[];
}

// 指挥中心可视化数据接口
export interface CommandCenterData {
  prisonName: string;
  sections: PrisonSection[];
  plans: PatrolPlan[];  // 巡更记录列表 - 后台直接返回的巡更计划数据
  totalStatistics: {
    totalStaff: number;
    onDutyStaff: number;
    totalPlans: number;
    inProgressPlans: number;
    completedPlans: number;
    missedPlans: number;
  };
}

// 生成人员数据
const generateStaff = (sectionName: string, subSectionName: string, startId: number): StaffInfo[] => {
  const staffNames = ['张三', '李四', '王五'];

  return staffNames.map((name, index) => {
    const staffId = startId + index;
    const isOnline = staffId % 3 === 0; // 简化：每3个人中有1个在线
    return {
      id: staffId,
      name: `${name}`,
      cardId: `P${String(staffId).padStart(3, '0')}`,
      avatar: '', // 使用空字符串，让前端生成SVG头像
      status: isOnline ? 'on-duty' : 'offline', // 保持向后兼容
      isOnDuty: isOnline,
      online: isOnline, // 新增：true=巡更中，false=离线
      department: sectionName,
      position: '巡更员'
    };
  });
};

// 生成巡更点数据
const generatePatrolPoints = (planId: number): PatrolPoint[] => {
  const pointNames = [
    '活动室', '1号室', '2号室', '3号室', '4号室', '5号室',
    '6号室', '7号室', '8号室', '9号室', '10号室', '活动室'
  ];
  
  const currentPointIndex = planId % 12;
  
  return pointNames.map((name, index) => ({
    id: `${planId}-${index}`,
    name,
    status: index < currentPointIndex ? 'checked' : 
           index === currentPointIndex ? 'current' : 
           index === currentPointIndex + 1 ? 'pending' : 'pending',
    checkTime: index < currentPointIndex ? 
      `${String(8 + Math.floor(index / 2)).padStart(2, '0')}:${String((index % 2) * 30).padStart(2, '0')}` : 
      undefined,
    location: { x: index * 50, y: 100 },
    description: `${name}巡更点`
  }));
};

// 生成巡更计划数据
const generatePatrolPlans = (sectionName: string, subSectionName: string, staff: StaffInfo[]): PatrolPlan[] => {
  const plans: PatrolPlan[] = [];
  const statuses: ('in-progress' | 'pending' | 'completed' | 'missed')[] = ['in-progress', 'pending', 'completed', 'missed'];
  
  staff.forEach((staffMember, index) => {
    const planId = parseInt(staffMember.id.toString());
    const status = statuses[index % statuses.length];
    
    plans.push({
      id: planId,
      name: `${subSectionName}巡更计划${index + 1}`,
      sectionName: `${sectionName}-${subSectionName}`,
      status,
      startTime: `${String(8 + index).padStart(2, '0')}:00`,
      endTime: `${String(9 + index).padStart(2, '0')}:00`,
      estimatedDuration: '60分钟',
      currentDuration: status === 'in-progress' ? `${20 + index * 5}分钟` : undefined,
      actualDuration: status === 'completed' ? `${55 + index * 2}分钟` : undefined,
      staff: staffMember,
      points: generatePatrolPoints(planId),
      priority: index % 3 === 0 ? 'high' : (index % 2 === 0 ? 'medium' : 'low'),
      notes: `${subSectionName}例行巡更`
    });
  });
  
  return plans;
};

// 生成分监区数据
const generateSubSections = (sectionName: string, startStaffId: number, subSectionCount: number = 3): SubSection[] => {
  const subSections: SubSection[] = [];
  const subSectionLabels = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];

  for (let subIndex = 1; subIndex <= subSectionCount; subIndex++) {
    const subSectionName = `${sectionName}${subSectionLabels[subIndex - 1]}区`;
    const staff = generateStaff(sectionName, subSectionName, startStaffId + (subIndex - 1) * 3);
    const patrolPlans = generatePatrolPlans(sectionName, subSectionName, staff);

    subSections.push({
      id: `${sectionName}-${subIndex}`,
      name: subSectionName,
      staff,
      patrolPlans
    });
  }

  return subSections;
};

// 生成模拟数据
export const generateCommandCenterMockData = (): CommandCenterData => {
  const sections: PrisonSection[] = [];
  let staffIdCounter = 1;

  // 生成10个监区，每个监区3-4个分监区（总共34个分监区）
  const subSectionCounts = [4, 3, 4, 3, 3, 4, 3, 4, 3, 3]; // 总计34个分监区

  for (let sectionIndex = 1; sectionIndex <= 10; sectionIndex++) {
    const sectionName = `第${sectionIndex}监区`;
    const subSectionCount = subSectionCounts[sectionIndex - 1];
    const subSections = generateSubSections(sectionName, staffIdCounter, subSectionCount);

    sections.push({
      id: sectionIndex,
      name: sectionName,
      subSections
    });

    staffIdCounter += subSectionCount * 3; // 每个分监区3个人员
  }
  
  // 计算统计数据并收集所有巡更计划
  let totalStaff = 0;
  let onDutyStaff = 0;
  let totalPlans = 0;
  let inProgressPlans = 0;
  let completedPlans = 0;
  let missedPlans = 0;
  const allPlans: PatrolPlan[] = [];

  sections.forEach(section => {
    section.subSections.forEach(subSection => {
      totalStaff += subSection.staff.length;
      onDutyStaff += subSection.staff.filter(s => s.isOnDuty).length;
      totalPlans += subSection.patrolPlans.length;
      inProgressPlans += subSection.patrolPlans.filter(p => p.status === 'in-progress').length;
      completedPlans += subSection.patrolPlans.filter(p => p.status === 'completed').length;
      missedPlans += subSection.patrolPlans.filter(p => p.status === 'missed').length;

      // 收集所有巡更计划
      allPlans.push(...subSection.patrolPlans);
    });
  });

  return {
    prisonName: '监狱指挥中心可视化大屏',
    sections,
    plans: allPlans,  // 添加巡更计划列表
    totalStatistics: {
      totalStaff,
      onDutyStaff,
      totalPlans,
      inProgressPlans,
      completedPlans,
      missedPlans
    }
  };
};
