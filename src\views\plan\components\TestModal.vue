<template>
  <a-modal 
    title="RTSP测试" 
    :width="600" 
    v-model:open="visible" 
    @cancel="handleCancel"
  >
    <div class="p-4">
      <h3>🎥 RTSP视频流测试</h3>
      <p>这是一个简单的测试弹窗</p>
      <p>RTSP地址: {{ rtspUrl }}</p>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const visible = ref(false);
const rtspUrl = ref('');

const showModal = (url?: string) => {
  console.log('TestModal showModal called with:', url);
  if (url) {
    rtspUrl.value = url;
  }
  visible.value = true;
};

const handleCancel = () => {
  visible.value = false;
};

defineExpose({
  showModal
});
</script>
