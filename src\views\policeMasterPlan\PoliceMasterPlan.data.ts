import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { h } from 'vue';

// 科技感数据显示组件
const renderTechData = (value: number | string, type: 'count' | 'rate' = 'count', status?: 'normal' | 'warning' | 'danger') => {
  const numValue = typeof value === 'string' ? parseFloat(value) || 0 : (value || 0);

  // 根据类型确定显示格式
  const displayValue = type === 'rate' ? `${numValue}%` : numValue.toString();

  // 根据状态确定颜色
  let textColor = '#1890ff'; // 默认蓝色
  let bgColor = 'rgba(24, 144, 255, 0.1)';
  let borderColor = 'rgba(24, 144, 255, 0.3)';

  if (status === 'warning') {
    textColor = '#fa8c16';
    bgColor = 'rgba(250, 140, 22, 0.1)';
    borderColor = 'rgba(250, 140, 22, 0.3)';
  }
  if (status === 'danger') {
    textColor = '#f5222d';
    bgColor = 'rgba(245, 34, 45, 0.1)';
    borderColor = 'rgba(245, 34, 45, 0.3)';
  }
  if (status === 'normal') {
    textColor = '#52c41a';
    bgColor = 'rgba(82, 196, 26, 0.1)';
    borderColor = 'rgba(82, 196, 26, 0.3)';
  }

  return h('div', {
    style: {
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '4px 12px',
      borderRadius: '6px',
      background: bgColor,
      border: `1px solid ${borderColor}`,
      color: textColor,
      fontWeight: '600',
      fontSize: '13px',
      fontFamily: 'Monaco, Consolas, monospace',
      minWidth: '60px',
      boxShadow: `0 2px 4px ${bgColor}`,
      transition: 'all 0.3s ease'
    }
  }, displayValue);
};

// 巡更率显示组件
const renderPatrolRate = (rate: number | string) => {
  const numRate = typeof rate === 'string' ? parseFloat(rate) || 0 : (rate || 0);

  // 根据巡更率确定状态和颜色
  let progressColor = '#52c41a'; // 绿色 - 正常
  let textColor = '#52c41a';
  let bgColor = 'rgba(82, 196, 26, 0.1)';

  if (numRate < 60) {
    progressColor = '#f5222d'; // 红色 - 危险
    textColor = '#f5222d';
    bgColor = 'rgba(245, 34, 45, 0.1)';
  } else if (numRate < 80) {
    progressColor = '#fa8c16'; // 橙色 - 警告
    textColor = '#fa8c16';
    bgColor = 'rgba(250, 140, 22, 0.1)';
  }

  return h('div', {
    style: {
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      width: '100%',
    }
  }, [
    // 进度条容器
    h('div', {
      style: {
        flex: 1,
        height: '16px',
        background: '#f5f5f5',
        borderRadius: '8px',
        border: '1px solid #d9d9d9',
        overflow: 'hidden',
        position: 'relative',
      }
    }, [
      // 进度条
      h('div', {
        style: {
          width: `${numRate}%`,
          height: '100%',
          background: progressColor,
          borderRadius: '8px',
          transition: 'width 0.3s ease',
        }
      })
    ]),
    // 数值显示
    h('span', {
      style: {
        fontFamily: '"SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace',
        fontSize: '12px',
        fontWeight: '600',
        color: textColor,
        minWidth: '40px',
        textAlign: 'right',
        padding: '2px 6px',
        background: bgColor,
        borderRadius: '4px',
        border: `1px solid ${progressColor}33`,
      }
    }, `${numRate}%`)
  ]);
};

//列表数据
export const columns: BasicColumn[] = [
  {
    title: '总计划名称',
    align: "center",
    dataIndex: 'masterPlanName',
    width: 250,
    ellipsis: true,
    customRender: ({ text }) => {
      return h('div', {
        style: {
          wordBreak: 'break-word',
          whiteSpace: 'normal',
          lineHeight: '1.4',
          padding: '4px 0',
          maxHeight: '60px',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          display: '-webkit-box',
          WebkitLineClamp: 3,
          WebkitBoxOrient: 'vertical'
        },
        title: text || ''
      }, text || '');
    }
  },
  {
    title: '巡更周期',
    align: "center",
    dataIndex: 'patrolCycle',
    width: 150
  },
  {
    title: '已执行计划次数',
    align: "center",
    dataIndex: 'executedPlanCount',
    width: 140,
    customRender: ({text}) => {
      return renderTechData(text || 0, 'count');
    }
  },
  {
    title: '总巡更点数',
    align: "center",
    dataIndex: 'totalPatrolPoints',
    width: 120,
    customRender: ({text}) => {
      return renderTechData(text || 0, 'count');
    }
  },
  {
    title: '正常巡更点数',
    align: "center",
    dataIndex: 'normalPatrolPoints',
    width: 130,
    customRender: ({text}) => {
      return renderTechData(text || 0, 'count', 'normal');
    }
  },
  {
    title: '待巡点数',
    align: "center",
    dataIndex: 'pendingPatrolPoints',
    width: 110,
    customRender: ({text}) => {
      const value = text || 0;
      const status = value > 0 ? 'warning' : 'normal';
      return renderTechData(value, 'count', status);
    }
  },
  {
    title: '漏巡点数',
    align: "center",
    dataIndex: 'missedPatrolPoints',
    width: 110,
    customRender: ({text}) => {
      const value = text || 0;
      const status = value > 0 ? 'danger' : 'normal';
      return renderTechData(value, 'count', status);
    }
  },
  {
    title: '正常巡更率',
    align: "center",
    dataIndex: 'normalPatrolRate',
    width: 180,
    customRender: ({text}) => {
      const rate = typeof text === 'string' ? parseFloat(text) || 0 : (text || 0);
      return renderPatrolRate(rate);
    }
  },
  // {
  //   title: '描述',
  //   align: "center",
  //   dataIndex: 'description',
  //   width: 200,
  //   ellipsis: true,
  //   customRender: ({ text }) => {
  //     return h('div', {
  //       style: {
  //         wordBreak: 'break-word',
  //         whiteSpace: 'normal',
  //         lineHeight: '1.4',
  //         padding: '4px 0',
  //         maxHeight: '60px',
  //         overflow: 'hidden',
  //         textOverflow: 'ellipsis',
  //         display: '-webkit-box',
  //         WebkitLineClamp: 3,
  //         WebkitBoxOrient: 'vertical'
  //       },
  //       title: text || ''
  //     }, text || '');
  //   }
  // },
];

// 高级查询数据
export const superQuerySchema = {
  masterPlanName: {title: '总计划名称',order: 0,view: 'text', type: 'string',},
  lineId: {title: '路线',order: 1,view: 'text', type: 'string',},
  patrolCycle: {title: '巡更周期',order: 2,view: 'text', type: 'string',},
  description: {title: '描述',order: 3,view: 'text', type: 'string',},
};
