import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';

const patrol: AppRouteModule = {
  path: '/patrol',
  name: 'Patrol',
  component: LAYOUT,
  redirect: '/patrol/index',
  meta: {
    orderNo: 15,
    icon: 'ant-design:eye-outlined',
    title: '巡更管理',
  },
  children: [
    {
      path: 'index',
      name: 'PatrolIndex',
      component: () => import('/@/views/patrolDashboard/index.vue'),
      meta: {
        title: '巡更大屏首页',
        hideMenu: false,
      },
    },
    {
      path: 'dashboard',
      name: 'PatrolDashboard',
      component: () => import('/@/views/patrolDashboard/PatrolDashboard.vue'),
      meta: {
        title: '分监区可视化大屏',
        hideMenu: false,
      },
    },
    {
      path: 'fullscreen',
      name: 'PatrolDashboardFullscreen',
      component: () => import('/@/views/patrolDashboard/PatrolDashboardFullscreen.vue'),
      meta: {
        title: '分监区大屏(全屏)',
        hideMenu: true,
        hideBreadcrumb: true,
        hideTab: true,
        ignoreKeepAlive: true,
      },
    },
    {
      path: 'visualization',
      name: 'PrisonVisualization',
      component: () => import('/@/views/patrolDashboard/PrisonVisualization.vue'),
      meta: {
        title: '监区可视化数据大屏',
        hideMenu: false,
      },
    },
    {
      path: 'police-patrol',
      name: 'PolicePatrolDashboard',
      component: () => import('/@/views/patrolDashboard/PolicePatrolDashboard.vue'),
      meta: {
        title: '监区民警巡更数据大屏',
        hideMenu: false,
      },
    },
    {
      path: 'police-patrol-fullscreen',
      name: 'PolicePatrolDashboardFullscreen',
      component: () => import('/@/views/patrolDashboard/PolicePatrolDashboardFullscreen.vue'),
      meta: {
        title: '监区民警巡更数据大屏(全屏)',
        hideMenu: true,
        hideBreadcrumb: true,
        hideTab: true,
        ignoreKeepAlive: true,
      },
    },
    {
      path: 'visualization-fullscreen',
      name: 'PrisonVisualizationFullscreen',
      component: () => import('/@/views/patrolDashboard/PrisonVisualizationFullscreen.vue'),
      meta: {
        title: '监区可视化大屏(全屏)',
        hideMenu: true,
        hideBreadcrumb: true,
        hideTab: true,
        ignoreKeepAlive: true,
      },
    },
    {
      path: 'command-center',
      name: 'CommandCenter',
      component: () => import('/@/views/patrolDashboard/CommandCenter.vue'),
      meta: {
        title: '指挥中心可视化大屏',
        hideMenu: false,
      },
    },
    {
      path: 'command-center-fullscreen',
      name: 'CommandCenterFullscreen',
      component: () => import('/@/views/patrolDashboard/CommandCenterFullscreen.vue'),
      meta: {
        title: '指挥中心大屏(全屏)',
        hideMenu: true,
        hideBreadcrumb: true,
        hideTab: true,
        ignoreKeepAlive: true,
      },
    },
    // 可以添加其他巡更相关的路由
    {
      path: 'plans',
      name: 'PatrolPlans',
      component: () => import('/@/views/plan/PlanList.vue'),
      meta: {
        title: '巡更计划管理',
      },
    },
    {
      path: 'records',
      name: 'PatrolRecords',
      component: () => import('/@/views/record/RecordList.vue'),
      meta: {
        title: '巡更记录',
      },
    },
    {
      path: 'staff',
      name: 'PatrolStaff',
      component: () => import('/@/views/patrolUser/PatrolUserList.vue'),
      meta: {
        title: '巡更人员管理',
      },
    },
    {
      path: 'alarms',
      name: 'PatrolAlarms',
      component: () => import('/@/views/alarm/AlarmList.vue'),
      meta: {
        title: '报警管理',
      },
    },
    {
      path: 'alarm-test',
      name: 'AlarmTest',
      component: () => import('/@/views/test/AlarmTest.vue'),
      meta: {
        title: '报警弹窗测试',
        hideMenu: false,
      },
    },
    {
      path: 'device-detail-test',
      name: 'DeviceDetailTest',
      component: () => import('/@/views/device/DeviceDetailTest.vue'),
      meta: {
        title: '设备详情测试',
        hideMenu: false,
      },
    },
    {
      path: 'alarm-detail-test',
      name: 'AlarmDetailTest',
      component: () => import('/@/views/alarm/AlarmDetailTest.vue'),
      meta: {
        title: '报警详情测试',
        hideMenu: false,
      },
    },
  ],
};

export default patrol;
