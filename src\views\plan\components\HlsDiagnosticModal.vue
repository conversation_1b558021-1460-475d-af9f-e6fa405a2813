<template>
  <j-modal 
    :title="'HLS视频播放诊断工具'" 
    :width="900" 
    v-model:open="open" 
    :footer="null" 
    @cancel="handleCancel" 
    class="hls-diagnostic-modal"
  >
    <div class="p-6">
      <!-- URL输入区域 -->
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-3">🔧 HLS播放问题诊断</h3>
        <div class="bg-blue-50 p-4 rounded-lg mb-4">
          <p class="text-sm text-blue-800 mb-2">
            <strong>常见错误：</strong> networkError-manifestLoadError
          </p>
          <p class="text-xs text-blue-600">
            此错误通常表示无法加载HLS清单文件(.m3u8)，可能的原因包括网络问题、CORS限制、服务器配置等。
          </p>
        </div>
        
        <label class="block text-sm font-medium text-gray-700 mb-2">
          输入HLS视频URL：
        </label>
        <div class="flex gap-2">
          <input 
            v-model="testUrl" 
            type="text" 
            class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="http://example.com/video.m3u8"
          />
          <button 
            @click="startDiagnosis" 
            :disabled="!testUrl || diagnosing"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 transition-colors"
          >
            {{ diagnosing ? '诊断中...' : '开始诊断' }}
          </button>
        </div>
      </div>

      <!-- 诊断结果 -->
      <div class="space-y-4">
        <!-- 基础检测 -->
        <div class="border rounded-lg p-4">
          <h4 class="font-medium mb-3 flex items-center">
            <span class="mr-2">🔍</span>
            基础检测
          </h4>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div class="flex justify-between">
              <span>HLS.js支持：</span>
              <span :class="hlsSupported ? 'text-green-600' : 'text-red-600'">
                {{ hlsSupported ? '✅ 支持' : '❌ 不支持' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span>浏览器原生HLS：</span>
              <span :class="nativeHlsSupported ? 'text-green-600' : 'text-red-600'">
                {{ nativeHlsSupported ? '✅ 支持' : '❌ 不支持' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span>URL格式：</span>
              <span :class="isValidHlsUrl ? 'text-green-600' : 'text-orange-600'">
                {{ isValidHlsUrl ? '✅ HLS格式' : '⚠️ 非标准格式' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span>网络连通性：</span>
              <span :class="getConnectivityColor()">
                {{ connectivityStatus }}
              </span>
            </div>
          </div>
        </div>

        <!-- 详细诊断 -->
        <div class="border rounded-lg p-4" v-if="diagnosticResults.length > 0">
          <h4 class="font-medium mb-3 flex items-center">
            <span class="mr-2">📋</span>
            诊断报告
          </h4>
          <div class="space-y-2">
            <div 
              v-for="(result, index) in diagnosticResults" 
              :key="index"
              class="p-3 rounded-md text-sm"
              :class="getResultClass(result.type)"
            >
              <div class="flex items-start">
                <span class="mr-2">{{ getResultIcon(result.type) }}</span>
                <div>
                  <div class="font-medium">{{ result.title }}</div>
                  <div class="mt-1">{{ result.message }}</div>
                  <div v-if="result.solution" class="mt-2 text-xs opacity-80">
                    <strong>解决方案：</strong> {{ result.solution }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 解决方案建议 -->
        <div class="border rounded-lg p-4">
          <h4 class="font-medium mb-3 flex items-center">
            <span class="mr-2">💡</span>
            常见解决方案
          </h4>
          <div class="space-y-3 text-sm">
            <div class="bg-yellow-50 p-3 rounded-md">
              <div class="font-medium text-yellow-800 mb-1">CORS跨域问题</div>
              <div class="text-yellow-700">
                • 服务器需要设置正确的CORS头：Access-Control-Allow-Origin<br>
                • 检查服务器是否允许跨域访问<br>
                • 尝试使用代理服务器
              </div>
            </div>
            
            <div class="bg-blue-50 p-3 rounded-md">
              <div class="font-medium text-blue-800 mb-1">网络连接问题</div>
              <div class="text-blue-700">
                • 检查网络连接是否正常<br>
                • 确认服务器地址和端口正确<br>
                • 检查防火墙设置
              </div>
            </div>
            
            <div class="bg-green-50 p-3 rounded-md">
              <div class="font-medium text-green-800 mb-1">服务器配置</div>
              <div class="text-green-700">
                • 确认服务器返回正确的Content-Type: application/vnd.apple.mpegurl<br>
                • 检查m3u8文件格式是否正确<br>
                • 验证视频片段文件是否存在
              </div>
            </div>
          </div>
        </div>

        <!-- 测试工具 -->
        <div class="border rounded-lg p-4">
          <h4 class="font-medium mb-3 flex items-center">
            <span class="mr-2">🛠️</span>
            测试工具
          </h4>
          <div class="space-y-2 text-sm">
            <button 
              @click="openUrlInNewTab" 
              :disabled="!testUrl"
              class="w-full text-left p-2 border rounded hover:bg-gray-50 disabled:opacity-50"
            >
              🌐 在新标签页中打开URL（检查文件内容）
            </button>
            <button 
              @click="copyToClipboard" 
              :disabled="!testUrl"
              class="w-full text-left p-2 border rounded hover:bg-gray-50 disabled:opacity-50"
            >
              📋 复制URL到剪贴板
            </button>
            <button 
              @click="generateCurlCommand" 
              :disabled="!testUrl"
              class="w-full text-left p-2 border rounded hover:bg-gray-50 disabled:opacity-50"
            >
              💻 生成curl测试命令
            </button>
          </div>
          
          <div v-if="curlCommand" class="mt-3 p-3 bg-gray-100 rounded-md">
            <div class="text-xs font-medium mb-1">curl测试命令：</div>
            <code class="text-xs break-all">{{ curlCommand }}</code>
            <button 
              @click="copyCurlCommand" 
              class="ml-2 text-xs text-blue-600 hover:text-blue-800"
            >
              复制
            </button>
          </div>
        </div>
      </div>
    </div>
  </j-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import JModal from "@/components/Modal/src/JModal/JModal.vue";
import Hls from 'hls.js';

interface DiagnosticResult {
  type: 'success' | 'warning' | 'error' | 'info';
  title: string;
  message: string;
  solution?: string;
}

const open = ref(false);
const testUrl = ref('http://***********:554/openUrl/3whQVPi.m3u8?beginTime=20170615T000000&endTime=20170617T000000&playBackMode=1');
const diagnosing = ref(false);
const diagnosticResults = ref<DiagnosticResult[]>([]);
const connectivityStatus = ref('未测试');
const curlCommand = ref('');

// 基础检测
const hlsSupported = computed(() => Hls.isSupported());
const nativeHlsSupported = computed(() => {
  const video = document.createElement('video');
  return video.canPlayType('application/vnd.apple.mpegurl') !== '';
});
const isValidHlsUrl = computed(() => {
  return testUrl.value && (
    testUrl.value.includes('.m3u8') || 
    testUrl.value.includes('openUrl')
  );
});

// 获取连通性状态颜色
const getConnectivityColor = () => {
  if (connectivityStatus.value.includes('✅')) return 'text-green-600';
  if (connectivityStatus.value.includes('❌')) return 'text-red-600';
  if (connectivityStatus.value.includes('⚠️')) return 'text-orange-600';
  return 'text-gray-600';
};

// 获取结果样式类
const getResultClass = (type: string) => {
  const classes = {
    success: 'bg-green-50 border-green-200',
    warning: 'bg-yellow-50 border-yellow-200',
    error: 'bg-red-50 border-red-200',
    info: 'bg-blue-50 border-blue-200'
  };
  return `border ${classes[type] || classes.info}`;
};

// 获取结果图标
const getResultIcon = (type: string) => {
  const icons = {
    success: '✅',
    warning: '⚠️',
    error: '❌',
    info: 'ℹ️'
  };
  return icons[type] || icons.info;
};

// 开始诊断
const startDiagnosis = async () => {
  if (!testUrl.value) return;
  
  diagnosing.value = true;
  diagnosticResults.value = [];
  connectivityStatus.value = '测试中...';
  
  try {
    // 1. URL格式检查
    if (!isValidHlsUrl.value) {
      diagnosticResults.value.push({
        type: 'warning',
        title: 'URL格式检查',
        message: 'URL不包含.m3u8扩展名，可能不是标准HLS格式',
        solution: '确认URL是否为有效的HLS流地址'
      });
    }

    // 2. 网络连通性测试
    try {
      const response = await fetch(testUrl.value, { 
        method: 'HEAD',
        mode: 'no-cors'
      });
      connectivityStatus.value = '✅ 可连接';
      diagnosticResults.value.push({
        type: 'success',
        title: '网络连通性',
        message: '服务器响应正常'
      });
    } catch (error) {
      connectivityStatus.value = '❌ 连接失败';
      diagnosticResults.value.push({
        type: 'error',
        title: '网络连通性',
        message: '无法连接到服务器',
        solution: '检查网络连接、服务器地址和端口是否正确'
      });
    }

    // 3. HLS支持检查
    if (!hlsSupported.value && !nativeHlsSupported.value) {
      diagnosticResults.value.push({
        type: 'error',
        title: 'HLS支持',
        message: '浏览器不支持HLS播放',
        solution: '使用支持HLS的浏览器或安装HLS.js库'
      });
    }

    // 4. CORS检查（尝试实际请求）
    try {
      const response = await fetch(testUrl.value);
      const text = await response.text();
      
      if (text.includes('#EXTM3U')) {
        diagnosticResults.value.push({
          type: 'success',
          title: 'HLS清单文件',
          message: '成功获取到HLS清单文件内容'
        });
      } else {
        diagnosticResults.value.push({
          type: 'warning',
          title: 'HLS清单文件',
          message: '获取到响应但内容不是有效的HLS清单',
          solution: '检查服务器返回的内容格式'
        });
      }
    } catch (error) {
      diagnosticResults.value.push({
        type: 'error',
        title: 'CORS/请求',
        message: '无法获取清单文件，可能是CORS限制',
        solution: '服务器需要设置正确的CORS头或使用代理'
      });
    }

  } catch (error) {
    diagnosticResults.value.push({
      type: 'error',
      title: '诊断失败',
      message: `诊断过程中发生错误: ${error.message}`
    });
  } finally {
    diagnosing.value = false;
  }
};

// 工具函数
const openUrlInNewTab = () => {
  if (testUrl.value) {
    window.open(testUrl.value, '_blank');
  }
};

const copyToClipboard = async () => {
  if (testUrl.value) {
    try {
      await navigator.clipboard.writeText(testUrl.value);
      alert('URL已复制到剪贴板');
    } catch (error) {
      console.error('复制失败:', error);
    }
  }
};

const generateCurlCommand = () => {
  if (testUrl.value) {
    curlCommand.value = `curl -I "${testUrl.value}"`;
  }
};

const copyCurlCommand = async () => {
  if (curlCommand.value) {
    try {
      await navigator.clipboard.writeText(curlCommand.value);
      alert('curl命令已复制到剪贴板');
    } catch (error) {
      console.error('复制失败:', error);
    }
  }
};

// 显示模态框
const showModal = () => {
  open.value = true;
};

// 关闭模态框
const handleCancel = () => {
  open.value = false;
  diagnosticResults.value = [];
  connectivityStatus.value = '未测试';
  curlCommand.value = '';
};

defineExpose({
  showModal
});
</script>

<style scoped>
.hls-diagnostic-modal :deep(.ant-modal-content) {
  border-radius: 16px;
}
</style>
