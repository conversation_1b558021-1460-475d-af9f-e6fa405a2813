<template>
  <div class="p-2">
    <a-row :gutter="24">
      <!-- 左侧路线列表 -->
      <a-col :xs="24" :sm="24" :md="5" :lg="4" :xl="3">
        <div class="route-list-container">
          <h3 style="margin-bottom: 16px;">路线列表</h3>
          <a-menu
            mode="inline"
            :selectedKeys="[queryParam.lineId || 'all']"
            @select="handleLineSelect"
          >
            <!-- 全部选项 -->
            <a-menu-item key="all">全部</a-menu-item>
            <!-- 动态路线列表 -->
            <a-menu-item
              v-for="route in routeList"
              :key="route.id"
            >
              {{ route.name }}
            </a-menu-item>
          </a-menu>
        </div>
      </a-col>

      <!-- 右侧表格内容 -->
      <a-col :xs="24" :sm="24" :md="19" :lg="20" :xl="21">
        <!--引用表格-->
        <BasicTable @register="registerTable" :rowSelection="rowSelection">
          <!--插槽:table标题-->
          <template #tableTitle>
            <!-- <a-button type="primary" @click="handleHlsTest" preIcon="ant-design:play-circle-outlined">
              HLS视频测试
            </a-button> -->
            <!-- <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
            <a-button type="primary" color="error" @click="handleDelete" preIcon="ant-design:delete-outlined" :disabled="!hasSelected"> 删除</a-button>
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="batchHandleDelete">
                    <Icon icon="ant-design:delete-outlined"></Icon>
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button>批量操作
                <Icon icon="mdi:chevron-down"></Icon>
              </a-button>
            </a-dropdown> -->
          </template>
          <!--操作栏-->
          <template #action="{ record }">
            <TableAction :actions="getTableAction(record)" />
          </template>
          <!--字段回显插槽-->
          <template #htmlSlot="{text}">
            <div v-html="text"></div>
          </template>
          <!--省市区字段回显插槽-->
          <template #pcaSlot="{text}">
            {{ getAreaTextByCode(text) }}
          </template>
          <template #fileSlot="{text}">
            <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
            <a-button v-else :ghost="true" type="primary" preIcon="ant-design:download-outlined" size="small" @click="downloadFile(text)">下载</a-button>
          </template>
        </BasicTable>
        <!-- 表单区域 -->
        <MasterPlanModal @register="registerModal" @success="handleSuccess"></MasterPlanModal>
        <!-- 巡更记录模态框 -->
        <PatrolRecordsModal ref="patrolRecordsModal"></PatrolRecordsModal>

        <!-- HLS视频测试模态框 -->
        <a-modal
          v-model:open="hlsTestVisible"
          title="HLS视频流测试"
          width="1200px"
          :footer="null"
          :destroyOnClose="true"
        >
          <div class="hls-test-container">
            <!-- 输入区域 -->
            <div class="input-section" style="margin-bottom: 20px;">
              <a-row :gutter="16" align="middle">
                <a-col :span="18">
                  <a-input
                    v-model:value="hlsTestUrl"
                    placeholder="请输入HLS视频流地址，例如: http://example.com/video.m3u8"
                    size="large"
                  />
                </a-col>
                <a-col :span="6">
                  <a-space>
                    <a-button type="primary" @click="startHlsTest" :loading="hlsTestLoading">
                      开始播放
                    </a-button>
                    <a-button @click="stopHlsTest" :disabled="!hlsTestPlaying">
                      停止播放
                    </a-button>
                  </a-space>
                </a-col>
              </a-row>
            </div>

            <!-- 预设地址快捷按钮 -->
            <div class="preset-urls" style="margin-bottom: 20px;">
              <a-space wrap>
                <a-tag color="blue" style="cursor: pointer;" @click="usePresetUrl('http://localhost:8080/jeecgboot/video/hls/test-stream/index.m3u8')">
                  本地测试流
                </a-tag>
                <a-tag color="green" style="cursor: pointer;" @click="usePresetUrl('https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8')">
                  Mux测试流
                </a-tag>
                <a-tag color="orange" style="cursor: pointer;" @click="usePresetUrl('https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/.m3u8')">
                  Demo测试流
                </a-tag>
              </a-space>
            </div>

            <!-- 视频播放区域 -->
            <div class="video-section">
              <div class="video-container" style="background: #000; border-radius: 8px; overflow: hidden;">
                <video
                  ref="hlsTestVideoElement"
                  controls
                  style="width: 100%; height: 500px; object-fit: contain;"
                  @loadstart="onVideoLoadStart"
                  @loadeddata="onVideoLoaded"
                  @error="onVideoError"
                  @play="onVideoPlay"
                  @pause="onVideoPause"
                  @ended="onVideoEnded"
                  @waiting="onVideoWaiting"
                  @canplay="onVideoCanPlay"
                >
                  您的浏览器不支持视频播放
                </video>
              </div>

              <!-- 状态信息 -->
              <div class="status-info" style="margin-top: 16px;">
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-statistic title="播放状态" :value="hlsTestStatus" />
                  </a-col>
                  <a-col :span="8">
                    <a-statistic title="播放时长" :value="hlsTestDuration" />
                  </a-col>
                  <a-col :span="8">
                    <a-statistic title="错误信息" :value="hlsTestError || '无'" />
                  </a-col>
                </a-row>
              </div>
            </div>
          </div>
        </a-modal>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" name="masterPlan-masterPlanList" setup>
  import {ref, computed, reactive, onMounted, onUnmounted} from 'vue';
  import {BasicTable, TableAction} from '/@/components/Table';
  import {useModal} from '/@/components/Modal';
  import {useListPage} from '/@/hooks/system/useListPage';
  import MasterPlanModal from './components/MasterPlanModal.vue'
  import PatrolRecordsModal from './components/PatrolRecordsModal.vue'

  import {columns, searchFormSchema} from './MasterPlan.data';
  import {list, deleteOne, deleteBatch, getImportUrl, getExportUrl, getPatrolConfig} from './MasterPlan.api';
  import {downloadFile} from '/@/utils/common/renderUtils';
  import {getAreaTextByCode} from '/@/components/Form/src/utils/Area';
  import {getDictItems} from '/@/api/common/api';
  import {useMessage} from '/@/hooks/web/useMessage';
  import { getUserManagedLines } from '/@/views/line/Line.api';
  import dayjs from 'dayjs';

  // 导入HLS.js
  import Hls from 'hls.js';

  const [registerModal, {openModal}] = useModal();
  const patrolRecordsModal = ref();
  const {createMessage} = useMessage();

  // 路线相关变量
  const routeList = ref<any[]>([]);
  const queryParam = reactive<any>({
    lineId: null
  });

  // 巡更配置相关变量
  const patrolConfig = ref<any>({
    startTime: '22:00:00', // 默认夜间巡更开始时间
    endTime: '06:00:00'    // 默认夜间巡更结束时间
  });

  // 初始化状态控制
  const isInitialized = ref(false);

  // HLS测试相关变量
  const hlsTestVisible = ref(false);
  const hlsTestUrl = ref('');
  const hlsTestLoading = ref(false);
  const hlsTestPlaying = ref(false);
  const hlsTestStatus = ref('未开始');
  const hlsTestDuration = ref('00:00:00');
  const hlsTestError = ref('');
  const hlsTestVideoElement = ref<HTMLVideoElement>();

  let hlsInstance: Hls | null = null;
  let durationTimer: NodeJS.Timeout | null = null;
  let playStartTime = 0;

  /**
   * 重置事件处理
   */
  const handleReset = async () => {
    try {
      // 重新计算默认时间范围
      const defaultTimeRange = calculateDefaultTimeRange();

      // 直接设置 createTime 参数
      queryParam.createTime_begin = defaultTimeRange[0] + ' 00:00:00';
      queryParam.createTime_end = defaultTimeRange[1] + ' 23:59:59';

      // 使用setTimeout避免阻塞UI线程
      setTimeout(() => {
        try {
          // 获取表单实例并设置默认时间范围
          const form = getForm();
          if (form) {
            form.setFieldsValue({
              timeRange: defaultTimeRange
            });
          }

          // 触发查询
          reload();
        } catch (error) {
          // console.error('设置表单默认值失败:', error);
        }
      }, 50);

    } catch (error) {
      // console.error('重置事件处理失败:', error);
    }
  };

  // 修复 useListPage 参数格式
  const {tableContext} = useListPage({
    tableProps: {
      title: '巡更计划总表管理',
      api: list,
      rowKey: 'id',
      columns,
      canResize: true,
      immediate: false, // 禁止自动查询，等待初始化完成后手动触发
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [],
        baseColProps: { span: 6 },
        resetFunc: handleReset, // 添加重置事件处理
      },
      actionColumn: {
        width: 150,
        fixed: 'right'
      },
      beforeFetch: (params) => {
       
        // 如果还未完成初始化，跳过查询
        if (!isInitialized.value) {
          console.log('⚠️ 初始化未完成，跳过查询');
          return false; // 返回false阻止查询
        }

        // 构建额外的查询参数
        let extraParams: any = {};

        // 处理路线参数
        if (!queryParam.lineId && routeList.value.length > 0) {
          const allLineIds = routeList.value.map(route => route.id);
          // console.log('选择全部，传递路线IDs:', allLineIds);

          // 尝试多种格式以兼容不同的后端实现
          extraParams.lineIds = allLineIds; // 数组格式
          extraParams.lineId = allLineIds.join(','); // 逗号分隔字符串格式
          extraParams['lineId_in'] = allLineIds; // IN查询格式
        } else if (queryParam.lineId) {
          // console.log('选择特定路线:', queryParam.lineId);
          extraParams.lineId = queryParam.lineId;
        }

        // 处理时间范围参数
        // 始终删除 timeRange 参数，不传递给后端
        delete params.timeRange;

        // 优先使用 queryParam 中直接设置的时间参数
        if (queryParam.createTime_begin && queryParam.createTime_end) {
          extraParams.createTime_begin = queryParam.createTime_begin;
          extraParams.createTime_end = queryParam.createTime_end;
        } else {
          // 如果没有设置时间范围，使用默认时间范围
          const defaultTimeRange = calculateDefaultTimeRange();
          extraParams.createTime_begin = defaultTimeRange[0] + ' 00:00:00';
          extraParams.createTime_end = defaultTimeRange[1] + ' 23:59:59';

          console.log('� 使用默认时间范围查询参数:', {
            默认日期范围: defaultTimeRange,
            createTime_begin: extraParams.createTime_begin,
            createTime_end: extraParams.createTime_end
          });
        }

        // 合并参数
        const mergedParams = Object.assign(params, extraParams);
        return mergedParams;
      }
    },
    exportConfig: {
      url: getExportUrl,
      name: "巡更计划总表管理"
    },
    importConfig: {
      url: getImportUrl,
      success: () => {
        reload();
      }
    }
  });

  const [registerTable, {reload, getForm}, {rowSelection, selectedRowKeys}] = tableContext;

  // 计算是否有选中项
  const hasSelected = computed(() => selectedRowKeys.value.length > 0);

  /**
   * 根据巡更配置计算默认查询时间范围
   * 默认时间范围：昨天到今天（只显示日期）
   */
  const calculateDefaultTimeRange = () => {
    const now = dayjs();

    // 昨天日期
    const startDate = now.subtract(1, 'day');
    // 今天日期
    const endDate = now;

    // console.log('计算默认时间范围:', {
    //   startDate: startDate.format('YYYY-MM-DD'),
    //   endDate: endDate.format('YYYY-MM-DD')
    // });

    return [
      startDate.format('YYYY-MM-DD'),
      endDate.format('YYYY-MM-DD')
    ];
  };

  // 初始化路线数据和巡更配置
  onMounted(async () => {
    try {
      // console.log('开始初始化数据...');

      // 1. 获取巡更配置
      try {
        const configResponse = await getPatrolConfig();
        // console.log('巡更配置数据:', configResponse);

        if (configResponse) {
          const config = configResponse; // 直接使用返回的配置对象
          patrolConfig.value = {
            startTime: config.startTime || '22:00:00',
            endTime: config.endTime || '06:00:00'
          };
          // console.log('巡更配置加载完成:', patrolConfig.value);
        }
      } catch (error) {
        // console.error('获取巡更配置失败，使用默认配置:', error);
      }

      // 2. 设置默认时间范围
      const defaultTimeRange = calculateDefaultTimeRange();

      // 设置默认的 createTime 参数
      queryParam.createTime_begin = defaultTimeRange[0] + ' 00:00:00';
      queryParam.createTime_end = defaultTimeRange[1] + ' 23:59:59';

      // 3. 设置表单默认值
      try {
        const form = getForm();
        if (form) {
          await form.setFieldsValue({
            timeRange: defaultTimeRange
          });
          // console.log('表单默认时间范围设置成功');
        }
      } catch (error) {
        // console.error('设置表单默认值失败:', error);
      }

      // console.log('开始获取用户管理的路线数据...');

      // 4. 使用 getUserManagedLines API 获取当前用户管理的路线
      const response = await getUserManagedLines({});
      // console.log('getUserManagedLines API 返回数据:', response);

      if (response && response.success && response.result) {
        // 处理返回的路线数据
        const lines = response.result;
        if (Array.isArray(lines)) {
          routeList.value = lines.map(item => ({
            id: item.id,
            name: item.name || item.lineName
          }));
        } else {
          // console.warn('返回的路线数据不是数组格式:', lines);
          routeList.value = [];
        }
      } else {
        // console.warn('获取路线数据失败或返回格式不正确:', response);
        routeList.value = [];
      }

      // console.log('处理后的路线数据:', routeList.value);

      // 5. 初始化完成，触发首次查询
      isInitialized.value = true;
      // console.log('初始化完成，触发首次查询');
      reload();

      // 6. 添加日期变化监听器
      window.addEventListener('dateRangeChange', (event: any) => {
       // console.log('📅 日期变化事件触发:', event.detail);
        if (isInitialized.value) {
          //console.log('🔄 日期变化，准备触发查询...');

          // 直接从事件中获取日期并赋值给查询参数
          if (event.detail && event.detail.dateStrings && event.detail.dateStrings.length === 2) {
            const [startDate, endDate] = event.detail.dateStrings;

            // 直接设置查询参数
            queryParam.createTime_begin = startDate + ' 00:00:00';
            queryParam.createTime_end = endDate + ' 23:59:59';

           
          }

          setTimeout(() => {
           
            reload();
          }, 200); // 延迟200ms确保表单值已更新
        } 
      });

    } catch (error) {
      // console.error('获取用户管理的路线失败:', error);
      routeList.value = [];
    }
  });

  /**
   * 路线选择事件处理
   */
  const handleLineSelect = ({ key }: { key: string }) => {
    // console.log('路线选择事件:', key);
    // console.log('当前路线列表:', routeList.value);
    // console.log('初始化状态:', isInitialized.value);

    if (key === 'all') {
      // 选择"全部"时，清空单个路线ID，让beforeFetch处理多路线查询
      queryParam.lineId = null;
      // console.log('选择全部，清空 lineId');
    } else {
      // 选择特定路线时，设置单个路线ID
      queryParam.lineId = key;
      // console.log('选择特定路线:', key);
    }

    // console.log('更新后的 queryParam:', queryParam);

    // 只有在初始化完成后才触发查询
    if (isInitialized.value) {
      reload(); // 触发查询
    } else {
      // console.log('初始化未完成，暂不触发查询');
    }
  };

  /**
   * 新增事件
   */
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
    });
  }
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
    });
  }
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: false,
    });
  }
  /**
   * 删除事件
   */
  async function handleDelete(record?: Recordable) {
    await deleteOne({ id: record?.id || selectedRowKeys.value[0] }, handleSuccess);
  }
  /**
   * 批量删除事件
   */
  function batchHandleDelete() {
    deleteBatch({ ids: selectedRowKeys.value }, handleSuccess);
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
  /**
   * 查看巡更记录
   */
  function handleViewPatrolRecords(record: Recordable) {
    console.log('点击查看巡更记录按钮，记录数据:', record);

    try {
      // 检查组件引用是否存在
      if (!patrolRecordsModal.value) {
        console.error('patrolRecordsModal组件引用不存在');
        createMessage.error('巡更记录组件未正确加载');
        return;
      }

      // 检查showModal方法是否存在
      if (typeof patrolRecordsModal.value.showModal !== 'function') {
        console.error('patrolRecordsModal.showModal方法不存在');
        createMessage.error('巡更记录组件方法不可用');
        return;
      }

      console.log('准备调用showModal方法');
      patrolRecordsModal.value.showModal(record);
      console.log('已调用showModal方法');

    } catch (error) {
      console.error('调用showModal失败:', error);
      createMessage.error('打开巡更记录失败，请稍后重试');
    }
  }

  /**
   * 操作栏
   */
  function getTableAction(record: Recordable) {
    return [
      {
        label: '查看巡更记录',
        onClick: handleViewPatrolRecords.bind(null, record),
        icon: 'mdi:clipboard-text-outline'
      }
    ]
  }
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record: Recordable) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        icon: 'mdi:pencil-outline'
      },
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
        icon: 'mdi:eye-outline'
      }, {
        label: '删除',
        color: 'error' as const,
        onClick: handleDelete.bind(null, record),
        icon: 'mdi:delete-outline'
      }
    ]
  }

  // ==================== HLS测试相关函数 ====================

  /**
   * 打开HLS测试模态框
   */
  function handleHlsTest() {
    hlsTestVisible.value = true;
    hlsTestUrl.value = '';
    hlsTestStatus.value = '未开始';
    hlsTestError.value = '';
    hlsTestDuration.value = '00:00:00';
  }

  /**
   * 使用预设URL
   */
  function usePresetUrl(url: string) {
    hlsTestUrl.value = url;
  }

  /**
   * 开始HLS测试
   */
  async function startHlsTest() {
    if (!hlsTestUrl.value.trim()) {
      createMessage.warning('请输入HLS视频流地址');
      return;
    }

    if (!hlsTestVideoElement.value) {
      createMessage.error('视频元素未找到');
      return;
    }

    hlsTestLoading.value = true;
    hlsTestError.value = '';
    hlsTestStatus.value = '正在加载...';

    try {
      // 停止之前的播放
      await stopHlsTest();

      // 检查HLS支持
      if (!Hls.isSupported()) {
        // 尝试原生播放
        if (hlsTestVideoElement.value.canPlayType('application/vnd.apple.mpegurl')) {
          hlsTestVideoElement.value.src = hlsTestUrl.value;
          await hlsTestVideoElement.value.play();
          hlsTestStatus.value = '播放中';
          hlsTestPlaying.value = true;
          startDurationTimer();
        } else {
          throw new Error('浏览器不支持HLS播放');
        }
      } else {
        // 使用HLS.js
        hlsInstance = new Hls({
          debug: false,
          enableWorker: false,
          lowLatencyMode: false,
          maxBufferLength: 30,
          maxMaxBufferLength: 60,
          fragLoadingTimeOut: 30000,
          manifestLoadingTimeOut: 20000,
          enableSoftwareAES: true
        });

        hlsInstance.loadSource(hlsTestUrl.value);
        hlsInstance.attachMedia(hlsTestVideoElement.value);

        hlsInstance.on(Hls.Events.MANIFEST_PARSED, () => {
          console.log('HLS清单解析完成');
          hlsTestVideoElement.value?.play().then(() => {
            hlsTestStatus.value = '播放中';
            hlsTestPlaying.value = true;
            startDurationTimer();
            console.log('HLS播放成功');
          }).catch(error => {
            console.error('HLS播放失败:', error);
            hlsTestError.value = '播放失败: ' + error.message;
            hlsTestStatus.value = '播放失败';
          });
        });

        hlsInstance.on(Hls.Events.ERROR, (event, data) => {
          console.error('HLS错误:', data);
          hlsTestError.value = `${data.type}: ${data.details}`;
          hlsTestStatus.value = '播放错误';

          if (data.fatal) {
            switch (data.type) {
              case Hls.ErrorTypes.NETWORK_ERROR:
                hlsTestError.value = '网络错误: ' + data.details;
                break;
              case Hls.ErrorTypes.MEDIA_ERROR:
                hlsTestError.value = '媒体错误: ' + data.details;
                break;
              default:
                hlsTestError.value = '未知错误: ' + data.details;
                break;
            }
          }
        });
      }
    } catch (error: any) {
      console.error('启动HLS测试失败:', error);
      hlsTestError.value = error.message || '启动失败';
      hlsTestStatus.value = '启动失败';
    } finally {
      hlsTestLoading.value = false;
    }
  }

  /**
   * 停止HLS测试
   */
  async function stopHlsTest() {
    hlsTestPlaying.value = false;
    hlsTestStatus.value = '已停止';

    // 停止计时器
    if (durationTimer) {
      clearInterval(durationTimer);
      durationTimer = null;
    }

    // 停止视频播放
    if (hlsTestVideoElement.value) {
      hlsTestVideoElement.value.pause();
      hlsTestVideoElement.value.src = '';
    }

    // 销毁HLS实例
    if (hlsInstance) {
      hlsInstance.destroy();
      hlsInstance = null;
    }
  }

  /**
   * 开始播放时长计时
   */
  function startDurationTimer() {
    playStartTime = Date.now();
    if (durationTimer) {
      clearInterval(durationTimer);
    }

    durationTimer = setInterval(() => {
      const elapsed = Date.now() - playStartTime;
      const hours = Math.floor(elapsed / 3600000);
      const minutes = Math.floor((elapsed % 3600000) / 60000);
      const seconds = Math.floor((elapsed % 60000) / 1000);

      hlsTestDuration.value = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }, 1000);
  }

  // ==================== 视频事件处理函数 ====================

  function onVideoLoadStart() {
    console.log('视频开始加载');
    hlsTestStatus.value = '加载中...';
  }

  function onVideoLoaded() {
    console.log('视频数据加载完成');
    hlsTestStatus.value = '已加载';
  }

  function onVideoError(event: Event) {
    console.error('视频播放错误:', event);
    hlsTestError.value = '视频播放错误';
    hlsTestStatus.value = '播放错误';
  }

  function onVideoPlay() {
    console.log('视频开始播放');
    hlsTestStatus.value = '播放中';
    hlsTestPlaying.value = true;
    if (!durationTimer) {
      startDurationTimer();
    }
  }

  function onVideoPause() {
    console.log('视频暂停');
    hlsTestStatus.value = '已暂停';
  }

  function onVideoEnded() {
    console.log('视频播放结束');
    hlsTestStatus.value = '播放完成';
    hlsTestPlaying.value = false;
    if (durationTimer) {
      clearInterval(durationTimer);
      durationTimer = null;
    }
  }

  function onVideoWaiting() {
    console.log('视频等待数据...');
    hlsTestStatus.value = '缓冲中...';
  }

  function onVideoCanPlay() {
    console.log('视频可以播放');
    if (hlsTestStatus.value === '缓冲中...') {
      hlsTestStatus.value = '播放中';
    }
  }

  // ==================== 组件生命周期 ====================

  onUnmounted(() => {
    // 清理HLS测试资源
    stopHlsTest();
  });
</script>

<style scoped>
/* 简洁的数据显示样式 */
:deep(.tech-data-container) {
  transition: all 0.2s ease !important;
}

:deep(.tech-data-container:hover) {
  opacity: 0.8;
}

/* 表格行悬停效果保持简洁 */
:deep(.ant-table-tbody > tr:hover > td) {
  background: rgba(24, 144, 255, 0.02) !important;
}

/* 操作列样式优化 */
:deep(.ant-table-tbody .ant-table-cell:last-child) {
  padding-right: 16px !important;
  white-space: nowrap !important;
}

/* 下拉菜单样式优化 */
:deep(.ant-dropdown-menu) {
  min-width: 120px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 确保操作按钮不会溢出 */
:deep(.ant-table-tbody .ant-table-cell:last-child .ant-btn-group) {
  display: flex !important;
  gap: 8px !important;
}

/* 表格单元格文本溢出处理 */
:deep(.ant-table-tbody .ant-table-cell) {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 左侧路线列表样式 */
.route-list-container {
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  height: calc(100vh - 100px);
  overflow-y: auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background: #fff;
}

.route-list-container h3 {
  color: #262626;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 20px;
}

.route-list-container :deep(.ant-menu) {
  border-right: none;
}

.route-list-container :deep(.ant-menu-item) {
  padding: 8px 12px;
  border-radius: 4px;
  margin: 4px 0;
  transition: all 0.2s;
}

.route-list-container :deep(.ant-menu-item:hover) {
  background-color: #f5f7fa;
}

.route-list-container :deep(.ant-menu-item-selected) {
  background-color: #e6f4ff;
  color: #1677ff;
  font-weight: 500;
}

/* 自定义滚动条样式 */
.route-list-container::-webkit-scrollbar {
  width: 6px;
}

.route-list-container::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.route-list-container::-webkit-scrollbar-track {
  background: #f5f5f5;
}

/* HLS测试相关样式 */
.hls-test-container {
  padding: 16px;
}

.hls-test-container .input-section {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.hls-test-container .preset-urls {
  padding: 12px 16px;
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.hls-test-container .video-container {
  position: relative;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.hls-test-container .video-container video {
  display: block;
  background: #000;
}

.hls-test-container .status-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.hls-test-container .status-info :deep(.ant-statistic-title) {
  color: #666;
  font-size: 14px;
}

.hls-test-container .status-info :deep(.ant-statistic-content) {
  color: #333;
  font-size: 16px;
  font-weight: 500;
}
</style>
