<template>
  <div class="video-monitor-test">
    <a-card title="视频监控测试页面" style="margin: 20px;">
      <div class="test-controls">
        <a-space>
          <a-button type="primary" @click="loadTestData">加载测试数据</a-button>
          <a-button @click="clearData">清空数据</a-button>
          <a-switch v-model:checked="showDebugInfo" checked-children="调试" un-checked-children="正常" />
        </a-space>
      </div>
      
      <a-divider />
      
      <!-- 调试信息 -->
      <div v-if="showDebugInfo" class="debug-info">
        <a-alert 
          message="调试信息" 
          :description="`视频数量: ${videoList.length}, HLS支持: ${hlsSupported}, 错误: ${errorMessage}`"
          type="info" 
          show-icon 
          style="margin-bottom: 16px"
        />
      </div>
      
      <!-- 错误信息 -->
      <a-alert 
        v-if="errorMessage" 
        :message="errorMessage" 
        type="error" 
        closable 
        @close="errorMessage = ''"
        style="margin-bottom: 16px"
      />
      
      <!-- 视频监控组件 -->
      <div class="video-monitor-container">
        <PlanVideoMonitorModal
          :plan-info="planInfo"
          :video-list="videoList"
          :default-grid-layout="4"
          @error="onVideoError"
        />
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import PlanVideoMonitorModal from './PlanVideoMonitorModal.vue';
import Hls from 'hls.js';

// 响应式数据
const showDebugInfo = ref(true);
const errorMessage = ref('');

// 测试数据
const planInfo = ref({
  id: 'test-plan-001',
  name: '测试巡更计划'
});

const videoList = ref<any[]>([]);

// 计算属性
const hlsSupported = computed(() => Hls.isSupported());

/**
 * 加载测试数据
 */
function loadTestData() {
  try {
    videoList.value = [
      {
        id: 'video-001',
        name: '测试摄像头1',
        videoUrl: 'http://***********:554/openUrl/3whQVPi.m3u8?beginTime=20170615T000000&endTime=20170617T000000&playBackMode=1',
        streamId: 'test_stream_001',
        websocketUrl: '/websocket/video/test_stream_001',
        hlsUrl: '/jeecgboot/api/video/hls/test_stream_001/index.m3u8',
        streamType: 'preview'
      },
      {
        id: 'video-002',
        name: '测试摄像头2',
        videoUrl: 'http://***********:554/openUrl/4whQVPi.m3u8?beginTime=20170615T000000&endTime=20170617T000000&playBackMode=1',
        streamId: 'test_stream_002',
        websocketUrl: '/websocket/video/test_stream_002',
        hlsUrl: '/jeecgboot/api/video/hls/test_stream_002/index.m3u8',
        streamType: 'playback'
      },
      {
        id: 'video-003',
        name: '测试摄像头3',
        videoUrl: 'rtsp://admin:admin123@192.168.1.100:554/h264/ch1/main/av_stream',
        streamId: 'test_stream_003',
        websocketUrl: '/websocket/video/test_stream_003',
        hlsUrl: '/jeecgboot/api/video/hls/test_stream_003/index.m3u8',
        streamType: 'preview'
      },
      {
        id: 'video-004',
        name: '测试摄像头4',
        videoUrl: 'rtsp://admin:admin123@192.168.1.101:554/h264/ch1/main/av_stream',
        streamId: 'test_stream_004',
        websocketUrl: '/websocket/video/test_stream_004',
        hlsUrl: '/jeecgboot/api/video/hls/test_stream_004/index.m3u8',
        streamType: 'preview'
      }
    ];
    
    message.success(`已加载 ${videoList.value.length} 个测试视频`);
    errorMessage.value = '';
  } catch (error) {
    console.error('加载测试数据失败:', error);
    errorMessage.value = '加载测试数据失败';
  }
}

/**
 * 清空数据
 */
function clearData() {
  videoList.value = [];
  errorMessage.value = '';
  message.info('已清空数据');
}

/**
 * 处理视频错误
 */
function onVideoError(error: any) {
  console.error('视频监控错误:', error);
  errorMessage.value = error.message || '视频监控发生错误';
}

// 初始化时加载测试数据
loadTestData();
</script>

<style scoped>
.video-monitor-test {
  min-height: 100vh;
  background: #f5f5f5;
}

.test-controls {
  margin-bottom: 16px;
}

.debug-info {
  margin-bottom: 16px;
}

.video-monitor-container {
  min-height: 500px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
