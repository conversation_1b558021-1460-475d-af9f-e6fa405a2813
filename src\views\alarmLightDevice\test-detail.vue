<template>
  <div class="test-container">
    <a-card title="报警灯设备详情页面测试">
      <a-space>
        <a-button type="primary" @click="showOnlineDevice">
          查看在线设备详情
        </a-button>
        <a-button @click="showOfflineDevice">
          查看离线设备详情
        </a-button>
        <a-button @click="showErrorDevice">
          查看故障设备详情
        </a-button>
      </a-space>
    </a-card>

    <!-- 详情模态框 -->
    <AlarmLightDeviceDetailModal ref="detailModal" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import AlarmLightDeviceDetailModal from './components/AlarmLightDeviceDetailModal.vue';

const detailModal = ref();

// 测试数据 - 在线设备
const onlineDevice = {
  id: '1',
  deviceName: '监区A-报警灯-001',
  model: 'AL-2024-Pro',
  status: '1',
  snCode: 'SN202401001',
  ipAddress: '***********00',
  gatewary: '***********',
  subnetMask: '*************',
  dns: '*******',
  clientId: 'alarm_light_001',
  username: 'device_user',
  sendTopic: 'alarm/send/001',
  receiveTopic: 'alarm/receive/001',
  volume: 8,
  image: 'https://via.placeholder.com/400x300/3b82f6/ffffff?text=报警灯设备',
  createUserName: '系统管理员',
  createTime: '2024-01-15 10:30:00',
  updateTime: '2024-01-20 14:20:00'
};

// 测试数据 - 离线设备
const offlineDevice = {
  id: '2',
  deviceName: '监区B-报警灯-002',
  model: 'AL-2024-Standard',
  status: '0',
  snCode: 'SN202401002',
  ipAddress: '***********01',
  gatewary: '***********',
  subnetMask: '*************',
  dns: '*******',
  volume: 5,
  createUserName: '设备管理员',
  createTime: '2024-01-10 09:15:00'
};

// 测试数据 - 故障设备
const errorDevice = {
  id: '3',
  deviceName: '监区C-报警灯-003',
  model: 'AL-2024-Advanced',
  status: '2',
  snCode: 'SN202401003',
  ipAddress: '***********02',
  gatewary: '***********',
  subnetMask: '*************',
  dns: '*******',
  clientId: 'alarm_light_003',
  username: 'device_user',
  sendTopic: 'alarm/send/003',
  receiveTopic: 'alarm/receive/003',
  volume: 3,
  image: 'https://via.placeholder.com/400x300/ef4444/ffffff?text=故障设备',
  createUserName: '技术人员',
  createTime: '2024-01-05 16:45:00',
  updateTime: '2024-01-22 11:30:00'
};

function showOnlineDevice() {
  detailModal.value.showModal(onlineDevice);
}

function showOfflineDevice() {
  detailModal.value.showModal(offlineDevice);
}

function showErrorDevice() {
  detailModal.value.showModal(errorDevice);
}
</script>

<style scoped lang="less">
.test-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}
</style>
