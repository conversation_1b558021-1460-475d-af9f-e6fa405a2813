<template>
  <a-modal
    v-model:open="visible"
    :title="null"
    :footer="null"
    :closable="false"
    :mask-closable="false"
    :width="400"
    :centered="true"
    :z-index="9999"
    class="alarm-light-modal"
    :body-style="{ padding: 0 }"
  >
    <div class="alarm-container">
      <!-- 报警灯效果 -->
      <div class="alarm-light-wrapper">
        <div class="alarm-light" :class="{ 'flashing': isFlashing }">
          <div class="light-core"></div>
          <div class="light-ring ring-1"></div>
          <div class="light-ring ring-2"></div>
          <div class="light-ring ring-3"></div>
        </div>
      </div>

      <!-- 报警信息 -->
      <div class="alarm-content">
        <div class="alarm-title">
          <Icon icon="ant-design:warning-filled" class="alarm-icon" />
          <span>紧急报警</span>
        </div>
        
        <div class="alarm-message">
          {{ alarmData?.msgTxt || '系统检测到异常情况' }}
        </div>
        
        <div class="alarm-description" v-if="alarmData?.msgDesc">
          {{ alarmData.msgDesc }}
        </div>
        
        <div class="alarm-time">
          {{ formatTime(alarmData?.timestamp || new Date()) }}
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="alarm-actions">
        <a-button
          type="primary"
          danger
          size="large"
          @click="handleConfirm"
          class="confirm-btn"
        >
          <Icon icon="ant-design:check-circle-outlined" />
          确认处理
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { Modal, Button } from 'ant-design-vue';
import { Icon } from '/@/components/Icon';
import { formatToDateTime } from '/@/utils/dateUtil';

interface AlarmData {
  msgTxt?: string;
  msgDesc?: string;
  timestamp?: Date | string;
  [key: string]: any;
}

interface Props {
  open: boolean;
  alarmData?: AlarmData;
}

const props = withDefaults(defineProps<Props>(), {
  open: false,
  alarmData: () => ({})
});

const emit = defineEmits<{
  'update:open': [value: boolean];
  'confirm': [data: AlarmData];
  'close': [];
}>();

const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
});

const isFlashing = ref(false);
let flashTimer: NodeJS.Timeout | null = null;

// 格式化时间
const formatTime = (time: Date | string) => {
  if (!time) return '';
  return formatToDateTime(time);
};

// 开始闪烁效果
const startFlashing = () => {
  isFlashing.value = true;
};

// 停止闪烁效果
const stopFlashing = () => {
  isFlashing.value = false;
  if (flashTimer) {
    clearTimeout(flashTimer);
    flashTimer = null;
  }
};

// 确认处理
const handleConfirm = () => {
  emit('confirm', props.alarmData);
  stopFlashing();
  visible.value = false;
};

// 监听弹窗打开状态
watch(() => props.open, (newVal) => {
  if (newVal) {
    startFlashing();
  } else {
    stopFlashing();
  }
});

onMounted(() => {
  if (props.open) {
    startFlashing();
  }
});

onUnmounted(() => {
  stopFlashing();
});
</script>

<style lang="less" scoped>
.alarm-light-modal {
  :deep(.ant-modal-content) {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    border: 2px solid #ff4757;
    border-radius: 16px;
    box-shadow: 
      0 0 30px rgba(255, 71, 87, 0.5),
      0 0 60px rgba(255, 71, 87, 0.3),
      inset 0 0 20px rgba(255, 71, 87, 0.1);
    overflow: hidden;
  }
}

.alarm-container {
  padding: 30px;
  text-align: center;
  color: #fff;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 20%, rgba(255, 71, 87, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(255, 107, 107, 0.1) 0%, transparent 50%);
    pointer-events: none;
  }
}

.alarm-light-wrapper {
  margin-bottom: 25px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
}

.alarm-light {
  position: relative;
  width: 80px;
  height: 80px;
  
  &.flashing {
    .light-core {
      animation: alarmFlash 0.8s ease-in-out infinite;
    }
    
    .light-ring {
      animation: ringPulse 1.2s ease-in-out infinite;
      
      &.ring-1 {
        animation-delay: 0s;
      }
      
      &.ring-2 {
        animation-delay: 0.2s;
      }
      
      &.ring-3 {
        animation-delay: 0.4s;
      }
    }
  }
}

.light-core {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, #ff4757 0%, #ff3742 50%, #e84118 100%);
  border-radius: 50%;
  box-shadow: 
    0 0 20px rgba(255, 71, 87, 0.8),
    0 0 40px rgba(255, 71, 87, 0.6),
    inset 0 0 10px rgba(255, 255, 255, 0.3);
}

.light-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid rgba(255, 71, 87, 0.6);
  border-radius: 50%;
  
  &.ring-1 {
    width: 80px;
    height: 80px;
  }
  
  &.ring-2 {
    width: 100px;
    height: 100px;
  }
  
  &.ring-3 {
    width: 120px;
    height: 120px;
  }
}

.alarm-content {
  margin-bottom: 25px;
}

.alarm-title {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  color: #ff4757;
  margin-bottom: 15px;
  text-shadow: 0 0 10px rgba(255, 71, 87, 0.8);
  
  .alarm-icon {
    font-size: 24px;
    margin-right: 8px;
    animation: iconBlink 1s ease-in-out infinite;
  }
}

.alarm-message {
  font-size: 16px;
  color: #fff;
  margin-bottom: 10px;
  line-height: 1.5;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

.alarm-description {
  font-size: 14px;
  color: #ddd;
  margin-bottom: 15px;
  line-height: 1.4;
}

.alarm-time {
  font-size: 12px;
  color: #aaa;
  font-family: 'Courier New', monospace;
}

.alarm-actions {
  display: flex;
  justify-content: center;

  .ant-btn {
    height: 45px;
    padding: 0 30px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;

    &.confirm-btn {
      background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
      border: none;
      box-shadow: 0 4px 15px rgba(255, 71, 87, 0.4);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 71, 87, 0.6);
      }
    }
  }
}

// 动画效果
@keyframes alarmFlash {
  0%, 100% {
    background: radial-gradient(circle, #ff4757 0%, #ff3742 50%, #e84118 100%);
    box-shadow: 
      0 0 20px rgba(255, 71, 87, 0.8),
      0 0 40px rgba(255, 71, 87, 0.6),
      inset 0 0 10px rgba(255, 255, 255, 0.3);
  }
  50% {
    background: radial-gradient(circle, #ff6b7a 0%, #ff5722 50%, #d32f2f 100%);
    box-shadow: 
      0 0 30px rgba(255, 71, 87, 1),
      0 0 60px rgba(255, 71, 87, 0.8),
      inset 0 0 15px rgba(255, 255, 255, 0.5);
  }
}

@keyframes ringPulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0;
  }
}

@keyframes iconBlink {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}
</style>
